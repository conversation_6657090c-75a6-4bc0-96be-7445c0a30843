<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.ciai.dal.mysql.standardmixratio.StandardMixRatioMapper">


    <select id="getSpecsMapping"
            resultType="cn.iocoder.yudao.module.ciai.controller.admin.standardmixratio.vo.JsSpecsVO">
        SELECT
            id,
            material_type,
            specification,
            origin,
            alkali_content,
            chloride_ion_content
        FROM
            ciai_wz_material_spec
        WHERE
            deleted = 0
        <if test="id != 0">
            AND raw_material_type_id = #{id}
        </if>
    </select>
</mapper>