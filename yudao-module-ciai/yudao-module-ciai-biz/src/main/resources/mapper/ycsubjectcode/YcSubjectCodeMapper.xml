<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.ciai.dal.mysql.ycsubjectcode.YcSubjectCodeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="findByAccountingSubjectCodeStartingWith"
            resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.ycsubjectcode.YcSubjectCodeDO">
        select *
        from ciai_yc_subject_code
        where accounting_subject_code like CONCAT(#{accountingSubjectCode}, '%')
    </select>
    <select id="findFirstLevelSubjects"
            resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.ycsubjectcode.YcSubjectCodeDO">
        SELECT *
        FROM ciai_yc_subject_code
        WHERE LENGTH(accounting_subject_code) = 4
        ORDER BY accounting_subject_code
    </select>
    <select id="selectCreditSubjectIdByBusinessType" resultType="java.lang.Long">
        SELECT id
        FROM ciai_yc_subject_code
        WHERE deleted = 0
          AND subject_name = #{expenseType}
            LIMIT 1 -- 确保只返回一个，如果配置可能重复，需要业务上保证唯一性
    </select>
</mapper>