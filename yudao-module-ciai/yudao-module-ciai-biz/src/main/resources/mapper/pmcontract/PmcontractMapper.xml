<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.ciai.dal.mysql.pmcontract.PmcontractMapper">

    <select id="selectPages" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.pmcontract.PmcontractDO">
        SELECT
        p.id,
        c.internal_contract_number AS internalContractNumber,
        c.contract_name AS contractName,
        c.transport_distance AS transportDistance,
        p.project_name AS projectName,
        i.company_name AS companyName,
        p.project_address AS projectAddress,
        p.id AS projectId
        FROM
        ciai_mm_sale_contract_project p
        LEFT JOIN  ciai_mm_sale_contract c ON c.id = p.sales_contract_id
        LEFT JOIN ciai_mm_unit_info i ON c.construction_unit_id = i.id
        <where>
            <if test="reqVo.internalContractNumber != null and reqVo.internalContractNumber !=''">
                AND c.internal_contract_number LIKE CONCAT('%',#{reqVo.internalContractNumber},'%')
            </if>
            <if test="reqVo.contractName != null and reqVo.contractName !=''">
                AND c.contract_name LIKE CONCAT('%',#{reqVo.contractName},'%')
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>