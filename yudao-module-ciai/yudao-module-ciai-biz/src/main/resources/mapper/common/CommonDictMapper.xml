<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--suppress ALL -->
<mapper namespace="cn.iocoder.yudao.module.ciai.dal.mysql.common.CommonDictMapper">

    <!-- 安全的通用字典选项查询 -->
    <select id="selectDictOptions" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO">
        /* 通用字典查询 */
        SELECT
            ${valueColumn} AS value,
            ${labelColumn} AS label
        FROM ${tableName}
        <if test="whereCondition != null and whereCondition != ''">
            WHERE ${whereCondition}
        </if>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
    </select>
    
    <!-- 备用的通用字典选项查询方法 -->
    <select id="queryDictOptions" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO">
        SELECT
            ${vc} AS value,
            ${lc} AS label
        FROM ${tn}
        <if test="wc != null and wc != ''">
            WHERE ${wc}
        </if>
        <if test="ob != null and ob != ''">
            ORDER BY ${ob}
        </if>
    </select>


    <select id="selectListByDeptId" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO">
        WITH RECURSIVE parent_dept AS (
            -- 获取目标部门的父部门ID，并动态判断是否顶层
            SELECT
                id AS original_dept_id,
                parent_id,
                -- 关键逻辑：如果父部门是顶层（parent_id=0），则根节点为目标部门自身
                CASE
                    WHEN parent_id = 0 THEN id
                    ELSE parent_id
                    END AS root_dept_id
            FROM system_dept
            WHERE id = #{deptId}
              AND deleted = false
        ), sub_depts AS (
            -- 递归查询从根节点（root_dept_id）开始的所有子部门
            SELECT id
            FROM system_dept
            WHERE id = (SELECT root_dept_id FROM parent_dept)
            UNION ALL
            SELECT d.id
            FROM system_dept d
                     INNER JOIN sub_depts sd ON d.parent_id = sd.id
            WHERE d.deleted = false
        )
-- 最终查询：排除根节点自身（如果需要保留根节点部门下的员工，移除WHERE条件）
        SELECT u.nickname AS label, u.id AS value
        FROM system_users u
            JOIN sub_depts sd ON u.dept_id = sd.id
        WHERE u.deleted = false
          AND sd.id != (SELECT root_dept_id FROM parent_dept);  -- 是否排除根节点部门自身
    </select>

    <select id="selectListByParentId" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO">
        WITH RECURSIVE target_parent AS (
            -- 1. 获取目标部门及其父部门信息，并动态确定根节点
            SELECT
                id AS original_dept_id,
                parent_id,
                CASE
                    WHEN parent_id = 0 THEN id      -- 如果父部门是顶层，根节点为自身
                    ELSE parent_id                  -- 否则根节点为父部门ID
                    END AS root_dept_id
            FROM system_dept
            WHERE id = #{deptId}
              AND deleted = false
        ), sub_depts AS (
            -- 2. 从根节点开始递归查询所有子部门
            SELECT id, name, parent_id
            FROM system_dept
            WHERE id = (SELECT root_dept_id FROM target_parent)
            UNION ALL
            SELECT d.id, d.name, d.parent_id
            FROM system_dept d
                     INNER JOIN sub_depts sd ON d.parent_id = sd.id
            WHERE d.deleted = false
        )
-- 3. 排除根节点自身，返回所有子部门
        SELECT id as value,name as label
        FROM sub_depts
        WHERE id != (SELECT root_dept_id FROM target_parent);
    </select>


    
</mapper> 