<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.ciai.dal.mysql.wzsupplierquotation.WzSupplierQuotationMapper">
    <insert id="updatePrice">
        insert into ciai_wz_material_pricing (material_info_id,material_unit_price,transport_unit_price,start_date,enabled) values (#{materialId},#{unitPrice},#{transportUnitPrice},#{startDate},1)
    </insert>
    <insert id="insert1">
        insert into ciai_wz_supplier_quotation
            (material_id,unit_price,delivery_days,delivery_volume,quality_report_url,creator)
            values (#{supplierContractId},#{unitPrice},#{deliveryDays},#{deliveryVolume},#{qualityReportUrl},#{userId})
    </insert>
    <update id="updatePricePlanStatus1">
        update ciai_wz_purchase_plan set status1 = #{stopPlan} where id = #{id}
    </update>
    <update id="updateQuotationStatus">
        update ciai_wz_supplier_quotation set status = 2 where id=#{quotationId}
    </update>

    <!-- cn/iocoder/yudao/module/ciai/dal/dataobject/wzpurchasecontract/WzContractMaterialJoinDO.java-->
     <select id="selectPurchasePlanByUserId"  resultType= "cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplierquotation.WzSupplierQuotationDO">
         select sq.id, sq.unit_price, sq.delivery_days, sq.quality_report_url, sq.create_time, sq.update_time, sq.deleted, sq.delivery_volume, mt.type_name, cm.id as material_id,
                ms.material_type as material_type, ms.specification as material_spec,sq.status as status
         from ciai_wz_supplier_quotation sq
                  left join ciai_wz_contract_material cm on sq.material_id=cm.id
                  join ciai_wz_material_spec ms on cm.material_type_id = ms.id
                  join ciai_wz_material_type mt on ms.raw_material_type_id=mt.id
                  join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
                  join ciai_wz_supplier ws on pc.signing_unit_id = ws.id
         where ws.login_account=#{userId}
           and (#{typeName} is null or #{typeName} = '' or mt.type_name like concat('%', #{typeName}, '%'))
           and (#{unitPrice} is null or #{unitPrice} = '' or sq.unit_price = #{unitPrice});
     </select>
    <select id="selectComprise" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplierquotation.WzSupplierQuotationDO">
        select
            cm.id as material_id,
            mt.type_name,
            ms.material_type as material_type,
            ms.specification as material_spec,
            s.unit_name as supplier_name,
            sq.id as quotationId,
            sq.unit_price,
            sq.delivery_days,
            sq.quality_report_url,
            sq.create_time,
            sq.update_time,
            sq.deleted,
            sq.delivery_volume,
            sq.status
        from ciai_wz_contract_material cm
        join ciai_wz_purchase_contract pc on cm.purchase_contract_id = pc.id
        join ciai_wz_supplier s on pc.signing_unit_id = s.id
        left join ciai_wz_material_spec ms on cm.material_type_id = ms.id
        left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
        left join ciai_wz_supplier_quotation sq on sq.material_id = cm.id
        where cm.deleted = 0 and sq.unit_price is not null and sq.status=1
          and pc.deleted = 0
          and s.deleted = 0
          and (#{typeName} is null or #{typeName} = '' or mt.type_name = #{typeName})
          and (#{materialType} is null or #{materialType} = '' or ms.material_type = #{materialType})
          and (#{materialSpec} is null or #{materialSpec} = '' or ms.specification = #{materialSpec})
          and (
            (#{minPrice} is null or #{maxPrice} is null or (#{minPrice} = 0 and #{maxPrice} = 0))
                or
            (sq.unit_price BETWEEN #{minPrice} and #{maxPrice})
            )
        limit #{offset},#{pageSize}
    </select>
    <select id="selectPurchasePlan" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanDO">
        select pp.id as id,pp.supplier_contract_id as supplier_contract_id,pp.planned_purchase_quantity as planned_purchase_quantity,pp.planned_purchase_date as planned_purchase_date,
               mt.type_name as type_name,ms.material_type as material_type,ms.specification as material_spec,pp.status1 as status1
        from ciai_wz_purchase_plan pp
                 left join ciai_wz_contract_material cm on pp.supplier_contract_id=cm.id
                 left join ciai_wz_material_spec ms on cm.material_type_id=ms.id
                 left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
                 left join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
                 left join ciai_wz_supplier ws on pc.signing_unit_id = ws.id
        where (#{typeName} is null or #{typeName} = '' or mt.type_name = #{typeName})
        limit #{offset}, #{pageSize}
    </select>
    <select id="getCount" resultType="java.lang.Long">
        select count(*)
        from ciai_wz_contract_material cm
        join ciai_wz_purchase_contract pc on cm.purchase_contract_id = pc.id
        join ciai_wz_supplier s on pc.signing_unit_id = s.id
        left join ciai_wz_material_spec ms on cm.material_type_id = ms.id
        left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
        left join ciai_wz_supplier_quotation sq on sq.material_id = cm.id
        where cm.deleted = 0
          and pc.deleted = 0
          and s.deleted = 0
          and cm.deleted = 0
          and sq.unit_price is not null
          and pc.deleted = 0
          and s.deleted = 0
          and (#{typeName} is null or #{typeName} = '' or mt.type_name = #{typeName})
          and (#{materialType} is null or #{materialType} = '' or ms.material_type = #{materialType})
          and (#{materialSpec} is null or #{materialSpec} = '' or ms.specification = #{materialSpec})
          and (
            (#{minPrice} is null or #{maxPrice} is null or (#{minPrice} = 0 AND #{maxPrice} = 0))
           or
            (sq.unit_price between #{minPrice} and #{maxPrice})
            )
    </select>
    <select id="getCount1" resultType="java.lang.Long">
        select count(*)
            from ciai_wz_purchase_plan pp
                 left join ciai_wz_contract_material cm on pp.supplier_contract_id=cm.id
                 left join ciai_wz_material_spec ms on cm.material_type_id=ms.id
                 left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
                 left join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
                 left join ciai_wz_supplier ws on pc.signing_unit_id = ws.id
            where (#{typeName} IS NULL OR #{typeName} = '' or mt.type_name = #{typeName})
    </select>
    <select id="selectPurchasePlanBySupplier" resultType="cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanDO">
        select pp.id as id,pp.supplier_contract_id as supplier_contract_id,pp.planned_purchase_quantity as planned_purchase_quantity,pp.planned_purchase_date as planned_purchase_date,
               mt.type_name as type_name,ms.material_type as material_type,ms.specification as material_spec
        from ciai_wz_purchase_plan pp
                 left join ciai_wz_contract_material cm on pp.supplier_contract_id=cm.id
                 left join ciai_wz_material_spec ms on cm.material_type_id=ms.id
                 left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
                 left join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
                 left join ciai_wz_supplier ws on pc.signing_unit_id = ws.id
        where ws.login_account = #{userId}
          and (#{typeName} is null or #{typeName} = '' or mt.type_name like concat('%', #{typeName}, '%')) limit #{offset}, #{pageSize}
    </select>
    <select id="getCount2" resultType="java.lang.Long">
        select count(*)
        from ciai_wz_purchase_plan pp
                 left join ciai_wz_contract_material cm on pp.supplier_contract_id=cm.id
                 left join ciai_wz_material_spec ms on cm.material_type_id=ms.id
                 left join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
                 left join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
                 left join ciai_wz_supplier ws on pc.signing_unit_id = ws.id
        where ws.login_account = #{userId}
    </select>
    <select id="selectPriceTrend" resultType="cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierquotation.vo.PriceTrendRespVO">
        select date_format(planned_purchase_date, '%m月') as month,avg(unit_price) as avg_price from (select pp.planned_purchase_date as planned_purchase_date,
              mt.type_name as type_name,ms.material_type as material_type,ms.specification as material_spec
        from ciai_wz_purchase_plan pp
        join ciai_wz_contract_material cm on pp.supplier_contract_id=cm.id
        join ciai_wz_material_spec ms on cm.material_type_id=ms.id
        join ciai_wz_material_type mt on ms.raw_material_type_id = mt.id
        join ciai_wz_purchase_contract pc on cm.purchase_contract_id=pc.id
        join ciai_wz_supplier ws on pc.signing_unit_id = ws.id) as ll
        join (select
        mt.type_name,
        ms.material_type as material_type,
        ms.specification as material_spec,
        sq.unit_price
      from ciai_wz_contract_material cm
             join ciai_wz_purchase_contract pc ON cm.purchase_contract_id = pc.id
             join ciai_wz_supplier s ON pc.signing_unit_id = s.id
             left join ciai_wz_material_spec ms ON cm.material_type_id = ms.id
             left join ciai_wz_material_type mt ON ms.raw_material_type_id = mt.id
             left join ciai_wz_supplier_quotation sq ON sq.material_id = cm.id
        where
             cm.deleted = 0 and sq.unit_price is not null) as lt on ll.type_name=lt.type_name
        where ll.material_type=lt.material_type and ll.material_spec=lt.material_spec and ll.type_name=#{typeName}
        group by
            date_format(planned_purchase_date, '%m月')
        ORDER BY
            month

    </select>


</mapper>