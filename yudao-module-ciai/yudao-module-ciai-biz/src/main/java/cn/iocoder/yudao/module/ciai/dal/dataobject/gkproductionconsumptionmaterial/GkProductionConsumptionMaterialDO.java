package cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionconsumptionmaterial;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 生产消耗原材料 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_gk_production_consumption_material")
@KeySequence("ciai_gk_production_consumption_material_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GkProductionConsumptionMaterialDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 编号
     */
    private Integer no;
    /**
     * 关联生产消耗ID
     */
    private Long scId;
    /**
     * 生产时间
     */
    private LocalDateTime productionTime;
    /**
     * 生产主机
     */
    private Long productionHost;
    /**
     * 仓位编码
     */
    private Long storageLocation;
    /**
     * 仓位名称
     */
    private String storageLocationName;
    /**
     * 仓位别名
     */
    private String storageLocationAlias;
    /**
     * 建委材料代码
     */
    private Integer constructionMaterialCode;
    /**
     * 材料名称
     */
    private String materialName;
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    /**
     * 理论值
     */
    private BigDecimal theoreticalValue;
    /**
     * 实际值
     */
    private BigDecimal actualValue;
    /**
     * 误差值
     */
    private BigDecimal errorValue;
    /**
     * 含水量
     */
    private BigDecimal moistureContent;
    /**
     * 是否更新单价
     */
    private Integer isUnitPriceUpdated;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 理论值2
     */
    private BigDecimal theoreticalValue2;
    /**
     * 实际值2
     */
    private BigDecimal actualValue2;
    /**
     * 启用状态
     */
    private Integer enabled;

}