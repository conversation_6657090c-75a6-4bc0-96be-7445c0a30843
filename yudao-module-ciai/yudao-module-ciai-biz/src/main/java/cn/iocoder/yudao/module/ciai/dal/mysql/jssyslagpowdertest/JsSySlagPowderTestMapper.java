package cn.iocoder.yudao.module.ciai.dal.mysql.jssyslagpowdertest;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssyslagpowdertest.JsSySlagPowderTestDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.jssyslagpowdertest.vo.*;

/**
 * 矿粉试验 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JsSySlagPowderTestMapper extends BaseMapperX<JsSySlagPowderTestDO> {

    default PageResult<JsSySlagPowderTestDO> selectPage(JsSySlagPowderTestPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JsSySlagPowderTestDO>()
                .eqIfPresent(JsSySlagPowderTestDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(JsSySlagPowderTestDO::getTestNo, reqVO.getTestNo())
                .eqIfPresent(JsSySlagPowderTestDO::getEntrustNo, reqVO.getEntrustNo())
                .eqIfPresent(JsSySlagPowderTestDO::getSampleNo, reqVO.getSampleNo())
                .eqIfPresent(JsSySlagPowderTestDO::getEntrustUnit, reqVO.getEntrustUnit())
                .eqIfPresent(JsSySlagPowderTestDO::getTestClient, reqVO.getTestClient())
                .eqIfPresent(JsSySlagPowderTestDO::getResponsiblePerson, reqVO.getResponsiblePerson())
                .eqIfPresent(JsSySlagPowderTestDO::getReviewer, reqVO.getReviewer())
                .eqIfPresent(JsSySlagPowderTestDO::getTester, reqVO.getTester())
                .likeIfPresent(JsSySlagPowderTestDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(JsSySlagPowderTestDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(JsSySlagPowderTestDO::getType, reqVO.getType())
                .eqIfPresent(JsSySlagPowderTestDO::getSpecification, reqVO.getSpecification())
                .eqIfPresent(JsSySlagPowderTestDO::getFactoryNo, reqVO.getFactoryNo())
                .betweenIfPresent(JsSySlagPowderTestDO::getFactoryDate, reqVO.getFactoryDate())
                .betweenIfPresent(JsSySlagPowderTestDO::getEntryDate, reqVO.getEntryDate())
                .eqIfPresent(JsSySlagPowderTestDO::getRepresentQuantity, reqVO.getRepresentQuantity())
                .betweenIfPresent(JsSySlagPowderTestDO::getSampleDate, reqVO.getSampleDate())
                .betweenIfPresent(JsSySlagPowderTestDO::getFormingDate, reqVO.getFormingDate())
                .eqIfPresent(JsSySlagPowderTestDO::getFineness, reqVO.getFineness())
                .eqIfPresent(JsSySlagPowderTestDO::getDensity, reqVO.getDensity())
                .eqIfPresent(JsSySlagPowderTestDO::getSpecificSurfaceArea, reqVO.getSpecificSurfaceArea())
                .eqIfPresent(JsSySlagPowderTestDO::getFlowDegree, reqVO.getFlowDegree())
                .eqIfPresent(JsSySlagPowderTestDO::getLossOnIgnition, reqVO.getLossOnIgnition())
                .eqIfPresent(JsSySlagPowderTestDO::getActivityIndex3, reqVO.getActivityIndex3())
                .eqIfPresent(JsSySlagPowderTestDO::getActivityIndex7, reqVO.getActivityIndex7())
                .eqIfPresent(JsSySlagPowderTestDO::getActivityIndex28, reqVO.getActivityIndex28())
                .eqIfPresent(JsSySlagPowderTestDO::getActivityIndexFast, reqVO.getActivityIndexFast())
                .eqIfPresent(JsSySlagPowderTestDO::getConclusion, reqVO.getConclusion())
                .eqIfPresent(JsSySlagPowderTestDO::getTestStatus7d, reqVO.getTestStatus7d())
                .eqIfPresent(JsSySlagPowderTestDO::getTestStatus28d, reqVO.getTestStatus28d())
                .eqIfPresent(JsSySlagPowderTestDO::getTestDate7d, reqVO.getTestDate7d())
                .eqIfPresent(JsSySlagPowderTestDO::getTestDate28d, reqVO.getTestDate28d())
                .betweenIfPresent(JsSySlagPowderTestDO::getTestDate, reqVO.getTestDate())
                .betweenIfPresent(JsSySlagPowderTestDO::getDeadlineDate, reqVO.getDeadlineDate())
                .likeIfPresent(JsSySlagPowderTestDO::getName, reqVO.getName())
                .eqIfPresent(JsSySlagPowderTestDO::getMoistureContent, reqVO.getMoistureContent())
                .eqIfPresent(JsSySlagPowderTestDO::getReportDate7d, reqVO.getReportDate7d())
                .eqIfPresent(JsSySlagPowderTestDO::getReportDate28d, reqVO.getReportDate28d())
                .betweenIfPresent(JsSySlagPowderTestDO::getReportDate, reqVO.getReportDate())
                .eqIfPresent(JsSySlagPowderTestDO::getAlkaliContent, reqVO.getAlkaliContent())
                .eqIfPresent(JsSySlagPowderTestDO::getChlorideIon, reqVO.getChlorideIon())
                .eqIfPresent(JsSySlagPowderTestDO::getAttachmentStatus, reqVO.getAttachmentStatus())
                .eqIfPresent(JsSySlagPowderTestDO::getModifier, reqVO.getModifier())
                .betweenIfPresent(JsSySlagPowderTestDO::getModifyTime, reqVO.getModifyTime())
                .eqIfPresent(JsSySlagPowderTestDO::getHs11, reqVO.getHs11())
                .eqIfPresent(JsSySlagPowderTestDO::getHs12, reqVO.getHs12())
                .eqIfPresent(JsSySlagPowderTestDO::getHsL1, reqVO.getHsL1())
                .eqIfPresent(JsSySlagPowderTestDO::getHs21, reqVO.getHs21())
                .eqIfPresent(JsSySlagPowderTestDO::getHs22, reqVO.getHs22())
                .eqIfPresent(JsSySlagPowderTestDO::getHsL2, reqVO.getHsL2())
                .eqIfPresent(JsSySlagPowderTestDO::getHsLv, reqVO.getHsLv())
                .eqIfPresent(JsSySlagPowderTestDO::getMd11, reqVO.getMd11())
                .eqIfPresent(JsSySlagPowderTestDO::getMd12, reqVO.getMd12())
                .eqIfPresent(JsSySlagPowderTestDO::getMd13, reqVO.getMd13())
                .eqIfPresent(JsSySlagPowderTestDO::getMd1, reqVO.getMd1())
                .eqIfPresent(JsSySlagPowderTestDO::getMd21, reqVO.getMd21())
                .eqIfPresent(JsSySlagPowderTestDO::getMd22, reqVO.getMd22())
                .eqIfPresent(JsSySlagPowderTestDO::getMd23, reqVO.getMd23())
                .eqIfPresent(JsSySlagPowderTestDO::getMd2, reqVO.getMd2())
                .eqIfPresent(JsSySlagPowderTestDO::getMdV, reqVO.getMdV())
                .eqIfPresent(JsSySlagPowderTestDO::getSs11, reqVO.getSs11())
                .eqIfPresent(JsSySlagPowderTestDO::getSs12, reqVO.getSs12())
                .eqIfPresent(JsSySlagPowderTestDO::getSsL1, reqVO.getSsL1())
                .eqIfPresent(JsSySlagPowderTestDO::getSs21, reqVO.getSs21())
                .eqIfPresent(JsSySlagPowderTestDO::getSs22, reqVO.getSs22())
                .eqIfPresent(JsSySlagPowderTestDO::getSsL2, reqVO.getSsL2())
                .eqIfPresent(JsSySlagPowderTestDO::getSsLv, reqVO.getSsLv())
                .eqIfPresent(JsSySlagPowderTestDO::getLd11, reqVO.getLd11())
                .eqIfPresent(JsSySlagPowderTestDO::getLd12, reqVO.getLd12())
                .eqIfPresent(JsSySlagPowderTestDO::getLdD1, reqVO.getLdD1())
                .eqIfPresent(JsSySlagPowderTestDO::getLd21, reqVO.getLd21())
                .eqIfPresent(JsSySlagPowderTestDO::getLd22, reqVO.getLd22())
                .eqIfPresent(JsSySlagPowderTestDO::getLdD2, reqVO.getLdD2())
                .eqIfPresent(JsSySlagPowderTestDO::getLdDv, reqVO.getLdDv())
                .eqIfPresent(JsSySlagPowderTestDO::getBb11, reqVO.getBb11())
                .eqIfPresent(JsSySlagPowderTestDO::getBb12, reqVO.getBb12())
                .eqIfPresent(JsSySlagPowderTestDO::getBb13, reqVO.getBb13())
                .eqIfPresent(JsSySlagPowderTestDO::getBb14, reqVO.getBb14())
                .eqIfPresent(JsSySlagPowderTestDO::getBb15, reqVO.getBb15())
                .eqIfPresent(JsSySlagPowderTestDO::getBb16, reqVO.getBb16())
                .eqIfPresent(JsSySlagPowderTestDO::getBb17, reqVO.getBb17())
                .eqIfPresent(JsSySlagPowderTestDO::getBb18, reqVO.getBb18())
                .eqIfPresent(JsSySlagPowderTestDO::getBb19, reqVO.getBb19())
                .eqIfPresent(JsSySlagPowderTestDO::getBbMj1, reqVO.getBbMj1())
                .eqIfPresent(JsSySlagPowderTestDO::getBb21, reqVO.getBb21())
                .eqIfPresent(JsSySlagPowderTestDO::getBb22, reqVO.getBb22())
                .eqIfPresent(JsSySlagPowderTestDO::getBb23, reqVO.getBb23())
                .eqIfPresent(JsSySlagPowderTestDO::getBb24, reqVO.getBb24())
                .eqIfPresent(JsSySlagPowderTestDO::getBb25, reqVO.getBb25())
                .eqIfPresent(JsSySlagPowderTestDO::getBb26, reqVO.getBb26())
                .eqIfPresent(JsSySlagPowderTestDO::getBb27, reqVO.getBb27())
                .eqIfPresent(JsSySlagPowderTestDO::getBb28, reqVO.getBb28())
                .eqIfPresent(JsSySlagPowderTestDO::getBb29, reqVO.getBb29())
                .eqIfPresent(JsSySlagPowderTestDO::getBbMj2, reqVO.getBbMj2())
                .eqIfPresent(JsSySlagPowderTestDO::getBbMjv, reqVO.getBbMjv())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural71, reqVO.getFlexural71())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural72, reqVO.getFlexural72())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural73, reqVO.getFlexural73())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural7v, reqVO.getFlexural7v())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural71, reqVO.getBaseFlexural71())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural72, reqVO.getBaseFlexural72())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural73, reqVO.getBaseFlexural73())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural7v, reqVO.getBaseFlexural7v())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural281, reqVO.getFlexural281())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural282, reqVO.getFlexural282())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural283, reqVO.getFlexural283())
                .eqIfPresent(JsSySlagPowderTestDO::getFlexural28v, reqVO.getFlexural28v())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural281, reqVO.getBaseFlexural281())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural282, reqVO.getBaseFlexural282())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural283, reqVO.getBaseFlexural283())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseFlexural28v, reqVO.getBaseFlexural28v())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad71, reqVO.getLoad71())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad72, reqVO.getLoad72())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad73, reqVO.getLoad73())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad74, reqVO.getLoad74())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad75, reqVO.getLoad75())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad76, reqVO.getLoad76())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive71, reqVO.getCompressive71())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive72, reqVO.getCompressive72())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive73, reqVO.getCompressive73())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive74, reqVO.getCompressive74())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive75, reqVO.getCompressive75())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive76, reqVO.getCompressive76())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive7v, reqVO.getCompressive7v())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad71, reqVO.getBaseLoad71())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad72, reqVO.getBaseLoad72())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad73, reqVO.getBaseLoad73())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad74, reqVO.getBaseLoad74())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad75, reqVO.getBaseLoad75())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad76, reqVO.getBaseLoad76())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive71, reqVO.getBaseCompressive71())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive72, reqVO.getBaseCompressive72())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive73, reqVO.getBaseCompressive73())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive74, reqVO.getBaseCompressive74())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive75, reqVO.getBaseCompressive75())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive76, reqVO.getBaseCompressive76())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive7v, reqVO.getBaseCompressive7v())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad281, reqVO.getBaseLoad281())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad282, reqVO.getBaseLoad282())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad283, reqVO.getBaseLoad283())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad284, reqVO.getBaseLoad284())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad285, reqVO.getBaseLoad285())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseLoad286, reqVO.getBaseLoad286())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad281, reqVO.getLoad281())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad282, reqVO.getLoad282())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad283, reqVO.getLoad283())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad284, reqVO.getLoad284())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad285, reqVO.getLoad285())
                .eqIfPresent(JsSySlagPowderTestDO::getLoad286, reqVO.getLoad286())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive281, reqVO.getCompressive281())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive282, reqVO.getCompressive282())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive283, reqVO.getCompressive283())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive284, reqVO.getCompressive284())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive285, reqVO.getCompressive285())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive286, reqVO.getCompressive286())
                .eqIfPresent(JsSySlagPowderTestDO::getCompressive28v, reqVO.getCompressive28v())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive281, reqVO.getBaseCompressive281())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive282, reqVO.getBaseCompressive282())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive283, reqVO.getBaseCompressive283())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive284, reqVO.getBaseCompressive284())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive285, reqVO.getBaseCompressive285())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive286, reqVO.getBaseCompressive286())
                .eqIfPresent(JsSySlagPowderTestDO::getBaseCompressive28v, reqVO.getBaseCompressive28v())
                .eqIfPresent(JsSySlagPowderTestDO::getStandard, reqVO.getStandard())
                .eqIfPresent(JsSySlagPowderTestDO::getTestStatus, reqVO.getTestStatus())
                .eqIfPresent(JsSySlagPowderTestDO::getUsageStatus, reqVO.getUsageStatus())
                .eqIfPresent(JsSySlagPowderTestDO::getSelected, reqVO.getSelected())
                .eqIfPresent(JsSySlagPowderTestDO::getPrinted, reqVO.getPrinted())
                .eqIfPresent(JsSySlagPowderTestDO::getYValue, reqVO.getYValue())
                .eqIfPresent(JsSySlagPowderTestDO::getStationCode, reqVO.getStationCode())
                .eqIfPresent(JsSySlagPowderTestDO::getTester2, reqVO.getTester2())
                .eqIfPresent(JsSySlagPowderTestDO::getStandard2, reqVO.getStandard2())
                .eqIfPresent(JsSySlagPowderTestDO::getProjectType, reqVO.getProjectType())
                .eqIfPresent(JsSySlagPowderTestDO::getFactoryBrand, reqVO.getFactoryBrand())
                .eqIfPresent(JsSySlagPowderTestDO::getCnsj1, reqVO.getCnsj1())
                .eqIfPresent(JsSySlagPowderTestDO::getCnsj2, reqVO.getCnsj2())
                .eqIfPresent(JsSySlagPowderTestDO::getCnsjB, reqVO.getCnsjB())
                .eqIfPresent(JsSySlagPowderTestDO::getInitialSettingTimeRatio, reqVO.getInitialSettingTimeRatio())
                .betweenIfPresent(JsSySlagPowderTestDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JsSySlagPowderTestDO::getId));
    }

}