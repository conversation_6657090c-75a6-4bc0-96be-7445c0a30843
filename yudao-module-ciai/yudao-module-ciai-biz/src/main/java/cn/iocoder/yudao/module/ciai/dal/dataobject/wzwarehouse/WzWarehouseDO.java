package cn.iocoder.yudao.module.ciai.dal.dataobject.wzwarehouse;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物资管理料仓管理 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_wz_warehouse")
@KeySequence("ciai_wz_warehouse_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WzWarehouseDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 料仓名称
     */
    private String warehouseName;
    /**
     * 别名
     */
    private String alias;
    /**
     * 原材种类ID
     */
    private Long rawMaterialTypeId;
    /**
     * 最高库存量
     */
    private Double maxInventory;
    /**
     * 最低库存量
     */
    private Double minInventory;
    /**
     * 安全库存
     */
    private Double safetyInventory;
    /**
     * 盘点值
     */
    private Double inventoryCheckValue;
    /**
     * 盘点时间
     */
    private LocalDateTime inventoryCheckTime;
    /**
     * 进料量
     */
    private Double intakeQuantity;
    /**
     * 消耗量
     */
    private Double consumptionQuantity;
    /**
     * 所属公司ID
     */
    private Long companyId;
    /**
     * 生产机组ID
     */
    private String productionUnitId;
    /**
     * 计算库存
     */
    private Boolean calculateInventory;
    /**
     * 料位仪库存
     */
    private Double levelGaugeInventory;
    /**
     * 工控机编号
     */
    private String controlMachineNumber;
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    /**
     * 作废
     */
    private Boolean isVoid;

}