package cn.iocoder.yudao.module.ciai.controller.admin.pmcontract.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 营销管理销售合同分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PmcontractPageReqVO extends PageParam {

    @Schema(description = "内部合同编号")
    private String internalContractNumber;

    @Schema(description = "合同名称")
    private String contractName;

}