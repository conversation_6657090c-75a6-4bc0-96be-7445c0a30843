package cn.iocoder.yudao.module.ciai.controller.admin.ycexpenseclaim.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 费用报销分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class YcExpenseClaimPageReqVO extends PageParam {

    @Schema(description = "报销单号")
    private String claimNo;

    @Schema(description = "报销类型", example = "1")
    private String expenseType;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applicationTime;

    @Schema(description = "用户ID", example = "2486")
    private Long userId;

    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @Schema(description = "提交人")
    private String submitter;

    @Schema(description = "部门ID", example = "12877")
    private Long depdId;

    @Schema(description = "报销部门")
    private String department;

    @Schema(description = "票据金额汇总/元")
    private BigDecimal totalAmount;

    @Schema(description = "票据金额大写")
    private String amountInWords;

    @Schema(description = "报销状态（如：待审核、已批准等）", example = "1")
    private Integer status;

    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "审核人名")
    @ExcelProperty("审核人名")
    private String auditorName;

}