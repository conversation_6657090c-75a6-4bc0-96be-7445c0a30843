package cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontract.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 营销管理销售合同 Response VO")
@Data
@ExcelIgnoreUnannotated
@NoArgsConstructor
@AllArgsConstructor
public class MmSaleContractDetailRespVO extends MmSaleContractRespVO{
    @Schema(description = "ID")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "施工单位ID")
    @ExcelProperty("施工单位名称")
    private String companyName;


}