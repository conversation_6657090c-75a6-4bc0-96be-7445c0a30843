package cn.iocoder.yudao.module.ciai.dal.dataobject.wzwarehouse;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物资管理料仓管理盘点记录 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_wz_warehouse_check")
@KeySequence("ciai_wz_warehouse_check_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WzWarehouseCheckDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 料仓ID
     */
    private Long warehouseId;
    /**
     * 盘点值
     */
    private String inventoryCheckValue;
    /**
     * 盘点类型
     */
    private String inventoryCheckType;
    /**
     * 盘点时间
     */
    private LocalDateTime inventoryCheckTime;
    /**
     * 盘点结果
     */
    private String inventoryCheckResult;
    /**
     * 损耗值
     */
    private Double lossValue;

}