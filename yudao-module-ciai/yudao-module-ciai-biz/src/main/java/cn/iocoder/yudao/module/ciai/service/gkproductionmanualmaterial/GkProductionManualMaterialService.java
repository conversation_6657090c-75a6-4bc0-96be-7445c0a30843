package cn.iocoder.yudao.module.ciai.service.gkproductionmanualmaterial;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualmaterial.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionmanualmaterial.GkProductionManualMaterialDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 生产手动消耗原材料 Service 接口
 *
 * <AUTHOR>
 */
public interface GkProductionManualMaterialService {

    /**
     * 创建生产手动消耗原材料
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGkProductionManualMaterial(@Valid GkProductionManualMaterialSaveReqVO createReqVO);

    /**
     * 更新生产手动消耗原材料
     *
     * @param updateReqVO 更新信息
     */
    void updateGkProductionManualMaterial(@Valid GkProductionManualMaterialSaveReqVO updateReqVO);

    /**
     * 删除生产手动消耗原材料
     *
     * @param id 编号
     */
    void deleteGkProductionManualMaterial(Long id);

    /**
     * 获得生产手动消耗原材料
     *
     * @param id 编号
     * @return 生产手动消耗原材料
     */
    GkProductionManualMaterialDO getGkProductionManualMaterial(Long id);

    /**
     * 获得生产手动消耗原材料分页
     *
     * @param pageReqVO 分页查询
     * @return 生产手动消耗原材料分页
     */
    PageResult<GkProductionManualMaterialDO> getGkProductionManualMaterialPage(GkProductionManualMaterialPageReqVO pageReqVO);

}