package cn.iocoder.yudao.module.ciai.gkjinterface.dal;

import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.TaskOrder;
import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.TaskOrderVO;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TaskOrderMapper {

    @Master
    TaskOrderVO selectData(@Param("id") Long id);

    @Select("SELECT COUNT(*) FROM trwd WHERE fRwno = #{fRwno}")
    boolean exists(@Param("fRwno") String fRwno);

    // #{fRwno},
    @Insert("INSERT INTO trwd (fRwno, FPhbNo, fHtno, fHtdw, fGcmc, fJzbw, fJzfs, fGcdz, " +
            "fTpz, fGls,fTld, fTbj, FJhrq,fJhsl, fXdrw, FCzy, FDlrq, fZt, fScbt) VALUES (" +
            "#{order.fRwno}, #{order.fRwno}, #{order.fHtno}, #{order.fHtdw}, #{order.fGcmc}, #{order.fJzbw}, #{order.fJzfs}, #{order.fGcdz}, " +
            "#{order.fTpz}, #{order.fGls}, #{order.fTld}, #{order.fTbj}, #{order.fJhrq}, #{order.fJhsl}, #{order.fXdrw}, #{order.fXdrw}, #{order.fJhrq}, #{order.fZt}, '*')")
    int insert(@Param("order") TaskOrder order);

    // FPhbNo=#{FPhbNo}
    @Update("UPDATE trwd SET FPhbNo=#{order.fRwno}, fHtno=#{order.fHtno}, fHtdw=#{order.fHtdw}, fGcmc=#{order.fGcmc}, fJzbw=#{order.fJzbw}, " +
            "fJzfs=#{order.fJzfs}, fTpz=#{order.fTpz}, fTbj=#{order.fTbj}, fGcdz=#{order.fGcdz}, fTld=#{order.fTld}, fGls=#{order.fGls}, fJhsl=#{order.fJhsl} " +
            "WHERE fRwno=#{order.fRwno}")
    int update(@Param("order") TaskOrder order);
}
