package cn.iocoder.yudao.module.ciai.controller.admin.jsfactorydataselection.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 技术管理_出厂资料选用 Response VO")
@Data
@ExcelIgnoreUnannotated
public class JsFactoryDataSelectionRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17739")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "任务计划ID", example = "21249")
    @ExcelProperty("任务计划ID")
    private Long taskPlanId;

    @Schema(description = "配合比库ID", example = "20322")
    @ExcelProperty("配合比库ID")
    private Long mixLibraryId;

    @Schema(description = "配合比编号")
    @ExcelProperty("配合比编号")
    private String mixNo;

    @Schema(description = "鉴定编号")
    @ExcelProperty("鉴定编号")
    private String identificationNo;

    @Schema(description = "试验委托人")
    @ExcelProperty("试验委托人")
    private String testClient;

    @Schema(description = "标号")
    @ExcelProperty("标号")
    private String markNo;

    @Schema(description = "强度等级")
    @ExcelProperty("强度等级")
    private String strengthGrade;

    @Schema(description = "抗渗等级")
    @ExcelProperty("抗渗等级")
    private String impermeabilityGrade;

    @Schema(description = "抗冻等级")
    @ExcelProperty("抗冻等级")
    private String frostResistanceGrade;

    @Schema(description = "石子种类", example = "2")
    @ExcelProperty("石子种类")
    private String stoneType;

    @Schema(description = "其他技术要求")
    @ExcelProperty("其他技术要求")
    private String otherTechnicalRequirements;

    @Schema(description = "施工单位名称", example = "李四")
    @ExcelProperty("施工单位名称")
    private String constructionUnitName;

    @Schema(description = "工程名称", example = "李四")
    @ExcelProperty("工程名称")
    private String projectName;

    @Schema(description = "施工部位")
    @ExcelProperty("施工部位")
    private String constructionPosition;

    @Schema(description = "要求坍落度")
    @ExcelProperty("要求坍落度")
    private String requiredSlump;

    @Schema(description = "实测坍落度")
    @ExcelProperty("实测坍落度")
    private String measuredSlump;

    @Schema(description = "计划方量")
    @ExcelProperty("计划方量")
    private Double plannedQuantity;

    @Schema(description = "申请日期")
    @ExcelProperty("申请日期")
    private LocalDateTime applicationDate;

    @Schema(description = "报告日期")
    @ExcelProperty("报告日期")
    private LocalDateTime reportDate;

    @Schema(description = "使用日期")
    @ExcelProperty("使用日期")
    private LocalDateTime useDate;

    @Schema(description = "鉴定时间")
    @ExcelProperty("鉴定时间")
    private LocalDateTime identificationTime;

    @Schema(description = "技术负责人")
    @ExcelProperty("技术负责人")
    private String technicalDirector;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String reviewer;

    @Schema(description = "试验人")
    @ExcelProperty("试验人")
    private String tester;

    @Schema(description = "质检员")
    @ExcelProperty("质检员")
    private String qualityInspector;

    @Schema(description = "是否冬施")
    @ExcelProperty("是否冬施")
    private Boolean isWinterConstruction;

    @Schema(description = "每盘方量")
    @ExcelProperty("每盘方量")
    private Double perBatchQuantity;

    @Schema(description = "是否取样")
    @ExcelProperty("是否取样")
    private Boolean isSampling;

    @Schema(description = "配合比比例")
    @ExcelProperty("配合比比例")
    private String mixRatio;

    @Schema(description = "是否水利工程")
    @ExcelProperty("是否水利工程")
    private Boolean isWaterProject;

    @Schema(description = "砂1含石1")
    @ExcelProperty("砂1含石1")
    private Double sand1ContainStone1;

    @Schema(description = "砂1含石2")
    @ExcelProperty("砂1含石2")
    private Double sand1ContainStone2;

    @Schema(description = "砂2含石1")
    @ExcelProperty("砂2含石1")
    private Double sand2ContainStone1;

    @Schema(description = "砂2含石2")
    @ExcelProperty("砂2含石2")
    private Double sand2ContainStone2;

    @Schema(description = "砂1含水")
    @ExcelProperty("砂1含水")
    private Double sand1WaterContent;

    @Schema(description = "砂2含水")
    @ExcelProperty("砂2含水")
    private Double sand2WaterContent;

    @Schema(description = "石1含水")
    @ExcelProperty("石1含水")
    private Double stone1WaterContent;

    @Schema(description = "石2含水")
    @ExcelProperty("石2含水")
    private Double stone2WaterContent;

    @Schema(description = "容重")
    @ExcelProperty("容重")
    private Double bulkDensity;

    @Schema(description = "水泥用量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("水泥用量")
    private BigDecimal cementAmount;

    @Schema(description = "水1用量")
    @ExcelProperty("水1用量")
    private BigDecimal water1Amount;

    @Schema(description = "水2用量")
    @ExcelProperty("水2用量")
    private BigDecimal water2Amount;

    @Schema(description = "石1用量")
    @ExcelProperty("石1用量")
    private BigDecimal stone1Amount;

    @Schema(description = "石2用量")
    @ExcelProperty("石2用量")
    private BigDecimal stone2Amount;

    @Schema(description = "砂1用量")
    @ExcelProperty("砂1用量")
    private BigDecimal sand1Amount;

    @Schema(description = "砂2用量")
    @ExcelProperty("砂2用量")
    private BigDecimal sand2Amount;

    @Schema(description = "掺合料1用量")
    @ExcelProperty("掺合料1用量")
    private BigDecimal admixture1Amount;

    @Schema(description = "掺合料2用量")
    @ExcelProperty("掺合料2用量")
    private BigDecimal admixture2Amount;

    @Schema(description = "掺合料3用量")
    @ExcelProperty("掺合料3用量")
    private BigDecimal admixture3Amount;

    @Schema(description = "外加剂1用量")
    @ExcelProperty("外加剂1用量")
    private BigDecimal additive1Amount;

    @Schema(description = "外加剂2用量")
    @ExcelProperty("外加剂2用量")
    private BigDecimal additive2Amount;

    @Schema(description = "外加剂3用量")
    @ExcelProperty("外加剂3用量")
    private BigDecimal additive3Amount;

    @Schema(description = "膨胀剂用量")
    @ExcelProperty("膨胀剂用量")
    private BigDecimal expansionAgentAmount;

    @Schema(description = "其他用量")
    @ExcelProperty("其他用量")
    private BigDecimal otherAmount;

    @Schema(description = "干料水泥用量")
    @ExcelProperty("干料水泥用量")
    private BigDecimal dryCementAmount;

    @Schema(description = "干料水1用量")
    @ExcelProperty("干料水1用量")
    private BigDecimal dryWater1Amount;

    @Schema(description = "干料水2用量")
    @ExcelProperty("干料水2用量")
    private BigDecimal dryWater2Amount;

    @Schema(description = "干料石1用量")
    @ExcelProperty("干料石1用量")
    private BigDecimal dryStone1Amount;

    @Schema(description = "干料石2用量")
    @ExcelProperty("干料石2用量")
    private BigDecimal dryStone2Amount;

    @Schema(description = "干料砂1用量")
    @ExcelProperty("干料砂1用量")
    private BigDecimal drySand1Amount;

    @Schema(description = "干料砂2用量")
    @ExcelProperty("干料砂2用量")
    private BigDecimal drySand2Amount;

    @Schema(description = "干料掺合料1用量")
    @ExcelProperty("干料掺合料1用量")
    private BigDecimal dryAdmixture1Amount;

    @Schema(description = "干料掺合料2用量")
    @ExcelProperty("干料掺合料2用量")
    private BigDecimal dryAdmixture2Amount;

    @Schema(description = "干料掺合料3用量")
    @ExcelProperty("干料掺合料3用量")
    private BigDecimal dryAdmixture3Amount;

    @Schema(description = "干料外加剂1用量")
    @ExcelProperty("干料外加剂1用量")
    private BigDecimal dryAdditive1Amount;

    @Schema(description = "干料外加剂2用量")
    @ExcelProperty("干料外加剂2用量")
    private BigDecimal dryAdditive2Amount;

    @Schema(description = "干料外加剂3用量")
    @ExcelProperty("干料外加剂3用量")
    private BigDecimal dryAdditive3Amount;

    @Schema(description = "干料膨胀剂用量")
    @ExcelProperty("干料膨胀剂用量")
    private BigDecimal dryExpansionAgentAmount;

    @Schema(description = "干料其他用量")
    @ExcelProperty("干料其他用量")
    private BigDecimal dryOtherAmount;

    @Schema(description = "搅拌时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("搅拌时间")
    private Integer mixingTime;

    @Schema(description = "强度3天")
    @ExcelProperty("强度3天")
    private BigDecimal strength3d;

    @Schema(description = "强度7天")
    @ExcelProperty("强度7天")
    private BigDecimal strength7d;

    @Schema(description = "强度28天")
    @ExcelProperty("强度28天")
    private BigDecimal strength28d;

    @Schema(description = "水1种类_ID", example = "14682")
    @ExcelProperty("水1种类_ID")
    private Long water1TypeId;

    @Schema(description = "水1种类", example = "1")
    @ExcelProperty("水1种类")
    private String water1Type;

    @Schema(description = "水2种类_ID", example = "96")
    @ExcelProperty("水2种类_ID")
    private Long water2TypeId;

    @Schema(description = "水2种类", example = "2")
    @ExcelProperty("水2种类")
    private String water2Type;

    @Schema(description = "水泥种类_ID", example = "26042")
    @ExcelProperty("水泥种类_ID")
    private Long cementTypeId;

    @Schema(description = "水泥种类", example = "1")
    @ExcelProperty("水泥种类")
    private String cementType;

    @Schema(description = "水泥规格")
    @ExcelProperty("水泥规格")
    private String cementSpec;

    @Schema(description = "水泥产地")
    @ExcelProperty("水泥产地")
    private String cementOrigin;

    @Schema(description = "砂1种类_ID", example = "11431")
    @ExcelProperty("砂1种类_ID")
    private Long sand1TypeId;

    @Schema(description = "砂1种类", example = "1")
    @ExcelProperty("砂1种类")
    private String sand1Type;

    @Schema(description = "砂1规格")
    @ExcelProperty("砂1规格")
    private String sand1Spec;

    @Schema(description = "砂1产地")
    @ExcelProperty("砂1产地")
    private String sand1Origin;

    @Schema(description = "砂2种类_ID", example = "19677")
    @ExcelProperty("砂2种类_ID")
    private Long sand2TypeId;

    @Schema(description = "砂2种类", example = "2")
    @ExcelProperty("砂2种类")
    private String sand2Type;

    @Schema(description = "砂2规格")
    @ExcelProperty("砂2规格")
    private String sand2Spec;

    @Schema(description = "砂2产地")
    @ExcelProperty("砂2产地")
    private String sand2Origin;

    @Schema(description = "石1种类_ID", example = "15169")
    @ExcelProperty("石1种类_ID")
    private Long stone1TypeId;

    @Schema(description = "石1种类", example = "2")
    @ExcelProperty("石1种类")
    private String stone1Type;

    @Schema(description = "石1规格")
    @ExcelProperty("石1规格")
    private String stone1Spec;

    @Schema(description = "石1产地")
    @ExcelProperty("石1产地")
    private String stone1Origin;

    @Schema(description = "石2种类_ID", example = "515")
    @ExcelProperty("石2种类_ID")
    private Long stone2TypeId;

    @Schema(description = "石2种类", example = "2")
    @ExcelProperty("石2种类")
    private String stone2Type;

    @Schema(description = "石2规格")
    @ExcelProperty("石2规格")
    private String stone2Spec;

    @Schema(description = "石2产地")
    @ExcelProperty("石2产地")
    private String stone2Origin;

    @Schema(description = "外加剂1种类_ID", example = "26845")
    @ExcelProperty("外加剂1种类_ID")
    private Long additive1TypeId;

    @Schema(description = "外加剂1种类", example = "2")
    @ExcelProperty("外加剂1种类")
    private String additive1Type;

    @Schema(description = "外加剂1规格")
    @ExcelProperty("外加剂1规格")
    private String additive1Spec;

    @Schema(description = "外加剂1产地")
    @ExcelProperty("外加剂1产地")
    private String additive1Origin;

    @Schema(description = "外加剂2种类_ID", example = "18746")
    @ExcelProperty("外加剂2种类_ID")
    private Long additive2TypeId;

    @Schema(description = "外加剂2种类", example = "1")
    @ExcelProperty("外加剂2种类")
    private String additive2Type;

    @Schema(description = "外加剂2规格")
    @ExcelProperty("外加剂2规格")
    private String additive2Spec;

    @Schema(description = "外加剂2产地")
    @ExcelProperty("外加剂2产地")
    private String additive2Origin;

    @Schema(description = "外加剂3种类_ID", example = "26929")
    @ExcelProperty("外加剂3种类_ID")
    private Long additive3TypeId;

    @Schema(description = "外加剂3种类", example = "2")
    @ExcelProperty("外加剂3种类")
    private String additive3Type;

    @Schema(description = "外加剂3规格")
    @ExcelProperty("外加剂3规格")
    private String additive3Spec;

    @Schema(description = "外加剂3产地")
    @ExcelProperty("外加剂3产地")
    private String additive3Origin;

    @Schema(description = "掺合料1种类_ID", example = "18930")
    @ExcelProperty("掺合料1种类_ID")
    private Long admixture1TypeId;

    @Schema(description = "掺合料1种类", example = "1")
    @ExcelProperty("掺合料1种类")
    private String admixture1Type;

    @Schema(description = "掺合料1规格")
    @ExcelProperty("掺合料1规格")
    private String admixture1Spec;

    @Schema(description = "掺合料1产地")
    @ExcelProperty("掺合料1产地")
    private String admixture1Origin;

    @Schema(description = "掺合料2种类_ID", example = "14136")
    @ExcelProperty("掺合料2种类_ID")
    private Long admixture2TypeId;

    @Schema(description = "掺合料2种类", example = "1")
    @ExcelProperty("掺合料2种类")
    private String admixture2Type;

    @Schema(description = "掺合料2规格")
    @ExcelProperty("掺合料2规格")
    private String admixture2Spec;

    @Schema(description = "掺合料2产地")
    @ExcelProperty("掺合料2产地")
    private String admixture2Origin;

    @Schema(description = "掺合料3种类_ID", example = "20326")
    @ExcelProperty("掺合料3种类_ID")
    private Long admixture3TypeId;

    @Schema(description = "掺合料3种类", example = "1")
    @ExcelProperty("掺合料3种类")
    private String admixture3Type;

    @Schema(description = "掺合料3规格")
    @ExcelProperty("掺合料3规格")
    private String admixture3Spec;

    @Schema(description = "掺合料3产地")
    @ExcelProperty("掺合料3产地")
    private String admixture3Origin;

    @Schema(description = "膨胀剂种类_ID", example = "19369")
    @ExcelProperty("膨胀剂种类_ID")
    private Long expansionAgentTypeId;

    @Schema(description = "膨胀剂种类", example = "1")
    @ExcelProperty("膨胀剂种类")
    private String expansionAgentType;

    @Schema(description = "膨胀剂规格")
    @ExcelProperty("膨胀剂规格")
    private String expansionAgentSpec;

    @Schema(description = "膨胀剂产地")
    @ExcelProperty("膨胀剂产地")
    private String expansionAgentOrigin;

    @Schema(description = "水泥试验编号")
    @ExcelProperty("水泥试验编号")
    private String cementTestCode;

    @Schema(description = "砂1试验编号")
    @ExcelProperty("砂1试验编号")
    private String sand1TestCode;

    @Schema(description = "砂2试验编号")
    @ExcelProperty("砂2试验编号")
    private String sand2TestCode;

    @Schema(description = "石1试验编号")
    @ExcelProperty("石1试验编号")
    private String stone1TestCode;

    @Schema(description = "石2试验编号")
    @ExcelProperty("石2试验编号")
    private String stone2TestCode;

    @Schema(description = "掺合料1试验编号")
    @ExcelProperty("掺合料1试验编号")
    private String admixture1TestCode;

    @Schema(description = "掺合料2试验编号")
    @ExcelProperty("掺合料2试验编号")
    private String admixture2TestCode;

    @Schema(description = "掺合料3试验编号")
    @ExcelProperty("掺合料3试验编号")
    private String admixture3TestCode;

    @Schema(description = "膨胀剂试验编号")
    @ExcelProperty("膨胀剂试验编号")
    private String expansionAgentTestCode;

    @Schema(description = "外加剂1试验编号")
    @ExcelProperty("外加剂1试验编号")
    private String additive1TestCode;

    @Schema(description = "外加剂2试验编号")
    @ExcelProperty("外加剂2试验编号")
    private String additive2TestCode;

    @Schema(description = "外加剂3试验编号")
    @ExcelProperty("外加剂3试验编号")
    private String additive3TestCode;

    @Schema(description = "水泥碱含量")
    @ExcelProperty("水泥碱含量")
    private Double cementAlkaliContent;

    @Schema(description = "石1碱含量")
    @ExcelProperty("石1碱含量")
    private Double stone1AlkaliContent;

    @Schema(description = "石2碱含量")
    @ExcelProperty("石2碱含量")
    private Double stone2AlkaliContent;

    @Schema(description = "砂1碱含量")
    @ExcelProperty("砂1碱含量")
    private Double sand1AlkaliContent;

    @Schema(description = "砂2碱含量")
    @ExcelProperty("砂2碱含量")
    private Double sand2AlkaliContent;

    @Schema(description = "掺合料1碱含量")
    @ExcelProperty("掺合料1碱含量")
    private Double admixture1AlkaliContent;

    @Schema(description = "掺合料2碱含量")
    @ExcelProperty("掺合料2碱含量")
    private Double admixture2AlkaliContent;

    @Schema(description = "掺合料3碱含量")
    @ExcelProperty("掺合料3碱含量")
    private Double admixture3AlkaliContent;

    @Schema(description = "外加剂1碱含量")
    @ExcelProperty("外加剂1碱含量")
    private Double additive1AlkaliContent;

    @Schema(description = "外加剂2碱含量")
    @ExcelProperty("外加剂2碱含量")
    private Double additive2AlkaliContent;

    @Schema(description = "外加剂3碱含量")
    @ExcelProperty("外加剂3碱含量")
    private Double additive3AlkaliContent;

    @Schema(description = "膨胀剂碱含量")
    @ExcelProperty("膨胀剂碱含量")
    private Double expansionAgentAlkaliContent;

    @Schema(description = "水泥氯离子含量")
    @ExcelProperty("水泥氯离子含量")
    private Double cementChlorideContent;

    @Schema(description = "石1氯离子含量")
    @ExcelProperty("石1氯离子含量")
    private Double stone1ChlorideContent;

    @Schema(description = "石2氯离子含量")
    @ExcelProperty("石2氯离子含量")
    private Double stone2ChlorideContent;

    @Schema(description = "砂1氯离子含量")
    @ExcelProperty("砂1氯离子含量")
    private Double sand1ChlorideContent;

    @Schema(description = "砂2氯离子含量")
    @ExcelProperty("砂2氯离子含量")
    private Double sand2ChlorideContent;

    @Schema(description = "掺合料1氯离子含量")
    @ExcelProperty("掺合料1氯离子含量")
    private Double admixture1ChlorideContent;

    @Schema(description = "掺合料2氯离子含量")
    @ExcelProperty("掺合料2氯离子含量")
    private Double admixture2ChlorideContent;

    @Schema(description = "掺合料3氯离子含量")
    @ExcelProperty("掺合料3氯离子含量")
    private Double admixture3ChlorideContent;

    @Schema(description = "外加剂1氯离子含量")
    @ExcelProperty("外加剂1氯离子含量")
    private Double additive1ChlorideContent;

    @Schema(description = "外加剂2氯离子含量")
    @ExcelProperty("外加剂2氯离子含量")
    private Double additive2ChlorideContent;

    @Schema(description = "外加剂3氯离子含量")
    @ExcelProperty("外加剂3氯离子含量")
    private Double additive3ChlorideContent;

    @Schema(description = "膨胀剂氯离子含量")
    @ExcelProperty("膨胀剂氯离子含量")
    private Double expansionAgentChlorideContent;

    @Schema(description = "水泥碱含量每方")
    @ExcelProperty("水泥碱含量每方")
    private Double cementAlkaliContentPer;

    @Schema(description = "石1碱含量每方")
    @ExcelProperty("石1碱含量每方")
    private Double stone1AlkaliContentPer;

    @Schema(description = "石2碱含量每方")
    @ExcelProperty("石2碱含量每方")
    private Double stone2AlkaliContentPer;

    @Schema(description = "砂1碱含量每方")
    @ExcelProperty("砂1碱含量每方")
    private Double sand1AlkaliContentPer;

    @Schema(description = "砂2碱含量每方")
    @ExcelProperty("砂2碱含量每方")
    private Double sand2AlkaliContentPer;

    @Schema(description = "掺合料1碱含量每方")
    @ExcelProperty("掺合料1碱含量每方")
    private Double admixture1AlkaliContentPer;

    @Schema(description = "掺合料2碱含量每方")
    @ExcelProperty("掺合料2碱含量每方")
    private Double admixture2AlkaliContentPer;

    @Schema(description = "掺合料3碱含量每方")
    @ExcelProperty("掺合料3碱含量每方")
    private Double admixture3AlkaliContentPer;

    @Schema(description = "外加剂1碱含量每方")
    @ExcelProperty("外加剂1碱含量每方")
    private Double additive1AlkaliContentPer;

    @Schema(description = "外加剂2碱含量每方")
    @ExcelProperty("外加剂2碱含量每方")
    private Double additive2AlkaliContentPer;

    @Schema(description = "外加剂3碱含量每方")
    @ExcelProperty("外加剂3碱含量每方")
    private Double additive3AlkaliContentPer;

    @Schema(description = "膨胀剂碱含量每方")
    @ExcelProperty("膨胀剂碱含量每方")
    private Double expansionAgentAlkaliContentPer;

    @Schema(description = "水泥氯离子含量每方")
    @ExcelProperty("水泥氯离子含量每方")
    private Double cementChlorideContentPer;

    @Schema(description = "石1氯离子含量每方")
    @ExcelProperty("石1氯离子含量每方")
    private Double stone1ChlorideContentPer;

    @Schema(description = "石2氯离子含量每方")
    @ExcelProperty("石2氯离子含量每方")
    private Double stone2ChlorideContentPer;

    @Schema(description = "砂1氯离子含量每方")
    @ExcelProperty("砂1氯离子含量每方")
    private Double sand1ChlorideContentPer;

    @Schema(description = "砂2氯离子含量每方")
    @ExcelProperty("砂2氯离子含量每方")
    private Double sand2ChlorideContentPer;

    @Schema(description = "掺合料1氯离子含量每方")
    @ExcelProperty("掺合料1氯离子含量每方")
    private Double admixture1ChlorideContentPer;

    @Schema(description = "掺合料2氯离子含量每方")
    @ExcelProperty("掺合料2氯离子含量每方")
    private Double admixture2ChlorideContentPer;

    @Schema(description = "掺合料3氯离子含量每方")
    @ExcelProperty("掺合料3氯离子含量每方")
    private Double admixture3ChlorideContentPer;

    @Schema(description = "外加剂1氯离子含量每方")
    @ExcelProperty("外加剂1氯离子含量每方")
    private Double additive1ChlorideContentPer;

    @Schema(description = "外加剂2氯离子含量每方")
    @ExcelProperty("外加剂2氯离子含量每方")
    private Double additive2ChlorideContentPer;

    @Schema(description = "外加剂3氯离子含量每方")
    @ExcelProperty("外加剂3氯离子含量每方")
    private Double additive3ChlorideContentPer;

    @Schema(description = "膨胀剂氯离子含量每方")
    @ExcelProperty("膨胀剂氯离子含量每方")
    private Double expansionAgentChlorideContentPer;

    @Schema(description = "碱含量")
    @ExcelProperty("碱含量")
    private Double totalAlkaliContent;

    @Schema(description = "氯离子含量")
    @ExcelProperty("氯离子含量")
    private Double totalChlorideContent;

    @Schema(description = "关联任务单号")
    private String taskNumber;

    @Schema(description = "关联要求坍落度")
    private String slumpRequired;

    @Schema(description = "关联标号")
    private String grade;

    @Schema(description = "关联强度等级")
    private String gStrengthGrade;

    @Schema(description = "关联抗渗等级")
    private String gImpermeabilityGrade;

    @Schema(description = "关联抗冻等级")
    private String antifreezeGrade;

    @Schema(description = "关联抗折等级")
    private String flexuralGrade;

    @Schema(description = "关联石子种类")
    private String gStoneType;

    @Schema(description = "关联特殊项目")
    private String specialItem;

    @Schema(description = "关联特殊项目1")
    private String specialItem1;

    @Schema(description = "关联特殊项目2")
    private String specialItem2;

    @Schema(description = "关联计划开盘时间")
    private LocalDateTime planOpenTime;

    @Schema(description = "关联要求开盘时间")
    private LocalDateTime demandOpenTime;

    @Schema(description = "关联实际开盘时间")
    private LocalDateTime actualOpenTime;

    @Schema(description = "关联任务结束时间")
    private LocalDateTime taskEndTime;

    @Schema(description = "关联任务状态")
    private String taskStatus;
    
    @Schema(description = "关联工程名称")
    private String gProjectName;

    @Schema(description = "关联内部合同编号")
    private String internalContractNumber;

    @Schema(description = "关联合同名称")
    private String contractName;

    @Schema(description = "关联工程概述")
    private String projectOverview;

    @Schema(description = "关联施工单位名称")
    private String companyName;

    @Schema(description = "关联施工部位")
    private String buildPosition;
}