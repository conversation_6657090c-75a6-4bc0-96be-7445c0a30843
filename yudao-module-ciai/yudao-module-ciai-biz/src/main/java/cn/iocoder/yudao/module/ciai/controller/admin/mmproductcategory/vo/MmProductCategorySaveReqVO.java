package cn.iocoder.yudao.module.ciai.controller.admin.mmproductcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 产品分类新增/修改 Request VO")
@Data
public class MmProductCategorySaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12714")
    private Long id;

    @Schema(description = "产品分类")
    private String productCategory;

}