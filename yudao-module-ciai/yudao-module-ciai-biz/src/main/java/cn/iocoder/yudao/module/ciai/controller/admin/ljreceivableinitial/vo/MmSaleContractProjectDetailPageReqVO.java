package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableinitial.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MmSaleContractProjectDetailPageReqVO extends PageParam {
    @Schema(description = "工程ID")
    private Long projectId;

    @Schema(description = "工程名称，模糊匹配")
    private String projectName;

    @Schema(description = "合同名称，模糊匹配")
    private String contractName;
}