package cn.iocoder.yudao.module.ciai.service.mmproductname;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.mmproductname.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmproductname.MmProductNameDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.mmproductname.MmProductNameMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 产品名称 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MmProductNameServiceImpl implements MmProductNameService {

    @Resource
    private MmProductNameMapper mmProductNameMapper;

    @Override
    public Long createMmProductName(MmProductNameSaveReqVO createReqVO) {
        // 插入
        MmProductNameDO mmProductName = BeanUtils.toBean(createReqVO, MmProductNameDO.class);
        mmProductNameMapper.insert(mmProductName);
        // 返回
        return mmProductName.getId();
    }

    @Override
    public void updateMmProductName(MmProductNameSaveReqVO updateReqVO) {
        // 校验存在
        validateMmProductNameExists(updateReqVO.getId());
        // 更新
        MmProductNameDO updateObj = BeanUtils.toBean(updateReqVO, MmProductNameDO.class);
        mmProductNameMapper.updateById(updateObj);
    }

    @Override
    public void deleteMmProductName(Long id) {
        // 校验存在
        validateMmProductNameExists(id);
        // 删除
        mmProductNameMapper.deleteById(id);
    }

    private void validateMmProductNameExists(Long id) {
        if (mmProductNameMapper.selectById(id) == null) {
            throw exception(MM_PRODUCT_NAME_NOT_EXISTS);
        }
    }

    @Override
    public MmProductNameDO getMmProductName(Long id) {
        return mmProductNameMapper.selectById(id);
    }

    @Override
    public PageResult<MmProductNameDO> getMmProductNamePage(MmProductNamePageReqVO pageReqVO) {
        return mmProductNameMapper.selectPage(pageReqVO);
    }

}