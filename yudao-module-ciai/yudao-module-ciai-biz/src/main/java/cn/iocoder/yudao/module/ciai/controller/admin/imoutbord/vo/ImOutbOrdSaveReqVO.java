package cn.iocoder.yudao.module.ciai.controller.admin.imoutbord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 领用出库新增/修改 Request VO")
@Data
public class ImOutbOrdSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28745")
    private Long id;

    @Schema(description = "出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outboundOrderNumber;

    @Schema(description = "单据类型")
    private String documentType;

    @Schema(description = "出库时间")
    private LocalDateTime outboundTime;

    @Schema(description = "会计期")
    private String accountingPeriod;

    @Schema(description = "领用人")
    private String receiver;

    @Schema(description = "领用分类ID")
    private Long receiveCategoryId;

    @Schema(description = "关联设备")
    private String relatedEquipment;

    @Schema(description = "备注")
    private String remarks;

}