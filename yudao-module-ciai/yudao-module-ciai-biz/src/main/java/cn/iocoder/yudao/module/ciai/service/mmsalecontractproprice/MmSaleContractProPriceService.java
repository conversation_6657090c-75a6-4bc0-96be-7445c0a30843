package cn.iocoder.yudao.module.ciai.service.mmsalecontractproprice;

import java.util.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontractproprice.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproprice.MmSaleContractProPriceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 工程定价信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MmSaleContractProPriceService {

    /**
     * 创建工程定价信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMmSaleContractProPrice(@Valid MmSaleContractProPriceSaveReqVO createReqVO);

    /**
     * 更新工程定价信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMmSaleContractProPrice(@Valid MmSaleContractProPriceSaveReqVO updateReqVO);

    /**
     * 删除工程定价信息
     *
     * @param id 编号
     */
    void deleteMmSaleContractProPrice(Long id);

    /**
     * 获得工程定价信息
     *
     * @param id 编号
     * @return 工程定价信息
     */
    MmSaleContractProPriceDO getMmSaleContractProPrice(Long id);

    /**
     * 获得工程定价信息分页
     *
     * @param pageReqVO 分页查询
     * @return 工程定价信息分页
     */
    PageResult<MmSaleContractProPriceDO> getMmSaleContractProPricePage(MmSaleContractProPricePageReqVO pageReqVO);

    /**
     * 根据产品分类和名称获取产品规格列表
     * @param trim
     * @param trim1
     * @return
     */
    List<String> getProductSpecifications(String trim, String trim1);


    IPage<MmSaleContractProPriceDO> getProPrice(Long id, int pageNum, int pageSize);

    /**
     * 快速定价
     * @param reqVO
     * @return
     */
    int quickCreatePrices(MmSaleContractProPriceSaveReqVO reqVO);

    /**
     * 更新价格
     * @param updateReqVO
     */
    void updateProjectPrices(MmSaleContractProPriceUpdateReqVO updateReqVO);
}