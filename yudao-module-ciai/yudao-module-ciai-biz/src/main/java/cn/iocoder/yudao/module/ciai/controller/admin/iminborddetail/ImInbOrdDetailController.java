package cn.iocoder.yudao.module.ciai.controller.admin.iminborddetail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.iminborddetail.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminborddetail.ImInbOrdDetailDO;
import cn.iocoder.yudao.module.ciai.service.iminborddetail.ImInbOrdDetailService;

@Tag(name = "管理后台 - 入库明细")
@RestController
@RequestMapping("/ciai/im-inb-ord-detail")
@Validated
public class ImInbOrdDetailController {

    @Resource
    private ImInbOrdDetailService imInbOrdDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建入库明细")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:create')")
    public CommonResult<Long> createImInbOrdDetail(@Valid @RequestBody ImInbOrdDetailSaveReqVO createReqVO) {
        return success(imInbOrdDetailService.createImInbOrdDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新入库明细")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:update')")
    public CommonResult<Boolean> updateImInbOrdDetail(@Valid @RequestBody ImInbOrdDetailSaveReqVO updateReqVO) {
        imInbOrdDetailService.updateImInbOrdDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除入库明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:delete')")
    public CommonResult<Boolean> deleteImInbOrdDetail(@RequestParam("id") Long id) {
        imInbOrdDetailService.deleteImInbOrdDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得入库明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:query')")
    public CommonResult<ImInbOrdDetailRespVO> getImInbOrdDetail(@RequestParam("id") Long id) {
        ImInbOrdDetailDO imInbOrdDetail = imInbOrdDetailService.getImInbOrdDetail(id);
        return success(BeanUtils.toBean(imInbOrdDetail, ImInbOrdDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得入库明细分页")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:query')")
    public CommonResult<PageResult<ImInbOrdDetailRespVO>> getImInbOrdDetailPage(@Valid ImInbOrdDetailPageReqVO pageReqVO) {
        PageResult<ImInbOrdDetailDO> pageResult = imInbOrdDetailService.getImInbOrdDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImInbOrdDetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出入库明细 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord-detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImInbOrdDetailExcel(@Valid ImInbOrdDetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImInbOrdDetailDO> list = imInbOrdDetailService.getImInbOrdDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "入库明细.xls", "数据", ImInbOrdDetailRespVO.class,
                        BeanUtils.toBean(list, ImInbOrdDetailRespVO.class));
    }

}