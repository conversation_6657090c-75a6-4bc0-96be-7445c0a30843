package cn.iocoder.yudao.module.ciai.controller.admin.geofence.vo;

import cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo.LocationPointVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 电子围栏新增/修改 Request VO")
@Data
public class GeoFenceSaveReqVO {

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "围栏名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "坐标")
    private List<LocationPointVO> multiPoints;

    @Schema(description = "电子围栏Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fenceId;
}