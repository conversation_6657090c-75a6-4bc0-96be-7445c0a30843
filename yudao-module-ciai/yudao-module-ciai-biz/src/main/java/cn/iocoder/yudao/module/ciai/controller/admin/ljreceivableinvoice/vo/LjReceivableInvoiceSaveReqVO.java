package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableinvoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 应收发票管理新增/修改 Request VO")
@Data
public class LjReceivableInvoiceSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8547")
    private Long id;

    @Schema(description = "工程ID", example = "9651")
    private Long projectId;

    @Schema(description = "开票日期")
    private LocalDateTime invoiceDate;

    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    @Schema(description = "开票类别")
    private String invoiceCategory;

    @Schema(description = "纳税人识别号", example = "23712")
    private String taxpayerId;

    @Schema(description = "开户银行", example = "王五")
    private String bankName;

    @Schema(description = "开户地址")
    private String bankAddress;

    @Schema(description = "开户单位")
    private String bankUnit;

    @Schema(description = "开户账号", example = "7515")
    private String bankAccount;

    @Schema(description = "发票张数", example = "14095")
    private Integer invoiceCount;

    @Schema(description = "发票号码")
    private String invoiceNumber;

    @Schema(description = "发票代码")
    private String invoiceCode;

    @Schema(description = "发票税点")
    private BigDecimal invoiceTax;

    @Schema(description = "其中金额")
    private BigDecimal amountIncluded;

    @Schema(description = "其中税额")
    private BigDecimal taxIncluded;

    @Schema(description = "开票申请人")
    private String invoiceApplicant;

    @Schema(description = "负责人")
    private String personInCharge;

    @Schema(description = "发票签收人")
    private String invoiceSignatory;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

}