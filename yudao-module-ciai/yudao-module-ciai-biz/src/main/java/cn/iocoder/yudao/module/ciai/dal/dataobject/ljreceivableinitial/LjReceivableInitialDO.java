package cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivableinitial;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 应收期初账款 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_lj_receivable_initial")
@KeySequence("ciai_lj_receivable_initial_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LjReceivableInitialDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 工程ID
     */
    private Long projectId;
    /**
     * 累计发生数量
     */
    private BigDecimal accumulatedOccurrenceQuantity;
    /**
     * 累计发生金额
     */
    private BigDecimal accumulatedOccurrenceAmount;
    /**
     * 累计回款金额
     */
    private BigDecimal accumulatedCollectionAmount;
    /**
     * 累计发票金额
     */
    private BigDecimal accumulatedInvoiceAmount;
    /**
     * 累计认签数量
     */
    private BigDecimal accumulatedRecognitionQuantity;
    /**
     * 累计认签金额
     */
    private BigDecimal accumulatedRecognitionAmount;
    /**
     * 会计期
     */
    private String accountingPeriod;
    /**
     * 期初日期
     */
    private LocalDateTime initialDate;
    /**
     * 启用状态
     */
    private Boolean enabled;

}