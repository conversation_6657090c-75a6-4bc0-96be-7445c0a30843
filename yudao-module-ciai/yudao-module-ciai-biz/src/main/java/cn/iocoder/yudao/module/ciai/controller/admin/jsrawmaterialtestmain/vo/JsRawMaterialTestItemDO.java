package cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "原材试验项目")
public class JsRawMaterialTestItemDO   {
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "原材类型ID")
    private Long rawMaterialTypeId;
    @Schema(description = "试验项目")
    private String testItem;
    @Schema(description = "试验标准")
    private String testStandard;
    @Schema(description = "是否必试")
    private Boolean isRequired;
    @Schema(description = "创建人")
    private String creator;
}
