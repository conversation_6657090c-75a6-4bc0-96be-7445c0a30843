package cn.iocoder.yudao.module.ciai.controller.admin.emequipmentrepair;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentrepair.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentrepair.EmEquipmentRepairDO;
import cn.iocoder.yudao.module.ciai.service.emequipmentrepair.EmEquipmentRepairService;

@Tag(name = "管理后台 - 设备维修记录")
@RestController
@RequestMapping("/ciai/em-equipment-repair")
@Validated
public class EmEquipmentRepairController {

    @Resource
    private EmEquipmentRepairService emEquipmentRepairService;

    @PostMapping("/create")
    @Operation(summary = "创建设备维修记录")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:create')")
    public CommonResult<Long> createEmEquipmentRepair(@Valid @RequestBody EmEquipmentRepairSaveReqVO createReqVO) {
        return success(emEquipmentRepairService.createEmEquipmentRepair(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备维修记录")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:update')")
    public CommonResult<Boolean> updateEmEquipmentRepair(@Valid @RequestBody EmEquipmentRepairSaveReqVO updateReqVO) {
        emEquipmentRepairService.updateEmEquipmentRepair(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备维修记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:delete')")
    public CommonResult<Boolean> deleteEmEquipmentRepair(@RequestParam("id") Long id) {
        emEquipmentRepairService.deleteEmEquipmentRepair(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备维修记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:query')")
    public CommonResult<EmEquipmentRepairRespVO> getEmEquipmentRepair(@RequestParam("id") Long id) {
        EmEquipmentRepairDO emEquipmentRepair = emEquipmentRepairService.getEmEquipmentRepair(id);
        return success(BeanUtils.toBean(emEquipmentRepair, EmEquipmentRepairRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备维修记录分页")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:query')")
    public CommonResult<PageResult<EmEquipmentRepairRespVO>> getEmEquipmentRepairPage(@Valid EmEquipmentRepairPageReqVO pageReqVO) {
        PageResult<EmEquipmentRepairDO> pageResult = emEquipmentRepairService.getEmEquipmentRepairPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmEquipmentRepairRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备维修记录 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-repair:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmEquipmentRepairExcel(@Valid EmEquipmentRepairPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmEquipmentRepairDO> list = emEquipmentRepairService.getEmEquipmentRepairPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "设备维修记录.xls", "数据", EmEquipmentRepairRespVO.class,
                BeanUtils.toBean(list, EmEquipmentRepairRespVO.class));
    }

}