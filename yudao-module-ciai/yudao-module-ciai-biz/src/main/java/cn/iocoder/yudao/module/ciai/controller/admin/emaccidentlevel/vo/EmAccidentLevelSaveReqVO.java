package cn.iocoder.yudao.module.ciai.controller.admin.emaccidentlevel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 设备管理_事故级别新增/修改 Request VO")
@Data
public class EmAccidentLevelSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6116")
    private Long id;

    @Schema(description = "事故级别名称", example = "李四")
    private String levelName;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}