package cn.iocoder.yudao.module.ciai.service.jsproductionmixselection;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.ciai.controller.admin.jsproductionmixselection.vo.JsProductionMixSelectionPageReqVO;
import cn.iocoder.yudao.module.ciai.controller.admin.jsproductionmixselection.vo.JsProductionMixSelectionSaveReqVO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsproductionmixselection.JsProductionMixSelectionDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 生产配合比选用 Service 接口
 *
 * <AUTHOR>
 */
public interface JsProductionMixSelectionService {

    /**
     * 创建生产配合比选用
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJsProductionMixSelection(@Valid JsProductionMixSelectionSaveReqVO createReqVO);

    /**
     * 更新生产配合比选用
     *
     * @param updateReqVO 更新信息
     */
    void updateJsProductionMixSelection(@Valid JsProductionMixSelectionSaveReqVO updateReqVO);

    /**
     * 删除生产配合比选用
     *
     * @param id 编号
     */
    void deleteJsProductionMixSelection(Long id);

    /**
     * 获得生产配合比选用
     *
     * @param id 编号
     * @return 生产配合比选用
     */
    JsProductionMixSelectionDO getJsProductionMixSelection(Long id);

    /**
     * 获得生产配合比选用分页
     *
     * @param pageReqVO 分页查询
     * @return 生产配合比选用分页
     */
    PageResult<JsProductionMixSelectionDO> getJsProductionMixSelectionPage(JsProductionMixSelectionPageReqVO pageReqVO);

    /**
     * 获得生产配合比选用分页（包含关联表字段）
     *
     * @param pageReqVO 分页查询
     * @return 生产配合比选用分页
     */
    List<JsProductionMixSelectionDO> getJsProductionMixSelectionPageWithRelated(JsProductionMixSelectionPageReqVO pageReqVO);

}