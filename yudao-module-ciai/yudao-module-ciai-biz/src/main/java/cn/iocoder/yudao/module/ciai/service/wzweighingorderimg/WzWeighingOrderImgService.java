package cn.iocoder.yudao.module.ciai.service.wzweighingorderimg;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.wzweighingorderimg.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzweighingorderimg.WzWeighingOrderImgDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 物资管理_采购入库过磅单图片 Service 接口
 *
 * <AUTHOR>
 */
public interface WzWeighingOrderImgService {

    /**
     * 创建物资管理_采购入库过磅单图片
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWzWeighingOrderImg(@Valid WzWeighingOrderImgSaveReqVO createReqVO);

    /**
     * 更新物资管理_采购入库过磅单图片
     *
     * @param updateReqVO 更新信息
     */
    void updateWzWeighingOrderImg(@Valid WzWeighingOrderImgSaveReqVO updateReqVO);

    /**
     * 删除物资管理_采购入库过磅单图片
     *
     * @param id 编号
     */
    void deleteWzWeighingOrderImg(Long id);

    /**
     * 获得物资管理_采购入库过磅单图片
     *
     * @param id 编号
     * @return 物资管理_采购入库过磅单图片
     */
    WzWeighingOrderImgDO getWzWeighingOrderImg(Long id);

    /**
     * 获得物资管理_采购入库过磅单图片分页
     *
     * @param pageReqVO 分页查询
     * @return 物资管理_采购入库过磅单图片分页
     */
    PageResult<WzWeighingOrderImgDO> getWzWeighingOrderImgPage(WzWeighingOrderImgPageReqVO pageReqVO);

}