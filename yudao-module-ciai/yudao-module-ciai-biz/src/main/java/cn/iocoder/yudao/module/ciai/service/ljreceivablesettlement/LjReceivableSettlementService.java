package cn.iocoder.yudao.module.ciai.service.ljreceivablesettlement;

import java.util.*;

import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivablesettlement.LjReceivableSettlementJoinDO;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.ljreceivablesettlement.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivablesettlement.LjReceivableSettlementDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 两金管理_应收_结算管理 Service 接口
 *
 * <AUTHOR>
 */
public interface LjReceivableSettlementService {

    /**
     * 创建两金管理_应收_结算管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLjReceivableSettlement(@Valid LjReceivableSettlementSaveReqVO createReqVO);

    /**
     * 更新两金管理_应收_结算管理
     *
     * @param updateReqVO 更新信息
     */
    void updateLjReceivableSettlement(@Valid LjReceivableSettlementSaveReqVO updateReqVO);

    /**
     * 批量更新两金管理_应收_结算管理
     * @param updateReqVO
     */
    void updateLjReceivableSettlementBatch(@Valid List<LjReceivableSettlementSaveReqVO> updateReqVO);


    /**
     * 删除两金管理_应收_结算管理
     *
     * @param id 编号
     */
    void deleteLjReceivableSettlement(Long id);

    /**
     * 删除两金管理_应收_结算管理
     *
     * @param settlementNumber
     */
    void deleteLjReceivableSettlementByNumber(String settlementNumber);

    /**
     * 获得两金管理_应收_结算管理
     *
     * @param id 编号
     * @return 两金管理_应收_结算管理
     */
    LjReceivableSettlementDO getLjReceivableSettlement(Long id);

    /**
     * 获得两金管理_应收_结算管理
     *
     * @param settlementNumber
     * @return
     */

    List<LjReceivableSettlementJoinDO> getLjReceivableSettlementByNumber(String settlementNumber);


    /**
     * 获得两金管理_应收_结算管理分页
     *
     * @param pageReqVO 分页查询
     * @return 两金管理_应收_结算管理分页
     */
    PageResult<LjReceivableSettlementDO> getLjReceivableSettlementPage(LjReceivableSettlementPageReqVO pageReqVO);

    /**
     * 获得两金管理_应收_结算管理联表查询分页
     *
     * @param pageReqVO 分页查询
     * @return 两金管理_应收_结算管理分页
     */
    PageResult<LjReceivableSettlementJoinDO> getLjReceivableSettlementPageJoin(LjReceivableSettlementPageReqVO pageReqVO);

    /**
     * 更新两金管理_应收_结算管理审核状态
     *
     * @param settlementNumber
     * @param type
     */
    void updateLjReceivableSettlementAudited(String settlementNumber, Integer type);

}