package cn.iocoder.yudao.module.ciai.dal.mysql.wzpurchasecontract;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.controller.admin.wzpurchasecontract.vo.WzMaterialPricingReqVO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchasecontract.WzMaterialPricingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 物资管理_采购合同_材料信息_定价 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WzMaterialPricingMapper extends BaseMapperX<WzMaterialPricingDO> {

    default PageResult<WzMaterialPricingDO> selectPage(PageParam reqVO, Long materialInfoId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WzMaterialPricingDO>()
                .eq(WzMaterialPricingDO::getMaterialInfoId, materialInfoId)
                .orderByDesc(WzMaterialPricingDO::getId));
    }

    default int deleteByMaterialInfoId(Long materialInfoId) {
        return delete(WzMaterialPricingDO::getMaterialInfoId, materialInfoId);
    }

    default PageResult<WzMaterialPricingDO> selectConditionPage(WzMaterialPricingReqVO reqVO){
        return selectPage(reqVO, new LambdaQueryWrapperX<WzMaterialPricingDO>()
                .ltIfPresent(WzMaterialPricingDO::getMaterialUnitPrice,reqVO.getMaterialUnitPriceMax())
                .gtIfPresent(WzMaterialPricingDO::getMaterialUnitPrice,reqVO.getMaterialUnitPriceMin())
                .ltIfPresent(WzMaterialPricingDO::getTransportUnitPrice,reqVO.getTransportUnitPriceMax())
                .gtIfPresent(WzMaterialPricingDO::getTransportUnitPrice,reqVO.getTransportUnitPriceMin())
                .betweenIfPresent(WzMaterialPricingDO::getStartDate, reqVO.getStartDateRange())
                .eq(WzMaterialPricingDO::getMaterialInfoId, reqVO.getMaterialInfoId())
                .orderByDesc(WzMaterialPricingDO::getId));
    }

    @Select("select material_info_id from ciai_wz_material_pricing where deleted = 0")
    List<Long> selectAllMaterialids();
}
