package cn.iocoder.yudao.module.ciai.controller.admin.jssyflyashtest.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 粉煤灰试验分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JsSyFlyAshTestPageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "试验编号")
    private String testNo;

    @Schema(description = "委托编号")
    private String entrustNo;

    @Schema(description = "试样编号")
    private String sampleNo;

    @Schema(description = "委托单位")
    private String entrustUnit;

    @Schema(description = "试验委托人")
    private String testClient;

    @Schema(description = "负责人")
    private String responsiblePerson;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "试验人")
    private String tester;

    @Schema(description = "工程名称", example = "张三")
    private String projectName;

    @Schema(description = "种类编码")
    private String typeCode;

    @Schema(description = "种类", example = "2")
    private String type;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "产地")
    private String origin;

    @Schema(description = "出厂日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] factoryDate;

    @Schema(description = "进厂日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] entryDate;

    @Schema(description = "收样日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sampleDate;

    @Schema(description = "代表数量")
    private String representQuantity;

    @Schema(description = "烧失量")
    private BigDecimal lossOnIgnition;

    @Schema(description = "细度")
    private BigDecimal fineness;

    @Schema(description = "需水量比")
    private Integer waterDemandRatio;

    @Schema(description = "结论")
    private String conclusion;

    @Schema(description = "试验日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] testDate;

    @Schema(description = "截止日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] deadlineDate;

    @Schema(description = "名称", example = "芋艿")
    private String name;

    @Schema(description = "28天胶砂抗压比")
    private String compressiveStrengthRatio28d;

    @Schema(description = "含水量")
    private BigDecimal moistureContent;

    @Schema(description = "报告日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] reportDate;

    @Schema(description = "XM1")
    private String xm1;

    @Schema(description = "XM2")
    private String xm2;

    @Schema(description = "出厂编号")
    private String factoryNo;

    @Schema(description = "XM11")
    private String xm11;

    @Schema(description = "XM21")
    private String xm21;

    @Schema(description = "XM31")
    private String xm31;

    @Schema(description = "XM12")
    private String xm12;

    @Schema(description = "XM22")
    private String xm22;

    @Schema(description = "XM32")
    private String xm32;

    @Schema(description = "XM13")
    private String xm13;

    @Schema(description = "XM23")
    private String xm23;

    @Schema(description = "XM33")
    private String xm33;

    @Schema(description = "XM14")
    private String xm14;

    @Schema(description = "XM24")
    private String xm24;

    @Schema(description = "XM34")
    private String xm34;

    @Schema(description = "XM15")
    private String xm15;

    @Schema(description = "XM25")
    private String xm25;

    @Schema(description = "XM35")
    private String xm35;

    @Schema(description = "XM16")
    private String xm16;

    @Schema(description = "XM26")
    private String xm26;

    @Schema(description = "XM36")
    private String xm36;

    @Schema(description = "XM17")
    private String xm17;

    @Schema(description = "XM27")
    private String xm27;

    @Schema(description = "XM37")
    private String xm37;

    @Schema(description = "XM18")
    private String xm18;

    @Schema(description = "XM28")
    private String xm28;

    @Schema(description = "XM38")
    private String xm38;

    @Schema(description = "碱含量")
    private BigDecimal alkaliContent;

    @Schema(description = "氯离子")
    private BigDecimal chlorideIon;

    @Schema(description = "附件状态", example = "1")
    private String attachmentStatus;

    @Schema(description = "修改人")
    private String modifier;

    @Schema(description = "时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modifyTime;

    @Schema(description = "XD11")
    private Double xd11;

    @Schema(description = "XD12")
    private Double xd12;

    @Schema(description = "XD筛余1")
    private BigDecimal xdResidue1;

    @Schema(description = "XD校正")
    private Double xdCorrection;

    @Schema(description = "XD21")
    private Double xd21;

    @Schema(description = "XD22")
    private Double xd22;

    @Schema(description = "XD筛余2")
    private BigDecimal xdResidue2;

    @Schema(description = "XDV")
    private BigDecimal xdV;

    @Schema(description = "XS11")
    private Integer xs11;

    @Schema(description = "XS12")
    private Integer xs12;

    @Schema(description = "XS13")
    private Integer xs13;

    @Schema(description = "XS14")
    private Integer xs14;

    @Schema(description = "XSL1")
    private Integer xsL1;

    @Schema(description = "XS21")
    private Integer xs21;

    @Schema(description = "XS22")
    private Integer xs22;

    @Schema(description = "XS23")
    private Integer xs23;

    @Schema(description = "XSL2")
    private Integer xsL2;

    @Schema(description = "XSLB")
    private Integer xsLb;

    @Schema(description = "SS11")
    private BigDecimal ss11;

    @Schema(description = "SS12")
    private BigDecimal ss12;

    @Schema(description = "SS13")
    private BigDecimal ss13;

    @Schema(description = "SS14")
    private BigDecimal ss14;

    @Schema(description = "SS1烧前重")
    private BigDecimal ss1PreWeight;

    @Schema(description = "SS1合重")
    private BigDecimal ss1TotalWeight;

    @Schema(description = "SS15")
    private BigDecimal ss15;

    @Schema(description = "SS16")
    private BigDecimal ss16;

    @Schema(description = "SS17")
    private BigDecimal ss17;

    @Schema(description = "SS18")
    private BigDecimal ss18;

    @Schema(description = "SS1烧后重")
    private BigDecimal ss1PostWeight;

    @Schema(description = "SSL1")
    private BigDecimal ssL1;

    @Schema(description = "SS21")
    private BigDecimal ss21;

    @Schema(description = "SS22")
    private BigDecimal ss22;

    @Schema(description = "SS23")
    private BigDecimal ss23;

    @Schema(description = "SS24")
    private BigDecimal ss24;

    @Schema(description = "SS2烧前重")
    private BigDecimal ss2PreWeight;

    @Schema(description = "SS2合重")
    private BigDecimal ss2TotalWeight;

    @Schema(description = "SS2烧后重")
    private BigDecimal ss2PostWeight;

    @Schema(description = "SS25")
    private BigDecimal ss25;

    @Schema(description = "SS26")
    private BigDecimal ss26;

    @Schema(description = "SS27")
    private BigDecimal ss27;

    @Schema(description = "SS28")
    private BigDecimal ss28;

    @Schema(description = "SSL2")
    private BigDecimal ssL2;

    @Schema(description = "执行标准")
    private String standard;

    @Schema(description = "试验状态", example = "2")
    private String testStatus;

    @Schema(description = "使用状态", example = "1")
    private String usageStatus;

    @Schema(description = "选择")
    private Integer selected;

    @Schema(description = "打印")
    private Integer printed;

    @Schema(description = "Y")
    private Integer yValue;

    @Schema(description = "站别")
    private String stationCode;

    @Schema(description = "试验人2")
    private String tester2;

    @Schema(description = "执行标准2")
    private String standard2;

    @Schema(description = "工程类型", example = "1")
    private String projectType;

    @Schema(description = "厂家牌号")
    private String factoryBrand;

    @Schema(description = "活性指数7")
    private Integer activityIndex7d;

    @Schema(description = "活性指数28")
    private Integer activityIndex28d;

    @Schema(description = "试验状态7天")
    private LocalDateTime testStatus7d;

    @Schema(description = "试验状态28天")
    private LocalDateTime testStatus28d;

    @Schema(description = "荷载71")
    private BigDecimal load71;

    @Schema(description = "荷载72")
    private BigDecimal load72;

    @Schema(description = "荷载73")
    private BigDecimal load73;

    @Schema(description = "荷载74")
    private BigDecimal load74;

    @Schema(description = "荷载75")
    private BigDecimal load75;

    @Schema(description = "荷载76")
    private BigDecimal load76;

    @Schema(description = "抗压71")
    private BigDecimal compressive71;

    @Schema(description = "抗压72")
    private BigDecimal compressive72;

    @Schema(description = "抗压73")
    private BigDecimal compressive73;

    @Schema(description = "抗压74")
    private BigDecimal compressive74;

    @Schema(description = "抗压75")
    private BigDecimal compressive75;

    @Schema(description = "抗压76")
    private BigDecimal compressive76;

    @Schema(description = "抗压7V")
    private BigDecimal compressive7v;

    @Schema(description = "基准荷载71")
    private BigDecimal baseLoad71;

    @Schema(description = "基准荷载72")
    private BigDecimal baseLoad72;

    @Schema(description = "基准荷载73")
    private BigDecimal baseLoad73;

    @Schema(description = "基准荷载74")
    private BigDecimal baseLoad74;

    @Schema(description = "基准荷载75")
    private BigDecimal baseLoad75;

    @Schema(description = "基准荷载76")
    private BigDecimal baseLoad76;

    @Schema(description = "基准抗压71")
    private BigDecimal baseCompressive71;

    @Schema(description = "基准抗压72")
    private BigDecimal baseCompressive72;

    @Schema(description = "基准抗压73")
    private BigDecimal baseCompressive73;

    @Schema(description = "基准抗压74")
    private BigDecimal baseCompressive74;

    @Schema(description = "基准抗压75")
    private BigDecimal baseCompressive75;

    @Schema(description = "基准抗压76")
    private BigDecimal baseCompressive76;

    @Schema(description = "基准抗压7V")
    private BigDecimal baseCompressive7v;

    @Schema(description = "基准荷载281")
    private BigDecimal baseLoad281;

    @Schema(description = "基准荷载282")
    private BigDecimal baseLoad282;

    @Schema(description = "基准荷载283")
    private BigDecimal baseLoad283;

    @Schema(description = "基准荷载284")
    private BigDecimal baseLoad284;

    @Schema(description = "基准荷载285")
    private BigDecimal baseLoad285;

    @Schema(description = "基准荷载286")
    private BigDecimal baseLoad286;

    @Schema(description = "荷载281")
    private BigDecimal load281;

    @Schema(description = "荷载282")
    private BigDecimal load282;

    @Schema(description = "荷载283")
    private BigDecimal load283;

    @Schema(description = "荷载284")
    private BigDecimal load284;

    @Schema(description = "荷载285")
    private BigDecimal load285;

    @Schema(description = "荷载286")
    private BigDecimal load286;

    @Schema(description = "抗压281")
    private BigDecimal compressive281;

    @Schema(description = "抗压282")
    private BigDecimal compressive282;

    @Schema(description = "抗压283")
    private BigDecimal compressive283;

    @Schema(description = "抗压284")
    private BigDecimal compressive284;

    @Schema(description = "抗压285")
    private BigDecimal compressive285;

    @Schema(description = "抗压286")
    private BigDecimal compressive286;

    @Schema(description = "抗压28V")
    private BigDecimal compressive28v;

    @Schema(description = "基准抗压281")
    private BigDecimal baseCompressive281;

    @Schema(description = "基准抗压282")
    private BigDecimal baseCompressive282;

    @Schema(description = "基准抗压283")
    private BigDecimal baseCompressive283;

    @Schema(description = "基准抗压284")
    private BigDecimal baseCompressive284;

    @Schema(description = "基准抗压285")
    private BigDecimal baseCompressive285;

    @Schema(description = "基准抗压286")
    private BigDecimal baseCompressive286;

    @Schema(description = "基准抗压28V")
    private BigDecimal baseCompressive28v;

    @Schema(description = "BBMJV")
    private String bbmjv;

    @Schema(description = "HS1")
    private BigDecimal hs1;

    @Schema(description = "HS2")
    private BigDecimal hs2;

    @Schema(description = "HS3")
    private BigDecimal hs3;

    @Schema(description = "HSL")
    private BigDecimal hsL;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}