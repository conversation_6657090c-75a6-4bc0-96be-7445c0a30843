package cn.iocoder.yudao.module.ciai.service.jssylightweightaggregatetest;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.jssylightweightaggregatetest.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssylightweightaggregatetest.JsSyLightweightAggregateTestDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 轻集料试验 Service 接口
 *
 * <AUTHOR>
 */
public interface JsSyLightweightAggregateTestService {

    /**
     * 创建轻集料试验
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJsSyLightweightAggregateTest(@Valid JsSyLightweightAggregateTestSaveReqVO createReqVO);

    /**
     * 更新轻集料试验
     *
     * @param updateReqVO 更新信息
     */
    void updateJsSyLightweightAggregateTest(@Valid JsSyLightweightAggregateTestSaveReqVO updateReqVO);

    /**
     * 删除轻集料试验
     *
     * @param id 编号
     */
    void deleteJsSyLightweightAggregateTest(Long id);

    /**
     * 获得轻集料试验
     *
     * @param id 编号
     * @return 轻集料试验
     */
    JsSyLightweightAggregateTestDO getJsSyLightweightAggregateTest(Long id);

    /**
     * 获得轻集料试验分页
     *
     * @param pageReqVO 分页查询
     * @return 轻集料试验分页
     */
    PageResult<JsSyLightweightAggregateTestDO> getJsSyLightweightAggregateTestPage(JsSyLightweightAggregateTestPageReqVO pageReqVO);

}