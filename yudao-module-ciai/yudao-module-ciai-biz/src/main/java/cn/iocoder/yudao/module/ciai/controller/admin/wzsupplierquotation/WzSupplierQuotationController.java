package cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierquotation;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierquotation.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplierquotation.WzSupplierQuotationDO;
import cn.iocoder.yudao.module.ciai.service.wzsupplierquotation.WzSupplierQuotationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 供应商材料报价")
@RestController
@RequestMapping("/ciai/wz-supplier-quotation")
@Validated
public class WzSupplierQuotationController {

    @Resource
    private WzSupplierQuotationService wzSupplierQuotationService;

    @PostMapping("/create")
    @Operation(summary = "创建供应商材料报价")
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:create')")
    public CommonResult<Integer> createWzSupplierQuotation(@Valid @RequestBody WzSupplierQuotationSaveReqVO createReqVO) {
        return success(wzSupplierQuotationService.createWzSupplierQuotation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新供应商材料报价")
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:update')")
    public CommonResult<Boolean> updateWzSupplierQuotation(@Valid @RequestBody WzSupplierQuotationSaveReqVO updateReqVO) {
        wzSupplierQuotationService.updateWzSupplierQuotation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除供应商材料报价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:delete')")
    public CommonResult<Boolean> deleteWzSupplierQuotation(@RequestParam("id") Integer id) {
        wzSupplierQuotationService.deleteWzSupplierQuotation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得供应商材料报价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:query')")
    public CommonResult<WzSupplierQuotationRespVO> getWzSupplierQuotation(@RequestParam("id") Integer id) {
        WzSupplierQuotationDO wzSupplierQuotation = wzSupplierQuotationService.getWzSupplierQuotation(id);
        return success(BeanUtils.toBean(wzSupplierQuotation, WzSupplierQuotationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得供应商材料报价分页")
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:query')")
    public CommonResult<PageResult<WzSupplierQuotationRespVO>> getWzSupplierQuotationPage(@Valid WzSupplierQuotationPageReqVO pageReqVO) {
        PageResult<WzSupplierQuotationDO> pageResult = wzSupplierQuotationService.getWzSupplierQuotationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WzSupplierQuotationRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出供应商材料报价 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:wz-supplier-quotation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWzSupplierQuotationExcel(@Valid WzSupplierQuotationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WzSupplierQuotationDO> list = wzSupplierQuotationService.getWzSupplierQuotationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "供应商材料报价.xls", "数据", WzSupplierQuotationRespVO.class,
                        BeanUtils.toBean(list, WzSupplierQuotationRespVO.class));
    }


    //   //根据用户id查询采购计划
    //  getPurchasePlanByUserId: async (params) => {
    //    return await request.get({ url: `/ciai/wz-supplier-quotation/get-purchase-plan-by-user-id`, params })
    //  },
    @GetMapping("/get-purchase-plan-by-user-id")
    @Operation(summary = "根据用户id查询采购计划")
    public CommonResult<List<WzSupplierQuotationDO >> getPurchasePlanByUserId(@Valid QuotationByIdReqVO quotation) {
        List<WzSupplierQuotationDO> list = wzSupplierQuotationService.getPurchasePlanByUserId(quotation);
        return success(list);
    }
//    supplierQuotationOperation: async (data: any) => {
//        return await request.post({ url: `/ciai/wz-supplier-quotation/supplier-quotation-operation`, data })
//    },
    @PostMapping("/supplier-quotation-operation")
    @Operation(summary = "供应商报价操作")
    public CommonResult<Boolean> supplierQuotationOperation(@RequestBody WzSupplierQuotationDO wzSupplierQuotationDO) {
        wzSupplierQuotationService.supplierQuotationOperation(wzSupplierQuotationDO);
        return success(true);
    }

    @GetMapping("/get-comprise")
    @Operation(summary = "获取对比表")
    public CommonResult<PageResult<WzSupplierQuotationDO>> getComprise(@Valid WzSupplierCompReq wzSupplierCompReq) {
        PageResult<WzSupplierQuotationDO> page = wzSupplierQuotationService.getComprise(wzSupplierCompReq);
        return success(page);
    }
    @GetMapping("/purchase-plan")
    @Operation(summary = "获取采购计划表")
    public CommonResult<PageResult<WzPurchasePlanDO>> chasePlan(@Valid GetPurchasePlanReqVO purchasePlanReqVO) {
        PageResult<WzPurchasePlanDO> list = wzSupplierQuotationService.getPurchasePlan(purchasePlanReqVO);
        return success(list);
    }

    @GetMapping("/purchase-plan-by-supplier")
    @Operation(summary = "根据供应商获取采购计划表")
    public CommonResult<PageResult<WzPurchasePlanDO>> chasePlanBySupplier(@Valid GetPurchasePlanReqVO purchasePlanReqVO) {
        PageResult<WzPurchasePlanDO> list = wzSupplierQuotationService.getPurchasePlanBySupplier(purchasePlanReqVO);
        return success(list);
    }

    @PostMapping("/update-price")
    @Operation(summary = "更新合同定价表")
    public CommonResult<Boolean> updatePrice(@RequestBody UpdatePriceReqVO updatePriceReqVO) {
        wzSupplierQuotationService.updatePrice(updatePriceReqVO);
        return success(true);
    }

    @GetMapping("/price-trend")
    @Operation(summary = "获取价格趋势")
    public CommonResult<List<PriceTrendRespVO>> getPriceTrend(@RequestParam("typeName") String typeName) {
        List<PriceTrendRespVO> list = wzSupplierQuotationService.getPriceTrend(typeName);
        return success(list);
    }
}