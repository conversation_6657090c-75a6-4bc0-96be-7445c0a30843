package cn.iocoder.yudao.module.ciai.controller.admin.cardriver.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 司机信息新增/修改 Request VO")
@Data
public class CarDriverSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "司机姓名")
    private String driverName;

}