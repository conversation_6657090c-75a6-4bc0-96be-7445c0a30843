package cn.iocoder.yudao.module.ciai.gkjinterface.dal;

import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.TjlbSelectVO;
import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.TjlbYclSelectVO;
import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.TjlbsdSelectVO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface MaterialSyncMapper {

    @Master
    @Select("SELECT actual_value FROM ciai_gk_production_consumption_material WHERE production_host = #{host} AND NO = #{fno} AND material_name= #{materialName} AND deleted = 0")
    double selectErpMaterialActualValue(@Param("host") Long host, @Param("fno") int fno, @Param("materialName") String materialName);

    @Master
    @Update("UPDATE ciai_gk_production_consumption set deleted = 1 WHERE production_host = #{host} AND no = #{fno} AND deleted = 0")
    int deleteErpMaterialRecord(@Param("host") Long host, @Param("fno") int fno);

    @Master
    @Select("""
            SELECT
              DATE_FORMAT(IFNULL(MAX(production_time), '2018-01-01'), '%Y-%m-%d %H:%i:%s')
            FROM
              ciai_gk_production_manual_consumption
            WHERE
              production_host = #{host} AND deleted = 0            
            """)
    String selectMaxManualMaterialTime(@Param("host") Long host);

    @Master
    @Select("select MAX(no) from ciai_gk_production_manual_consumption where  production_host = #{host} AND deleted = 0")
    String selectMaxManualMaterialNo(@Param("host") Long host);

    //    @DS("unit1")
    @Select("SELECT MAX(frqsj) FROM ${tableName} WHERE FScbt = #{host}")
    Date selectMaxManualTimeFromGK(@Param("tableName") String tableName, @Param("host") Long host);

    //    @DS("unit1")
    List<TjlbsdSelectVO> selectManualMaterialFromGK(@Param("host") Long host, @Param("erpTime") String erpTime, @Param("collectionQuantity") int collectionQuantity);

    //    @DS("unit1")
    List<TjlbSelectVO> selectMasterByFno(@Param("host") Long host, @Param("fno") int fno);

    //    @DS("unit1")
    List<TjlbSelectVO> selectMasterList(@Param("host") Long host, @Param("fno") int fno, @Param("collectionQuantity") int collectionQuantity);

    List<TjlbSelectVO> selectMasterDiskList(@Param("host") Long host, @Param("fno") int fno, @Param("collectionQuantity") int collectionQuantity);


    //    @DS("unit1")
    List<TjlbYclSelectVO> selectMaterialDetailByFno(@Param("fno") int fno, @Param("currentBatch") int  currentBatch);

    //    @DS("unit1")
    @Select("select max(ycl.FNo) from TJlbYcl  ycl  inner join TJlb on tjlb.FNo=ycl.FNo where FScbt = #{host}")
    int selectGkMaxFno(@Param("host") Long host);

    //    @DS("unit1")
    @Select("SELECT TOP 1 * FROM tjlbycl WHERE FNo = #{fno} AND FPanNo = 0")
    Map<String, Object> selectLastGkMaterial(@Param("fno") int fno);
}
