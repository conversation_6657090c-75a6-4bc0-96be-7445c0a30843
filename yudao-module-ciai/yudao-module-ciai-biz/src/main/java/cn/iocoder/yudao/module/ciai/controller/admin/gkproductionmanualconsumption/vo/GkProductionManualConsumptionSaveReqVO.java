package cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualconsumption.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 生产手动消耗新增/修改 Request VO")
@Data
public class GkProductionManualConsumptionSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "编号")
    private Integer no;

    @Schema(description = "生产主机")
    private Long productionHost;

    @Schema(description = "生产时间")
    private LocalDateTime productionTime;

    @Schema(description = "出单时间")
    private LocalDateTime orderTime;

    @Schema(description = "产品标号")
    private String productNumber;

    @Schema(description = "生产方量")
    private BigDecimal productionVolume;

    @Schema(description = "生产人员")
    private String productionPersonnel;

    @Schema(description = "当前盘次")
    private Integer currentBatch;

    @Schema(description = "涉及盘数")
    private Integer involvedBatchCount;

    @Schema(description = "是否砂浆")
    private Integer isMortar;

    @Schema(description = "任务单号")
    private String taskNumber;

    @Schema(description = "配比单号")
    private String mixOrderNumber;

    @Schema(description = "运输单号")
    private String transportOrderNumber;

    @Schema(description = "工程名称")
    private String projectName;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "施工部位")
    private String constructionPart;

    @Schema(description = "浇注方式")
    private String pouringMethod;

    @Schema(description = "技术要求")
    private String technicalRequirements;

    @Schema(description = "车辆编号")
    private String vehicleNumber;

    @Schema(description = "司机")
    private String driver;

    @Schema(description = "本车方量")
    private BigDecimal vehicleVolume;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "计算机名")
    private String computerName;

    @Schema(description = "采集时间")
    private LocalDateTime collectionTime;

    @Schema(description = "是否手动")
    private Integer isManual;

    @Schema(description = "PHB1")
    private Integer phb1;

    @Schema(description = "PHB2")
    private Integer phb2;

    @Schema(description = "PHB3")
    private Integer phb3;

    @Schema(description = "搅拌时间")
    private Integer mixingTime;

    @Schema(description = "隐藏")
    private Integer hidden;

    @Schema(description = "启用状态")
    private Integer enabled;

}