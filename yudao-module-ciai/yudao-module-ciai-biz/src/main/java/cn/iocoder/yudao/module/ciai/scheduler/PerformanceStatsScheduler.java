package cn.iocoder.yudao.module.ciai.scheduler;

import cn.iocoder.yudao.module.ciai.common.VehiclePerformanceCacheService;
import cn.iocoder.yudao.module.ciai.dal.dataobject.carvehicle.CarVehicleDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.vehicleperformancestats.VehiclePerformanceStatsDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.carvehicle.CarVehicleMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class PerformanceStatsScheduler {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private VehiclePerformanceCacheService performanceCacheService;

    @Resource
    private CarVehicleMapper carVehicleMapper;


    /**
     * 每日凌晨1:00执行一次，更新车辆性能统计表
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void updateVehiclePerformanceStats() {
        String sql = """
                    INSERT INTO ciai_vehicle_performance_stats (
                         truck_number,
                         project_address,
                         avg_transport_time,
                         avg_unload_time,
                         avg_return_time,
                         avg_production_time
                     )
                     SELECT
                         v.self_number,
                         p.project_address,
                         -- 平均运输时间：从出站到到达工地的时间差（分钟）
                         AVG(TIMESTAMPDIFF(MINUTE, s.out_factory_dtime, s.site_in_dtime)) AS avg_transport_time,
                
                         -- 平均卸料时间：从开始卸料到结束的时间差（分钟）
                         AVG(TIMESTAMPDIFF(MINUTE, s.unburden_dtime, s.unburden_end_dtime)) AS avg_unload_time,
                
                         -- 平均回厂时间：从离开工地到回站的时间差（分钟）
                         AVG(TIMESTAMPDIFF(MINUTE, s.site_out_dtime, r.return_time)) AS avg_return_time,
                
                         -- 平均生产时间：从出单到出战完成的时间差（分钟）
                         AVG(TIMESTAMPDIFF(MINUTE, r.order_time, r.departure_time)) AS avg_production_time
                
                     FROM hnt_concrete_supply_record s
                     JOIN ciai_pm_ship_records r ON s.ship_records_id = r.id
                     JOIN ciai_pm_task_plan p ON r.task_plan_id = p.id
                     JOIN ciai_car_vehicle v ON r.vehicle_id = v.id  -- 加入车辆信息表的连接
                     WHERE r.status = '已出单' AND s.out_factory_dtime IS NOT NULL
                     GROUP BY v.self_number, p.project_address
                     ON DUPLICATE KEY UPDATE
                         avg_transport_time = VALUES(avg_transport_time),
                         avg_unload_time = VALUES(avg_unload_time),
                         avg_return_time = VALUES(avg_return_time),
                         avg_production_time = VALUES(avg_production_time)
                
                """;

        try {
            jdbcTemplate.update(sql);
            log.info("【车辆性能统计】每日调度统计数据已更新");
            refreshAllVehiclesCache();
        } catch (Exception e) {
            log.error("【车辆性能统计】每日更新失败", e);
        }
    }

    /**
     * 更新完数据库后，刷新 Redis 缓存（可选）
     */
    private void refreshAllVehiclesCache() {
        List<CarVehicleDO> allVehicles = carVehicleMapper.selectAll(); // 查询所有车辆编号
        for (CarVehicleDO vehicle : allVehicles) {
            List<VehiclePerformanceStatsDO> statsList = performanceCacheService.getPerformanceStats(vehicle.getSelfNumber());
            if (statsList != null && !statsList.isEmpty()) {
                performanceCacheService.setPerformanceStats(vehicle.getSelfNumber(), statsList);
                log.info("【Redis】车辆 {} 的缓存已刷新", vehicle.getSelfNumber());
            }
        }
    }
}