package cn.iocoder.yudao.module.ciai.controller.admin.emequipmentplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备管理_设备计划新增/修改 Request VO")
@Data
public class EmEquipmentPlanSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "计划编号")
    private String code;

    @Schema(description = "计划名称")
    private String name;

    @Schema(description = "方案")
    private Long planId;

    @Schema(description = "起始日期")
    private LocalDateTime startDate;

    @Schema(description = "期限")
    private Long duration;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "完成日期")
    private LocalDateTime completionDate;

    @Schema(description = "责任人")
    private String responsiblePerson;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "设备名称")
    private Long equipmentId;

    @Schema(description = "现场拍照")
    private String onSitePhoto;

    @Schema(description = "最终结果")
    private String finalResult;

}