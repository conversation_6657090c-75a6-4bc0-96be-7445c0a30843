package cn.iocoder.yudao.module.ciai.controller.admin.jssyflyashtest.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 粉煤灰试验 Response VO")
@Data
@ExcelIgnoreUnannotated
public class JsSyFlyAshTestRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8793")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "单号")
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "试验编号")
    @ExcelProperty("试验编号")
    private String testNo;

    @Schema(description = "委托编号")
    @ExcelProperty("委托编号")
    private String entrustNo;

    @Schema(description = "试样编号")
    @ExcelProperty("试样编号")
    private String sampleNo;

    @Schema(description = "委托单位")
    @ExcelProperty("委托单位")
    private String entrustUnit;

    @Schema(description = "试验委托人")
    @ExcelProperty("试验委托人")
    private String testClient;

    @Schema(description = "负责人")
    @ExcelProperty("负责人")
    private String responsiblePerson;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String reviewer;

    @Schema(description = "试验人")
    @ExcelProperty("试验人")
    private String tester;

    @Schema(description = "工程名称", example = "张三")
    @ExcelProperty("工程名称")
    private String projectName;

    @Schema(description = "种类编码")
    @ExcelProperty("种类编码")
    private String typeCode;

    @Schema(description = "种类", example = "2")
    @ExcelProperty("种类")
    private String type;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "产地")
    @ExcelProperty("产地")
    private String origin;

    @Schema(description = "出厂日期")
    @ExcelProperty("出厂日期")
    private LocalDateTime factoryDate;

    @Schema(description = "进厂日期")
    @ExcelProperty("进厂日期")
    private LocalDateTime entryDate;

    @Schema(description = "收样日期")
    @ExcelProperty("收样日期")
    private LocalDateTime sampleDate;

    @Schema(description = "代表数量")
    @ExcelProperty("代表数量")
    private String representQuantity;

    @Schema(description = "烧失量")
    @ExcelProperty("烧失量")
    private BigDecimal lossOnIgnition;

    @Schema(description = "细度")
    @ExcelProperty("细度")
    private BigDecimal fineness;

    @Schema(description = "需水量比")
    @ExcelProperty("需水量比")
    private Integer waterDemandRatio;

    @Schema(description = "结论")
    @ExcelProperty("结论")
    private String conclusion;

    @Schema(description = "试验日期")
    @ExcelProperty("试验日期")
    private LocalDateTime testDate;

    @Schema(description = "截止日期")
    @ExcelProperty("截止日期")
    private LocalDateTime deadlineDate;

    @Schema(description = "名称", example = "芋艿")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "28天胶砂抗压比")
    @ExcelProperty("28天胶砂抗压比")
    private String compressiveStrengthRatio28d;

    @Schema(description = "含水量")
    @ExcelProperty("含水量")
    private BigDecimal moistureContent;

    @Schema(description = "报告日期")
    @ExcelProperty("报告日期")
    private LocalDateTime reportDate;

    @Schema(description = "XM1")
    @ExcelProperty("XM1")
    private String xm1;

    @Schema(description = "XM2")
    @ExcelProperty("XM2")
    private String xm2;

    @Schema(description = "出厂编号")
    @ExcelProperty("出厂编号")
    private String factoryNo;

    @Schema(description = "XM11")
    @ExcelProperty("XM11")
    private String xm11;

    @Schema(description = "XM21")
    @ExcelProperty("XM21")
    private String xm21;

    @Schema(description = "XM31")
    @ExcelProperty("XM31")
    private String xm31;

    @Schema(description = "XM12")
    @ExcelProperty("XM12")
    private String xm12;

    @Schema(description = "XM22")
    @ExcelProperty("XM22")
    private String xm22;

    @Schema(description = "XM32")
    @ExcelProperty("XM32")
    private String xm32;

    @Schema(description = "XM13")
    @ExcelProperty("XM13")
    private String xm13;

    @Schema(description = "XM23")
    @ExcelProperty("XM23")
    private String xm23;

    @Schema(description = "XM33")
    @ExcelProperty("XM33")
    private String xm33;

    @Schema(description = "XM14")
    @ExcelProperty("XM14")
    private String xm14;

    @Schema(description = "XM24")
    @ExcelProperty("XM24")
    private String xm24;

    @Schema(description = "XM34")
    @ExcelProperty("XM34")
    private String xm34;

    @Schema(description = "XM15")
    @ExcelProperty("XM15")
    private String xm15;

    @Schema(description = "XM25")
    @ExcelProperty("XM25")
    private String xm25;

    @Schema(description = "XM35")
    @ExcelProperty("XM35")
    private String xm35;

    @Schema(description = "XM16")
    @ExcelProperty("XM16")
    private String xm16;

    @Schema(description = "XM26")
    @ExcelProperty("XM26")
    private String xm26;

    @Schema(description = "XM36")
    @ExcelProperty("XM36")
    private String xm36;

    @Schema(description = "XM17")
    @ExcelProperty("XM17")
    private String xm17;

    @Schema(description = "XM27")
    @ExcelProperty("XM27")
    private String xm27;

    @Schema(description = "XM37")
    @ExcelProperty("XM37")
    private String xm37;

    @Schema(description = "XM18")
    @ExcelProperty("XM18")
    private String xm18;

    @Schema(description = "XM28")
    @ExcelProperty("XM28")
    private String xm28;

    @Schema(description = "XM38")
    @ExcelProperty("XM38")
    private String xm38;

    @Schema(description = "碱含量")
    @ExcelProperty("碱含量")
    private BigDecimal alkaliContent;

    @Schema(description = "氯离子")
    @ExcelProperty("氯离子")
    private BigDecimal chlorideIon;

    @Schema(description = "附件状态", example = "1")
    @ExcelProperty("附件状态")
    private String attachmentStatus;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String modifier;

    @Schema(description = "时间")
    @ExcelProperty("时间")
    private LocalDateTime modifyTime;

    @Schema(description = "XD11")
    @ExcelProperty("XD11")
    private Double xd11;

    @Schema(description = "XD12")
    @ExcelProperty("XD12")
    private Double xd12;

    @Schema(description = "XD筛余1")
    @ExcelProperty("XD筛余1")
    private BigDecimal xdResidue1;

    @Schema(description = "XD校正")
    @ExcelProperty("XD校正")
    private Double xdCorrection;

    @Schema(description = "XD21")
    @ExcelProperty("XD21")
    private Double xd21;

    @Schema(description = "XD22")
    @ExcelProperty("XD22")
    private Double xd22;

    @Schema(description = "XD筛余2")
    @ExcelProperty("XD筛余2")
    private BigDecimal xdResidue2;

    @Schema(description = "XDV")
    @ExcelProperty("XDV")
    private BigDecimal xdV;

    @Schema(description = "XS11")
    @ExcelProperty("XS11")
    private Integer xs11;

    @Schema(description = "XS12")
    @ExcelProperty("XS12")
    private Integer xs12;

    @Schema(description = "XS13")
    @ExcelProperty("XS13")
    private Integer xs13;

    @Schema(description = "XS14")
    @ExcelProperty("XS14")
    private Integer xs14;

    @Schema(description = "XSL1")
    @ExcelProperty("XSL1")
    private Integer xsL1;

    @Schema(description = "XS21")
    @ExcelProperty("XS21")
    private Integer xs21;

    @Schema(description = "XS22")
    @ExcelProperty("XS22")
    private Integer xs22;

    @Schema(description = "XS23")
    @ExcelProperty("XS23")
    private Integer xs23;

    @Schema(description = "XSL2")
    @ExcelProperty("XSL2")
    private Integer xsL2;

    @Schema(description = "XSLB")
    @ExcelProperty("XSLB")
    private Integer xsLb;

    @Schema(description = "SS11")
    @ExcelProperty("SS11")
    private BigDecimal ss11;

    @Schema(description = "SS12")
    @ExcelProperty("SS12")
    private BigDecimal ss12;

    @Schema(description = "SS13")
    @ExcelProperty("SS13")
    private BigDecimal ss13;

    @Schema(description = "SS14")
    @ExcelProperty("SS14")
    private BigDecimal ss14;

    @Schema(description = "SS1烧前重")
    @ExcelProperty("SS1烧前重")
    private BigDecimal ss1PreWeight;

    @Schema(description = "SS1合重")
    @ExcelProperty("SS1合重")
    private BigDecimal ss1TotalWeight;

    @Schema(description = "SS15")
    @ExcelProperty("SS15")
    private BigDecimal ss15;

    @Schema(description = "SS16")
    @ExcelProperty("SS16")
    private BigDecimal ss16;

    @Schema(description = "SS17")
    @ExcelProperty("SS17")
    private BigDecimal ss17;

    @Schema(description = "SS18")
    @ExcelProperty("SS18")
    private BigDecimal ss18;

    @Schema(description = "SS1烧后重")
    @ExcelProperty("SS1烧后重")
    private BigDecimal ss1PostWeight;

    @Schema(description = "SSL1")
    @ExcelProperty("SSL1")
    private BigDecimal ssL1;

    @Schema(description = "SS21")
    @ExcelProperty("SS21")
    private BigDecimal ss21;

    @Schema(description = "SS22")
    @ExcelProperty("SS22")
    private BigDecimal ss22;

    @Schema(description = "SS23")
    @ExcelProperty("SS23")
    private BigDecimal ss23;

    @Schema(description = "SS24")
    @ExcelProperty("SS24")
    private BigDecimal ss24;

    @Schema(description = "SS2烧前重")
    @ExcelProperty("SS2烧前重")
    private BigDecimal ss2PreWeight;

    @Schema(description = "SS2合重")
    @ExcelProperty("SS2合重")
    private BigDecimal ss2TotalWeight;

    @Schema(description = "SS2烧后重")
    @ExcelProperty("SS2烧后重")
    private BigDecimal ss2PostWeight;

    @Schema(description = "SS25")
    @ExcelProperty("SS25")
    private BigDecimal ss25;

    @Schema(description = "SS26")
    @ExcelProperty("SS26")
    private BigDecimal ss26;

    @Schema(description = "SS27")
    @ExcelProperty("SS27")
    private BigDecimal ss27;

    @Schema(description = "SS28")
    @ExcelProperty("SS28")
    private BigDecimal ss28;

    @Schema(description = "SSL2")
    @ExcelProperty("SSL2")
    private BigDecimal ssL2;

    @Schema(description = "执行标准")
    @ExcelProperty("执行标准")
    private String standard;

    @Schema(description = "试验状态", example = "2")
    @ExcelProperty("试验状态")
    private String testStatus;

    @Schema(description = "使用状态", example = "1")
    @ExcelProperty("使用状态")
    private String usageStatus;

    @Schema(description = "选择")
    @ExcelProperty("选择")
    private Integer selected;

    @Schema(description = "打印")
    @ExcelProperty("打印")
    private Integer printed;

    @Schema(description = "Y")
    @ExcelProperty("Y")
    private Integer yValue;

    @Schema(description = "站别")
    @ExcelProperty("站别")
    private String stationCode;

    @Schema(description = "试验人2")
    @ExcelProperty("试验人2")
    private String tester2;

    @Schema(description = "执行标准2")
    @ExcelProperty("执行标准2")
    private String standard2;

    @Schema(description = "工程类型", example = "1")
    @ExcelProperty("工程类型")
    private String projectType;

    @Schema(description = "厂家牌号")
    @ExcelProperty("厂家牌号")
    private String factoryBrand;

    @Schema(description = "活性指数7")
    @ExcelProperty("活性指数7")
    private Integer activityIndex7d;

    @Schema(description = "活性指数28")
    @ExcelProperty("活性指数28")
    private Integer activityIndex28d;

    @Schema(description = "试验状态7天")
    @ExcelProperty("试验状态7天")
    private LocalDateTime testStatus7d;

    @Schema(description = "试验状态28天")
    @ExcelProperty("试验状态28天")
    private LocalDateTime testStatus28d;

    @Schema(description = "荷载71")
    @ExcelProperty("荷载71")
    private BigDecimal load71;

    @Schema(description = "荷载72")
    @ExcelProperty("荷载72")
    private BigDecimal load72;

    @Schema(description = "荷载73")
    @ExcelProperty("荷载73")
    private BigDecimal load73;

    @Schema(description = "荷载74")
    @ExcelProperty("荷载74")
    private BigDecimal load74;

    @Schema(description = "荷载75")
    @ExcelProperty("荷载75")
    private BigDecimal load75;

    @Schema(description = "荷载76")
    @ExcelProperty("荷载76")
    private BigDecimal load76;

    @Schema(description = "抗压71")
    @ExcelProperty("抗压71")
    private BigDecimal compressive71;

    @Schema(description = "抗压72")
    @ExcelProperty("抗压72")
    private BigDecimal compressive72;

    @Schema(description = "抗压73")
    @ExcelProperty("抗压73")
    private BigDecimal compressive73;

    @Schema(description = "抗压74")
    @ExcelProperty("抗压74")
    private BigDecimal compressive74;

    @Schema(description = "抗压75")
    @ExcelProperty("抗压75")
    private BigDecimal compressive75;

    @Schema(description = "抗压76")
    @ExcelProperty("抗压76")
    private BigDecimal compressive76;

    @Schema(description = "抗压7V")
    @ExcelProperty("抗压7V")
    private BigDecimal compressive7v;

    @Schema(description = "基准荷载71")
    @ExcelProperty("基准荷载71")
    private BigDecimal baseLoad71;

    @Schema(description = "基准荷载72")
    @ExcelProperty("基准荷载72")
    private BigDecimal baseLoad72;

    @Schema(description = "基准荷载73")
    @ExcelProperty("基准荷载73")
    private BigDecimal baseLoad73;

    @Schema(description = "基准荷载74")
    @ExcelProperty("基准荷载74")
    private BigDecimal baseLoad74;

    @Schema(description = "基准荷载75")
    @ExcelProperty("基准荷载75")
    private BigDecimal baseLoad75;

    @Schema(description = "基准荷载76")
    @ExcelProperty("基准荷载76")
    private BigDecimal baseLoad76;

    @Schema(description = "基准抗压71")
    @ExcelProperty("基准抗压71")
    private BigDecimal baseCompressive71;

    @Schema(description = "基准抗压72")
    @ExcelProperty("基准抗压72")
    private BigDecimal baseCompressive72;

    @Schema(description = "基准抗压73")
    @ExcelProperty("基准抗压73")
    private BigDecimal baseCompressive73;

    @Schema(description = "基准抗压74")
    @ExcelProperty("基准抗压74")
    private BigDecimal baseCompressive74;

    @Schema(description = "基准抗压75")
    @ExcelProperty("基准抗压75")
    private BigDecimal baseCompressive75;

    @Schema(description = "基准抗压76")
    @ExcelProperty("基准抗压76")
    private BigDecimal baseCompressive76;

    @Schema(description = "基准抗压7V")
    @ExcelProperty("基准抗压7V")
    private BigDecimal baseCompressive7v;

    @Schema(description = "基准荷载281")
    @ExcelProperty("基准荷载281")
    private BigDecimal baseLoad281;

    @Schema(description = "基准荷载282")
    @ExcelProperty("基准荷载282")
    private BigDecimal baseLoad282;

    @Schema(description = "基准荷载283")
    @ExcelProperty("基准荷载283")
    private BigDecimal baseLoad283;

    @Schema(description = "基准荷载284")
    @ExcelProperty("基准荷载284")
    private BigDecimal baseLoad284;

    @Schema(description = "基准荷载285")
    @ExcelProperty("基准荷载285")
    private BigDecimal baseLoad285;

    @Schema(description = "基准荷载286")
    @ExcelProperty("基准荷载286")
    private BigDecimal baseLoad286;

    @Schema(description = "荷载281")
    @ExcelProperty("荷载281")
    private BigDecimal load281;

    @Schema(description = "荷载282")
    @ExcelProperty("荷载282")
    private BigDecimal load282;

    @Schema(description = "荷载283")
    @ExcelProperty("荷载283")
    private BigDecimal load283;

    @Schema(description = "荷载284")
    @ExcelProperty("荷载284")
    private BigDecimal load284;

    @Schema(description = "荷载285")
    @ExcelProperty("荷载285")
    private BigDecimal load285;

    @Schema(description = "荷载286")
    @ExcelProperty("荷载286")
    private BigDecimal load286;

    @Schema(description = "抗压281")
    @ExcelProperty("抗压281")
    private BigDecimal compressive281;

    @Schema(description = "抗压282")
    @ExcelProperty("抗压282")
    private BigDecimal compressive282;

    @Schema(description = "抗压283")
    @ExcelProperty("抗压283")
    private BigDecimal compressive283;

    @Schema(description = "抗压284")
    @ExcelProperty("抗压284")
    private BigDecimal compressive284;

    @Schema(description = "抗压285")
    @ExcelProperty("抗压285")
    private BigDecimal compressive285;

    @Schema(description = "抗压286")
    @ExcelProperty("抗压286")
    private BigDecimal compressive286;

    @Schema(description = "抗压28V")
    @ExcelProperty("抗压28V")
    private BigDecimal compressive28v;

    @Schema(description = "基准抗压281")
    @ExcelProperty("基准抗压281")
    private BigDecimal baseCompressive281;

    @Schema(description = "基准抗压282")
    @ExcelProperty("基准抗压282")
    private BigDecimal baseCompressive282;

    @Schema(description = "基准抗压283")
    @ExcelProperty("基准抗压283")
    private BigDecimal baseCompressive283;

    @Schema(description = "基准抗压284")
    @ExcelProperty("基准抗压284")
    private BigDecimal baseCompressive284;

    @Schema(description = "基准抗压285")
    @ExcelProperty("基准抗压285")
    private BigDecimal baseCompressive285;

    @Schema(description = "基准抗压286")
    @ExcelProperty("基准抗压286")
    private BigDecimal baseCompressive286;

    @Schema(description = "基准抗压28V")
    @ExcelProperty("基准抗压28V")
    private BigDecimal baseCompressive28v;

    @Schema(description = "BBMJV")
    @ExcelProperty("BBMJV")
    private String bbmjv;

    @Schema(description = "HS1")
    @ExcelProperty("HS1")
    private BigDecimal hs1;

    @Schema(description = "HS2")
    @ExcelProperty("HS2")
    private BigDecimal hs2;

    @Schema(description = "HS3")
    @ExcelProperty("HS3")
    private BigDecimal hs3;

    @Schema(description = "HSL")
    @ExcelProperty("HSL")
    private BigDecimal hsL;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}