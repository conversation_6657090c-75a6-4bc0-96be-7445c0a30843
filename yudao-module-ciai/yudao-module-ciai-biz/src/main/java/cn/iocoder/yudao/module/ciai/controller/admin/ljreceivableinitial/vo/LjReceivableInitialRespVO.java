package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableinitial.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 应收期初账款 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LjReceivableInitialRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8417")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "工程ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5063")
    @ExcelProperty("工程ID")
    private Long projectId;

    @Schema(description = "累计发生数量")
    @ExcelProperty("累计发生数量")
    private BigDecimal accumulatedOccurrenceQuantity;

    @Schema(description = "累计发生金额")
    @ExcelProperty("累计发生金额")
    private BigDecimal accumulatedOccurrenceAmount;

    @Schema(description = "累计回款金额")
    @ExcelProperty("累计回款金额")
    private BigDecimal accumulatedCollectionAmount;

    @Schema(description = "累计发票金额")
    @ExcelProperty("累计发票金额")
    private BigDecimal accumulatedInvoiceAmount;

    @Schema(description = "累计认签数量")
    @ExcelProperty("累计认签数量")
    private BigDecimal accumulatedRecognitionQuantity;

    @Schema(description = "累计认签金额")
    @ExcelProperty("累计认签金额")
    private BigDecimal accumulatedRecognitionAmount;

    @Schema(description = "会计期")
    @ExcelProperty("会计期")
    private String accountingPeriod;

    @Schema(description = "期初日期")
    @ExcelProperty("期初日期")
    private LocalDateTime initialDate;

}