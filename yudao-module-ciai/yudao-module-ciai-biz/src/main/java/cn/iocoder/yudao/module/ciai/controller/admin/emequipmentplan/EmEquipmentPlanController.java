package cn.iocoder.yudao.module.ciai.controller.admin.emequipmentplan;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentplan.EmEquipmentPlanDO;
import cn.iocoder.yudao.module.ciai.service.emequipmentplan.EmEquipmentPlanService;

@Tag(name = "管理后台 - 设备管理_设备计划")
@RestController
@RequestMapping("/ciai/em-equipment-plan")
@Validated
public class EmEquipmentPlanController {

    @Resource
    private EmEquipmentPlanService emEquipmentPlanService;

    @PostMapping("/create")
    @Operation(summary = "创建设备管理_设备计划")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:create')")
    public CommonResult<Long> createEmEquipmentPlan(@Valid @RequestBody EmEquipmentPlanSaveReqVO createReqVO) {
        return success(emEquipmentPlanService.createEmEquipmentPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备管理_设备计划")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:update')")
    public CommonResult<Boolean> updateEmEquipmentPlan(@Valid @RequestBody EmEquipmentPlanSaveReqVO updateReqVO) {
        emEquipmentPlanService.updateEmEquipmentPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备管理_设备计划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:delete')")
    public CommonResult<Boolean> deleteEmEquipmentPlan(@RequestParam("id") Long id) {
        emEquipmentPlanService.deleteEmEquipmentPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备管理_设备计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:query')")
    public CommonResult<EmEquipmentPlanRespVO> getEmEquipmentPlan(@RequestParam("id") Long id) {
        EmEquipmentPlanDO emEquipmentPlan = emEquipmentPlanService.getEmEquipmentPlan(id);
        return success(BeanUtils.toBean(emEquipmentPlan, EmEquipmentPlanRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备管理_设备计划分页")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:query')")
    public CommonResult<PageResult<EmEquipmentPlanRespVO>> getEmEquipmentPlanPage(@Valid EmEquipmentPlanPageReqVO pageReqVO) {
        PageResult<EmEquipmentPlanDO> pageResult = emEquipmentPlanService.getEmEquipmentPlanPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmEquipmentPlanRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备管理_设备计划 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:em-equipment-plan:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmEquipmentPlanExcel(@Valid EmEquipmentPlanPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmEquipmentPlanDO> list = emEquipmentPlanService.getEmEquipmentPlanPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "设备管理_设备计划.xls", "数据", EmEquipmentPlanRespVO.class,
                        BeanUtils.toBean(list, EmEquipmentPlanRespVO.class));
    }


}