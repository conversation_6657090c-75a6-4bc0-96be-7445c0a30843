package cn.iocoder.yudao.module.ciai.service.immeasureunit;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.ciai.controller.admin.immeasureunit.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.immeasureunit.ImMeasureUnitDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.immeasureunit.ImMeasureUnitMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 库存管理_计量单位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImMeasureUnitServiceImpl implements ImMeasureUnitService {

    @Resource
    private ImMeasureUnitMapper imMeasureUnitMapper;

    @Override
    public Long createImMeasureUnit(ImMeasureUnitSaveReqVO createReqVO) {
        // 插入
        ImMeasureUnitDO imMeasureUnit = BeanUtils.toBean(createReqVO, ImMeasureUnitDO.class);
        imMeasureUnitMapper.insert(imMeasureUnit);
        // 返回
        return imMeasureUnit.getId();
    }

    @Override
    public void updateImMeasureUnit(ImMeasureUnitSaveReqVO updateReqVO) {
        // 校验存在
        validateImMeasureUnitExists(updateReqVO.getId());
        // 更新
        ImMeasureUnitDO updateObj = BeanUtils.toBean(updateReqVO, ImMeasureUnitDO.class);
        imMeasureUnitMapper.updateById(updateObj);
    }

    @Override
    public void deleteImMeasureUnit(Long id) {
        // 校验存在
        validateImMeasureUnitExists(id);
        // 删除
        imMeasureUnitMapper.deleteById(id);
    }

    private void validateImMeasureUnitExists(Long id) {
        if (imMeasureUnitMapper.selectById(id) == null) {
            throw exception(IM_MEASURE_UNIT_NOT_EXISTS);
        }
    }

    @Override
    public ImMeasureUnitDO getImMeasureUnit(Long id) {
        return imMeasureUnitMapper.selectById(id);
    }

    @Override
    public PageResult<ImMeasureUnitDO> getImMeasureUnitPage(ImMeasureUnitPageReqVO pageReqVO) {
        return imMeasureUnitMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ImMeasureUnitSaveReqVO> getImMeasureUnitList() {
        return imMeasureUnitMapper.selectList().stream()
                .map(e -> BeanUtils.toBean(e, ImMeasureUnitSaveReqVO.class))
                .collect(Collectors.toList());
    }

}