package cn.iocoder.yudao.module.ciai.dal.dataobject.iminbord;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 库存管理_入库单 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_im_inb_ord")
@KeySequence("ciai_im_inb_ord_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImInbOrdDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 入库单号
     */
    private String inboundOrderNumber;
    /**
     * 单位ID
     */
    private Long unitId;
    /**
     * 单据类型
     */
    private String documentType;
    /**
     * 入库分类ID
     */
    private Long inboundCategoryId;
    /**
     * 部门ID
     */
    private Long departmentId;
    /**
     * 业务员
     */
    private String salesperson;
    /**
     * 发票号
     */
    private String invoiceNumber;
    /**
     * 源单号
     */
    private String sourceDocumentNumber;
    /**
     * 会计期
     */
    private String accountingPeriod;
    /**
     * 入库日期
     */
    private LocalDateTime inboundDate;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 审核日期
     */
    private LocalDateTime reviewDate;
    /**
     * 批准人
     */
    private String approver;
    /**
     * 批准日期
     */
    private LocalDateTime approveDate;
    /**
     * 备注
     */
    private String remarks;

    // 附件
    private String documentAttachment;

}