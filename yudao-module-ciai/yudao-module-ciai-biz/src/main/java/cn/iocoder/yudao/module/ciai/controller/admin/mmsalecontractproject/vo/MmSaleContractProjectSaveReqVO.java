package cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontractproject.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 工程信息新增/修改 Request VO")
@Data
public class MmSaleContractProjectSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "工程名称")
    private String projectName;

    @Schema(description = "工程简称")
    private String projectShortName;

    @Schema(description = "工程地址")
    private String projectAddress;

    @Schema(description = "工程代码")
    private String projectCode;

    @Schema(description = "工程备注")
    private String projectRemark;

    @Schema(description = "是否需要资料")
    private Integer isNeedMaterial;

    @Schema(description = "销售合同ID")
    private Long salesContractId;

}