package cn.iocoder.yudao.module.ciai.controller.admin.standardmixratio;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.standardmixratio.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.standardmixratio.StandardMixRatioDO;
import cn.iocoder.yudao.module.ciai.service.standardmixratio.StandardMixRatioService;

@Tag(name = "管理后台 - 技术管理_标准配合比库")
@RestController
@RequestMapping("/ciai/standard-mix-ratio")
@Validated
public class StandardMixRatioController {

    @Resource
    private StandardMixRatioService standardMixRatioService;

    @PostMapping("/create")
    @Operation(summary = "创建技术管理_标准配合比库")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:create')")
    public CommonResult<Long> createStandardMixRatio(@Valid @RequestBody StandardMixRatioSaveReqVO createReqVO) {
        return success(standardMixRatioService.createStandardMixRatio(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新技术管理_标准配合比库")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:update')")
    public CommonResult<Boolean> updateStandardMixRatio(@Valid @RequestBody StandardMixRatioSaveReqVO updateReqVO) {
        standardMixRatioService.updateStandardMixRatio(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除技术管理_标准配合比库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:delete')")
    public CommonResult<Boolean> deleteStandardMixRatio(@RequestParam("id") Long id) {
        standardMixRatioService.deleteStandardMixRatio(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得技术管理_标准配合比库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:query')")
    public CommonResult<StandardMixRatioRespVO> getStandardMixRatio(@RequestParam("id") Long id) {
        StandardMixRatioDO standardMixRatio = standardMixRatioService.getStandardMixRatio(id);
        return success(BeanUtils.toBean(standardMixRatio, StandardMixRatioRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得技术管理_标准配合比库分页")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:query')")
    public CommonResult<PageResult<StandardMixRatioRespVO>> getStandardMixRatioPage(@Valid StandardMixRatioPageReqVO pageReqVO) {
        PageResult<StandardMixRatioDO> pageResult = standardMixRatioService.getStandardMixRatioPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, StandardMixRatioRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出技术管理_标准配合比库 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStandardMixRatioExcel(@Valid StandardMixRatioPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<StandardMixRatioDO> list = standardMixRatioService.getStandardMixRatioPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "技术管理_标准配合比库.xls", "数据", StandardMixRatioRespVO.class,
                        BeanUtils.toBean(list, StandardMixRatioRespVO.class));
    }

    @GetMapping("/kinds")
    @Operation(summary = "查询物资种类规格ID")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:query')")
    public CommonResult<Long> getKinds(@RequestParam("kinds") String kinds) {
        Long id = standardMixRatioService.getKindsService(kinds);
        return success(id);
    }

    @GetMapping("/specs")
    @Operation(summary = "查询物资种类规格")
    @PreAuthorize("@ss.hasPermission('ciai:standard-mix-ratio:query')")
    public CommonResult<List<JsSpecsVO>> getKinds(@RequestParam("id") Long id) {
        List<JsSpecsVO> list = standardMixRatioService.getSpecsService(id);
        return success(BeanUtils.toBean(list, JsSpecsVO.class));
    }

}