package cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseinbound;

import lombok.Data;

@Data
public class WzInboundExceptionMessageDO {
    /**
     * 车辆牌号
     */
    private String vehicleNumber;

    /**
     * 物料名称（原材料类型名称 + 物料类型 + 规格）
     */
    private String materialName;

    /**
     * 料仓名称
     */
    private String warehouseName;

    /**
     * 停留时长（分钟）
     */
    private Integer duration;

}
