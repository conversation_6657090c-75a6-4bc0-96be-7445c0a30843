package cn.iocoder.yudao.module.ciai.dal.mysql.ycsubjectcode;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ycsubjectcode.YcSubjectCodeDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.ycsubjectcode.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 科目代码 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YcSubjectCodeMapper extends BaseMapperX<YcSubjectCodeDO> {

    default PageResult<YcSubjectCodeDO> selectPage(YcSubjectCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<YcSubjectCodeDO>()
                .likeIfPresent(YcSubjectCodeDO::getSubjectCategory, reqVO.getSubjectCategory())
                .likeIfPresent(YcSubjectCodeDO::getAccountingSubjectCode, reqVO.getAccountingSubjectCode())
                .likeIfPresent(YcSubjectCodeDO::getSubjectName, reqVO.getSubjectName())
                .eqIfPresent(YcSubjectCodeDO::getBalanceDirection, reqVO.getBalanceDirection())
                .orderByDesc(YcSubjectCodeDO::getId));
    }

    /**
     * 根据科目编码前缀查询
     * @param accountingSubjectCode
     * @return
     */
    List<YcSubjectCodeDO> findByAccountingSubjectCodeStartingWith(@Param("accountingSubjectCode") String accountingSubjectCode);

    @Select("SELECT accounting_subject_code FROM ciai_yc_subject_code WHERE subject_name = #{expenseType} AND deleted = 0")
    String getSubjectPrefixByExpenseType(String expenseType);

    List<YcSubjectCodeDO> findFirstLevelSubjects();

    /**
     * 根据业务类型（费用类型）查询默认的贷方科目ID
     *
     * @param expenseType 费用类型
     * @return 对应的贷方科目ID，如果未找到则返回 null
     */
    Long selectCreditSubjectIdByBusinessType(@Param("expenseType") String expenseType);
}