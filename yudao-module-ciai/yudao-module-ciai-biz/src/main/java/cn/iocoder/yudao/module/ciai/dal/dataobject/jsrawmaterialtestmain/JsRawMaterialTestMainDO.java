package cn.iocoder.yudao.module.ciai.dal.dataobject.jsrawmaterialtestmain;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 原材试验 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_js_raw_material_test_commission")
@KeySequence("ciai_js_raw_material_test_commission_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JsRawMaterialTestMainDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 委托编号
     */
    private String commissionNo;
    /**
     * 委托单位
     */
    private String commissionUnit;
    /**
     * 原材类型ID
     */
    private Long rawMaterialTypeId;
    /**
     * 原材类型
     */
    private String rawMaterialType;
    /**
     * 种类ID
     */
    private Long typeId;
    /**
     * 种类
     */
    private String type;
    /**
     * 规格
     */
    private String specification;
    /**
     * 产地
     */
    private String origin;
    /**
     * 执行标准
     */
    private String executionStandard;
    /**
     * 执行标准2
     */
    private String executionStandard2;
    /**
     * 委托人
     */
    private String client;
    /**
     * 接收人
     */
    private String receiver;
    /**
     * 委托日期
     */
    private LocalDateTime commissionDate;
    /**
     * 接收日期
     */
    private LocalDateTime receiveDate;
    /**
     * 检验项目
     */
    private String inspectionItems;
    /**
     * 代表数量
     */
    private String representativeQuantity;
    /**
     * 出厂批号
     */
    private String factoryBatchNo;
    /**
     * 出厂日期
     */
    private LocalDateTime factoryDate;
    /**
     * 入库日期
     */
    private LocalDateTime storageDate;
    /**
     * 氯离子含量
     */
    private Double chlorideContent;
    /**
     * 碱含量
     */
    private Double alkaliContent;
    /**
     * 试验状态
     */
    private String testStatus;
    /**
     * 启用状态 (1:启用 0:停用)
     */
    private Boolean enabled;

}