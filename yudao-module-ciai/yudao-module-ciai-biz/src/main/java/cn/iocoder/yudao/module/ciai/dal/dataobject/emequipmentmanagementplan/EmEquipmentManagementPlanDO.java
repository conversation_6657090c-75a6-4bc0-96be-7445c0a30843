package cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentmanagementplan;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 方案描述 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_em_equipment_management_plan")
@KeySequence("ciai_em_equipment_management_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmEquipmentManagementPlanDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 方案名称
     */
    private String name;
    /**
     * 时间计划
     *
     * 枚举 {@link TODO em_time_plan 对应的类}
     */
    private Long timePlanId;
    /**
     * 内容计划
     */
    private Long contentPlanId;
    /**
     * 方案描述
     */
    private String remark;
    /**
     * 启用状态
     */
    private Boolean enabled;

}