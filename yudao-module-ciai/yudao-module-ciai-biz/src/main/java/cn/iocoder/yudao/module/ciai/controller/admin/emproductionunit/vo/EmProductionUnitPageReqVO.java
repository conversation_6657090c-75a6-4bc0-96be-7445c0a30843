package cn.iocoder.yudao.module.ciai.controller.admin.emproductionunit.vo;

import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备管理_生产机组分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EmProductionUnitPageReqVO extends PageParam {

    @Schema(description = "机组名称")
    private String unitName;

    @Schema(description = "每盘方量")
    private Double perBatchVolume;

    @Schema(description = "采集时间")
    private LocalDateTime collectionTime;
}