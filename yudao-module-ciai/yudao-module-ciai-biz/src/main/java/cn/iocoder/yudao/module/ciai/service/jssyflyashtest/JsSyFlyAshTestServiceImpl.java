package cn.iocoder.yudao.module.ciai.service.jssyflyashtest;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.jssyflyashtest.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssyflyashtest.JsSyFlyAshTestDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.jssyflyashtest.JsSyFlyAshTestMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 粉煤灰试验 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JsSyFlyAshTestServiceImpl implements JsSyFlyAshTestService {

    @Resource
    private JsSyFlyAshTestMapper jsSyFlyAshTestMapper;

    @Override
    public Long createJsSyFlyAshTest(JsSyFlyAshTestSaveReqVO createReqVO) {
        // 插入
        JsSyFlyAshTestDO jsSyFlyAshTest = BeanUtils.toBean(createReqVO, JsSyFlyAshTestDO.class);
        jsSyFlyAshTestMapper.insert(jsSyFlyAshTest);
        // 返回
        return jsSyFlyAshTest.getId();
    }

    @Override
    public void updateJsSyFlyAshTest(JsSyFlyAshTestSaveReqVO updateReqVO) {
        // 校验存在
        validateJsSyFlyAshTestExists(updateReqVO.getId());
        // 更新
        JsSyFlyAshTestDO updateObj = BeanUtils.toBean(updateReqVO, JsSyFlyAshTestDO.class);
        jsSyFlyAshTestMapper.updateById(updateObj);
    }

    @Override
    public void deleteJsSyFlyAshTest(Long id) {
        // 校验存在
        validateJsSyFlyAshTestExists(id);
        // 删除
        jsSyFlyAshTestMapper.deleteById(id);
    }

    private void validateJsSyFlyAshTestExists(Long id) {
        if (jsSyFlyAshTestMapper.selectById(id) == null) {
            throw exception(JS_SY_FLY_ASH_TEST_NOT_EXISTS);
        }
    }

    @Override
    public JsSyFlyAshTestDO getJsSyFlyAshTest(Long id) {
        return jsSyFlyAshTestMapper.selectById(id);
    }

    @Override
    public PageResult<JsSyFlyAshTestDO> getJsSyFlyAshTestPage(JsSyFlyAshTestPageReqVO pageReqVO) {
        return jsSyFlyAshTestMapper.selectPage(pageReqVO);
    }

}