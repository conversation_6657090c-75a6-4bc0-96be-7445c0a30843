package cn.iocoder.yudao.module.ciai.service.iminborddetail;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.iminborddetail.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminborddetail.ImInbOrdDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 入库明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ImInbOrdDetailService {

    /**
     * 创建入库明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImInbOrdDetail(@Valid ImInbOrdDetailSaveReqVO createReqVO);

    /**
     * 更新入库明细
     *
     * @param updateReqVO 更新信息
     */
    void updateImInbOrdDetail(@Valid ImInbOrdDetailSaveReqVO updateReqVO);

    /**
     * 删除入库明细
     *
     * @param id 编号
     */
    void deleteImInbOrdDetail(Long id);

    /**
     * 获得入库明细
     *
     * @param id 编号
     * @return 入库明细
     */
    ImInbOrdDetailDO getImInbOrdDetail(Long id);

    /**
     * 获得入库明细分页
     *
     * @param pageReqVO 分页查询
     * @return 入库明细分页
     */
    PageResult<ImInbOrdDetailDO> getImInbOrdDetailPage(ImInbOrdDetailPageReqVO pageReqVO);

}