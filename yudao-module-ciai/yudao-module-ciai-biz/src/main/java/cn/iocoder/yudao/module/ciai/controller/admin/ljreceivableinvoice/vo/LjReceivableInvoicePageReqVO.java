package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableinvoice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 应收发票管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LjReceivableInvoicePageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "工程ID", example = "9651")
    private Long projectId;

    @Schema(description = "开票日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] invoiceDate;

    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    @Schema(description = "开票类别")
    private String invoiceCategory;

    @Schema(description = "纳税人识别号", example = "23712")
    private String taxpayerId;

    @Schema(description = "开户银行", example = "王五")
    private String bankName;

    @Schema(description = "开户地址")
    private String bankAddress;

    @Schema(description = "开户单位")
    private String bankUnit;

    @Schema(description = "开户账号", example = "7515")
    private String bankAccount;

    @Schema(description = "发票张数", example = "14095")
    private Integer invoiceCount;

    @Schema(description = "发票号码")
    private String invoiceNumber;

    @Schema(description = "发票代码")
    private String invoiceCode;

    @Schema(description = "发票税点")
    private BigDecimal invoiceTax;

    @Schema(description = "其中金额")
    private BigDecimal amountIncluded;

    @Schema(description = "其中税额")
    private BigDecimal taxIncluded;

    @Schema(description = "开票申请人")
    private String invoiceApplicant;

    @Schema(description = "负责人")
    private String personInCharge;

    @Schema(description = "发票签收人")
    private String invoiceSignatory;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "合同名称")
    private String contractName;

    @Schema(description = "工程名称")
    private String projectName;

}