package cn.iocoder.yudao.module.ciai.controller.admin.gkproductionconsumption;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.gkproductionconsumption.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionconsumption.GkProductionConsumptionDO;
import cn.iocoder.yudao.module.ciai.service.gkproductionconsumption.GkProductionConsumptionService;

@Tag(name = "管理后台 - 生产消耗")
@RestController
@RequestMapping("/ciai/gk-production-consumption")
@Validated
public class GkProductionConsumptionController {

    @Resource
    private GkProductionConsumptionService gkProductionConsumptionService;

    @PostMapping("/create")
    @Operation(summary = "创建生产消耗")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:create')")
    public CommonResult<Long> createGkProductionConsumption(@Valid @RequestBody GkProductionConsumptionSaveReqVO createReqVO) {
        return success(gkProductionConsumptionService.createGkProductionConsumption(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新生产消耗")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:update')")
    public CommonResult<Boolean> updateGkProductionConsumption(@Valid @RequestBody GkProductionConsumptionSaveReqVO updateReqVO) {
        gkProductionConsumptionService.updateGkProductionConsumption(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除生产消耗")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:delete')")
    public CommonResult<Boolean> deleteGkProductionConsumption(@RequestParam("id") Long id) {
        gkProductionConsumptionService.deleteGkProductionConsumption(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得生产消耗")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:query')")
    public CommonResult<GkProductionConsumptionRespVO> getGkProductionConsumption(@RequestParam("id") Long id) {
        GkProductionConsumptionDO gkProductionConsumption = gkProductionConsumptionService.getGkProductionConsumption(id);
        return success(BeanUtils.toBean(gkProductionConsumption, GkProductionConsumptionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得生产消耗分页")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:query')")
    public CommonResult<PageResult<GkProductionConsumptionRespVO>> getGkProductionConsumptionPage(@Valid GkProductionConsumptionPageReqVO pageReqVO) {
        PageResult<GkProductionConsumptionDO> pageResult = gkProductionConsumptionService.getGkProductionConsumptionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GkProductionConsumptionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出生产消耗 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-consumption:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGkProductionConsumptionExcel(@Valid GkProductionConsumptionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GkProductionConsumptionDO> list = gkProductionConsumptionService.getGkProductionConsumptionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "生产消耗.xls", "数据", GkProductionConsumptionRespVO.class,
                        BeanUtils.toBean(list, GkProductionConsumptionRespVO.class));
    }

}