package cn.iocoder.yudao.module.ciai.dal.mysql.imoutbord;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.controller.admin.iminbord.vo.ImInbOrdDetailRespVO;
import cn.iocoder.yudao.module.ciai.controller.admin.iminbord.vo.ImInbOrdPageReqVO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminbord.ImInbOrdDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.imoutbord.ImOutbOrdDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.imoutbord.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 领用出库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImOutbOrdMapper extends BaseMapperX<ImOutbOrdDO> {

    default PageResult<ImOutbOrdDO> selectPage(ImOutbOrdPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImOutbOrdDO>()
                .betweenIfPresent(ImOutbOrdDO::getOutboundTime, reqVO.getOutboundTime())
                .orderByDesc(ImOutbOrdDO::getId));
    }

    IPage<ImOutbOrdDetailRespVO> selectDetailPage(@Param("page") Page<?> page,@Param("reqVO") ImOutbOrdPageReqVO reqVO);

    @Select("SELECT MAX(outbound_order_number) FROM ciai_im_outb_ord WHERE outbound_order_number LIKE #{pattern}")
    String selectMaxOrderNoByDate(String s);

    /**
     * 获取可用库存物料列表
     * @return
     */
    List<ImOutbOrdMaterialRespVO> selectMaterialList();
}