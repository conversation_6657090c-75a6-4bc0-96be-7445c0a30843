package cn.iocoder.yudao.module.ciai.controller.admin.ycsubjectcode;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.ycsubjectcode.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ycsubjectcode.YcSubjectCodeDO;
import cn.iocoder.yudao.module.ciai.service.ycsubjectcode.YcSubjectCodeService;

@Tag(name = "管理后台 - 科目代码")
@RestController
@RequestMapping("/ciai/yc-subject-code")
@Validated
public class YcSubjectCodeController {

    @Resource
    private YcSubjectCodeService ycSubjectCodeService;

    @PostMapping("/create")
    @Operation(summary = "创建科目代码")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:create')")
    public CommonResult<Long> createYcSubjectCode(@Valid @RequestBody YcSubjectCodeSaveReqVO createReqVO) {
        return success(ycSubjectCodeService.createYcSubjectCode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新科目代码")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:update')")
    public CommonResult<Boolean> updateYcSubjectCode(@Valid @RequestBody YcSubjectCodeSaveReqVO updateReqVO) {
        ycSubjectCodeService.updateYcSubjectCode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除科目代码")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:delete')")
    public CommonResult<Boolean> deleteYcSubjectCode(@RequestParam("id") Long id) {
        ycSubjectCodeService.deleteYcSubjectCode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得科目代码")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:query')")
    public CommonResult<YcSubjectCodeRespVO> getYcSubjectCode(@RequestParam("id") Long id) {
        YcSubjectCodeDO ycSubjectCode = ycSubjectCodeService.getYcSubjectCode(id);
        return success(BeanUtils.toBean(ycSubjectCode, YcSubjectCodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得科目代码分页")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:query')")
    public CommonResult<PageResult<YcSubjectCodeRespVO>> getYcSubjectCodePage(@Valid YcSubjectCodePageReqVO pageReqVO) {
        PageResult<YcSubjectCodeDO> pageResult = ycSubjectCodeService.getYcSubjectCodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, YcSubjectCodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出科目代码 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportYcSubjectCodeExcel(@Valid YcSubjectCodePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<YcSubjectCodeDO> list = ycSubjectCodeService.getYcSubjectCodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "科目代码.xls", "数据", YcSubjectCodeRespVO.class,
                        BeanUtils.toBean(list, YcSubjectCodeRespVO.class));
    }

    /**
     * 获取科目代码树
     * @param expenseType
     * @return
     */
    @GetMapping("/tree")
    @Operation(summary = "获取报销树形")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:query')")
    public CommonResult<List<SubjectTreeNode>> getSubjectTree(@RequestParam("expenseType") String expenseType) {
        List<SubjectTreeNode> subjectTreeByExpenseType = ycSubjectCodeService.getSubjectTreeByExpenseType(expenseType);
        return success(subjectTreeByExpenseType);
    }

    /**
     * 获取报销类型列表（一级科目）
     * @return 报销类型列表
     */
    @GetMapping("/expense-types")
    @Operation(summary = "获取报销类型列表")
    @PreAuthorize("@ss.hasPermission('ciai:yc-subject-code:query')")
    public CommonResult<List<SubjectTreeNode>> getExpenseTypes() {
        List<SubjectTreeNode> expenseTypeList = ycSubjectCodeService.getExpenseTypeList();
        return success(expenseTypeList);
    }
}