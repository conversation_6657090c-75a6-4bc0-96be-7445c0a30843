package cn.iocoder.yudao.module.ciai.dal.mysql.ljreceivablecollection;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivablecollection.LjReceivableCollectionDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.ljreceivablecollection.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 应收回款管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LjReceivableCollectionMapper extends BaseMapperX<LjReceivableCollectionDO> {

    default PageResult<LjReceivableCollectionDO> selectPage(LjReceivableCollectionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LjReceivableCollectionDO>()
                .betweenIfPresent(LjReceivableCollectionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(LjReceivableCollectionDO::getProjectId, reqVO.getProjectId())
                .eqIfPresent(LjReceivableCollectionDO::getCollectionAmount, reqVO.getCollectionAmount())
                .eqIfPresent(LjReceivableCollectionDO::getCollectionMethod, reqVO.getCollectionMethod())
                .betweenIfPresent(LjReceivableCollectionDO::getCollectionDate, reqVO.getCollectionDate())
                .eqIfPresent(LjReceivableCollectionDO::getAccountingPeriod, reqVO.getAccountingPeriod())
                .eqIfPresent(LjReceivableCollectionDO::getSalesperson, reqVO.getSalesperson())
                .eqIfPresent(LjReceivableCollectionDO::getPayeeBank, reqVO.getPayeeBank())
                .likeIfPresent(LjReceivableCollectionDO::getPayeeAccountName, reqVO.getPayeeAccountName())
                .eqIfPresent(LjReceivableCollectionDO::getPayeeAccountNumber, reqVO.getPayeeAccountNumber())
                .eqIfPresent(LjReceivableCollectionDO::getPayerBank, reqVO.getPayerBank())
                .likeIfPresent(LjReceivableCollectionDO::getPayerAccountName, reqVO.getPayerAccountName())
                .eqIfPresent(LjReceivableCollectionDO::getPayerAccountNumber, reqVO.getPayerAccountNumber())
                .eqIfPresent(LjReceivableCollectionDO::getBillNumber, reqVO.getBillNumber())
                .eqIfPresent(LjReceivableCollectionDO::getBillType, reqVO.getBillType())
                .eqIfPresent(LjReceivableCollectionDO::getWriteOffAmount, reqVO.getWriteOffAmount())
                .eqIfPresent(LjReceivableCollectionDO::getSettlementStatus, reqVO.getSettlementStatus())
                .eqIfPresent(LjReceivableCollectionDO::getAuditor, reqVO.getAuditor())
                .betweenIfPresent(LjReceivableCollectionDO::getAuditTime, reqVO.getAuditTime())
                .eqIfPresent(LjReceivableCollectionDO::getRemark, reqVO.getRemark())
                .orderByDesc(LjReceivableCollectionDO::getId));
    }

    /**
     * 获取应收回款管理详细信息
     * @param page
     * @param reqVO
     * @return
     */
    IPage<LjReceivableCollectionDetailRespVO> selectDetailPage(@Param("page") Page<?> page, @Param("reqVO") LjReceivableCollectionPageReqVO reqVO);
}