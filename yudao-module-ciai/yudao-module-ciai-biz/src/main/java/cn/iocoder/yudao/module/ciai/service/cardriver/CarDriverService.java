package cn.iocoder.yudao.module.ciai.service.cardriver;

import java.util.*;

import cn.iocoder.yudao.module.ciai.dal.dataobject.emvehicledriver.EmVehicleDriverDO;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.cardriver.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.cardriver.CarDriverDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 司机信息 Service 接口
 *
 * <AUTHOR>
 */
public interface CarDriverService {

    /**
     * 创建司机信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCarDriver(@Valid CarDriverSaveReqVO createReqVO);

    /**
     * 更新司机信息
     *
     * @param updateReqVO 更新信息
     */
    void updateCarDriver(@Valid CarDriverSaveReqVO updateReqVO);

    /**
     * 删除司机信息
     *
     * @param id 编号
     */
    void deleteCarDriver(Long id);

    /**
     * 获得司机信息
     *
     * @param id 编号
     * @return 司机信息
     */
    CarDriverDO getCarDriver(Long id);

    /**
     * 获得司机信息分页
     *
     * @param pageReqVO 分页查询
     * @return 司机信息分页
     */
    PageResult<CarDriverDO> getCarDriverPage(CarDriverPageReqVO pageReqVO);

    /**
     * 获得司机信息列表
     * @return
     */
    List<CarDriverDO> getDriverList();

    List<CarDriverDO> getCarDriverAll();

    /**
     * 批量查询司机信息
     *
     * @param driverIds 司机信息编号
     * @return 司机信息列表
     */
    List<CarDriverDO> selectBatchIds(List<Long> driverIds);
}