package cn.iocoder.yudao.module.ciai.service.emaccidentreport;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emaccidentreport.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emaccidentreport.EmAccidentReportDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 设备管理_事故报告 Service 接口
 *
 * <AUTHOR>
 */
public interface EmAccidentReportService {

    /**
     * 创建设备管理_事故报告
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmAccidentReport(@Valid EmAccidentReportSaveReqVO createReqVO);

    /**
     * 更新设备管理_事故报告
     *
     * @param updateReqVO 更新信息
     */
    void updateEmAccidentReport(@Valid EmAccidentReportSaveReqVO updateReqVO);

    /**
     * 删除设备管理_事故报告
     *
     * @param id 编号
     */
    void deleteEmAccidentReport(Long id);

    /**
     * 获得设备管理_事故报告
     *
     * @param id 编号
     * @return 设备管理_事故报告
     */
    EmAccidentReportDO getEmAccidentReport(Long id);

    /**
     * 获得设备管理_事故报告分页
     *
     * @param pageReqVO 分页查询
     * @return 设备管理_事故报告分页
     */
    PageResult<EmAccidentReportDO> getEmAccidentReportPage(EmAccidentReportPageReqVO pageReqVO);

}