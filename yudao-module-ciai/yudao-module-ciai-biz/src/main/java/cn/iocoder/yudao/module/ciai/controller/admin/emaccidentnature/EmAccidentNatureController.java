package cn.iocoder.yudao.module.ciai.controller.admin.emaccidentnature;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.emaccidentnature.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emaccidentnature.EmAccidentNatureDO;
import cn.iocoder.yudao.module.ciai.service.emaccidentnature.EmAccidentNatureService;

@Tag(name = "管理后台 - 设备管理_事故性质")
@RestController
@RequestMapping("/ciai/em-accident-nature")
@Validated
public class EmAccidentNatureController {

    @Resource
    private EmAccidentNatureService emAccidentNatureService;

    @PostMapping("/create")
    @Operation(summary = "创建设备管理_事故性质")
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:create')")
    public CommonResult<Long> createEmAccidentNature(@Valid @RequestBody EmAccidentNatureSaveReqVO createReqVO) {
        return success(emAccidentNatureService.createEmAccidentNature(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备管理_事故性质")
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:update')")
    public CommonResult<Boolean> updateEmAccidentNature(@Valid @RequestBody EmAccidentNatureSaveReqVO updateReqVO) {
        emAccidentNatureService.updateEmAccidentNature(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备管理_事故性质")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:delete')")
    public CommonResult<Boolean> deleteEmAccidentNature(@RequestParam("id") Long id) {
        emAccidentNatureService.deleteEmAccidentNature(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备管理_事故性质")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:query')")
    public CommonResult<EmAccidentNatureRespVO> getEmAccidentNature(@RequestParam("id") Long id) {
        EmAccidentNatureDO emAccidentNature = emAccidentNatureService.getEmAccidentNature(id);
        return success(BeanUtils.toBean(emAccidentNature, EmAccidentNatureRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备管理_事故性质分页")
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:query')")
    public CommonResult<PageResult<EmAccidentNatureRespVO>> getEmAccidentNaturePage(@Valid EmAccidentNaturePageReqVO pageReqVO) {
        PageResult<EmAccidentNatureDO> pageResult = emAccidentNatureService.getEmAccidentNaturePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmAccidentNatureRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备管理_事故性质 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:em-accident-nature:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmAccidentNatureExcel(@Valid EmAccidentNaturePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmAccidentNatureDO> list = emAccidentNatureService.getEmAccidentNaturePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "设备管理_事故性质.xls", "数据", EmAccidentNatureRespVO.class,
                        BeanUtils.toBean(list, EmAccidentNatureRespVO.class));
    }

}