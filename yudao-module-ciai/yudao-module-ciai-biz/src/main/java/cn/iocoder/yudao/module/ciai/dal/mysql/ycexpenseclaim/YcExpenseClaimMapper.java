package cn.iocoder.yudao.module.ciai.dal.mysql.ycexpenseclaim;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ycexpenseclaim.YcExpenseClaimDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.ycexpenseclaim.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 费用报销 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YcExpenseClaimMapper extends BaseMapperX<YcExpenseClaimDO> {

    default PageResult<YcExpenseClaimDO> selectPage(YcExpenseClaimPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<YcExpenseClaimDO>()
                .eqIfPresent(YcExpenseClaimDO::getClaimNo, reqVO.getClaimNo())
                .eqIfPresent(YcExpenseClaimDO::getExpenseType, reqVO.getExpenseType())
                .betweenIfPresent(YcExpenseClaimDO::getApplicationTime, reqVO.getApplicationTime())
                .eqIfPresent(YcExpenseClaimDO::getUserId, reqVO.getUserId())
                .eqIfPresent(YcExpenseClaimDO::getSubmitter, reqVO.getSubmitter())
                .eqIfPresent(YcExpenseClaimDO::getDepdId, reqVO.getDepdId())
                .eqIfPresent(YcExpenseClaimDO::getDepartment, reqVO.getDepartment())
                .eqIfPresent(YcExpenseClaimDO::getTotalAmount, reqVO.getTotalAmount())
                .eqIfPresent(YcExpenseClaimDO::getAmountInWords, reqVO.getAmountInWords())
                .eqIfPresent(YcExpenseClaimDO::getStatus, reqVO.getStatus())
                .eqIfPresent(YcExpenseClaimDO::getAuditor, reqVO.getAuditor())
                .betweenIfPresent(YcExpenseClaimDO::getAuditTime, reqVO.getAuditTime())
                .betweenIfPresent(YcExpenseClaimDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(YcExpenseClaimDO::getId));
    }

    IPage<YcExpenseClaimWithAttachmentRespVO> selectDetailPage(@Param("page") Page<?> page, @Param("reqVO") YcExpenseClaimPageReqVO reqVO);
}