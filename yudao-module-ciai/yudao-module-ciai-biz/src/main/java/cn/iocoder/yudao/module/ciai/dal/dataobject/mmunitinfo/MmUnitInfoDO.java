package cn.iocoder.yudao.module.ciai.dal.dataobject.mmunitinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 营销管理单位信息 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_mm_unit_info")
@KeySequence("ciai_mm_unit_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MmUnitInfoDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单位编号
     */
    private String companyNumber;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 单位简称
     */
    private String companyShortName;
    /**
     * 单位地址
     */
    private String companyAddress;
    /**
     * 负责人
     */
    private String personInCharge;
    /**
     * 联系人
     */
    private String contactPerson;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 客户级别
     */
    private String customerLevel;
    /**
     * 备注
     */
    private String remark;
    /**
     * 开户行
     */
    private String bankName;
    /**
     * 账号
     */
    private String accountNumber;
    /**
     * 税号
     */
    private String taxNumber;
    /**
     * 控制方量
     */
    private Double controlVolume;
    /**
     * 控制金额
     */
    private Double controlAmount;
    /**
     * 预警金额
     */
    private Double warningAmount;
    /**
     * 信誉额度
     */
    private Double creditLimit;
    /**
     * 登录账号
     */
    private String loginAccount;
    /**
     * 启用
     */
    private Integer isEnabled;

    public MmUnitInfoDO(Long id, String companyName) {
        this.id = id;
        this.companyName = companyName;
    }
}