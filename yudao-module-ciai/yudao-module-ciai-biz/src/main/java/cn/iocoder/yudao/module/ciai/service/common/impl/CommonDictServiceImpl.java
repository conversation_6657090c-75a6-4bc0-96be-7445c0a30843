package cn.iocoder.yudao.module.ciai.service.common.impl;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO;
import cn.iocoder.yudao.module.ciai.dal.mysql.common.CommonDictMapper;
import cn.iocoder.yudao.module.ciai.service.common.CommonDictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 通用字典 Service 实现类
 */
@Service
@Validated
@Slf4j
public class CommonDictServiceImpl implements CommonDictService {

    @Autowired
    private CommonDictMapper commonDictMapper;
    
    // SQL注入检测模式
    private static final Pattern SQL_INJECTION_PATTERN = 
            Pattern.compile("('.+--)|(--)|(\\\\)|(')|(\")|(;)|(\\b(ALTER|CREATE|DELETE|DROP|EXEC|INSERT|UPDATE|UNION)\\b)", 
                    Pattern.CASE_INSENSITIVE);
    
    // 表名和字段名正则验证（只允许字母、数字和下划线）
    private static final Pattern NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]+$");
    
    @Override
    // 暂时注释缓存，便于调试
    // @Cacheable(value = "dict_options", key = "#tableName + '_' + #valueColumn + '_' + #labelColumn + '_' + #whereCondition + '_' + #orderBy")
    public List<OptionVO> getDictOptions(String valueColumn, String labelColumn, 
                                      String tableName, String whereCondition, String orderBy) {
        // 参数验证和SQL注入防护
        if (!isValidName(tableName)) {
            log.error("表名不合法: {}", tableName);
            throw new ServiceException("表名包含非法字符");
        }
        
        if (!isValidName(valueColumn) || !isValidName(labelColumn)) {
            log.error("列名不合法: value={}, label={}", valueColumn, labelColumn);
            throw new ServiceException("列名包含非法字符");
        }
        
        if (StrUtil.isNotEmpty(whereCondition) && containsSqlInjection(whereCondition)) {
            log.error("WHERE条件包含可疑SQL注入: {}", whereCondition);
            throw new ServiceException("查询条件包含非法字符");
        }
        
        if (StrUtil.isNotEmpty(orderBy) && containsSqlInjection(orderBy)) {
            log.error("ORDER BY条件包含可疑SQL注入: {}", orderBy);
            throw new ServiceException("排序条件包含非法字符");
        }
        
        // 构建SQL日志，便于调试
        StringBuilder sqlLog = new StringBuilder();
        sqlLog.append("SELECT ").append(valueColumn).append(" AS value, ")
              .append(labelColumn).append(" AS label FROM ").append(tableName);
        if (StrUtil.isNotEmpty(whereCondition)) {
            sqlLog.append(" WHERE ").append(whereCondition);
        }
        if (StrUtil.isNotEmpty(orderBy)) {
            sqlLog.append(" ORDER BY ").append(orderBy);
        }
        
        // 打印完整的SQL查询语句
        log.info("执行通用字典查询SQL: {}", sqlLog.toString());
        
        try {
            // 尝试使用备用方法查询
            log.info("尝试使用备用查询方法...");
            List<OptionVO> result = commonDictMapper.queryDictOptions(
                    valueColumn, labelColumn, tableName, whereCondition, orderBy);
            
            // 打印查询结果数量
            log.info("查询结果数量: {}", result != null ? result.size() : 0);
            if (result != null && !result.isEmpty()) {
                log.info("首条数据: {}", result.get(0));
            } else {
                log.info("未查询到数据，请检查表名、字段名和查询条件是否正确");
                // 检查表是否存在
                log.info("请确认表 '{}' 存在，并且字段 '{}' 和 '{}' 在该表中存在", 
                        tableName, valueColumn, labelColumn);
            }
            
            return result;
        } catch (Exception e) {
            log.error("通用字典查询异常: table={}, value={}, label={}, where={}, orderBy={}, 异常信息: {}", 
                    tableName, valueColumn, labelColumn, whereCondition, orderBy, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OptionVO> selectListByDeptId(Long deptId) {
        return commonDictMapper.selectListByDeptId(deptId);
    }

    @Override
    public List<OptionVO> selectListByParentId(Long parentId) {
        return commonDictMapper.selectListByParentId(parentId);
    }

    /**
     * 验证名称是否合法（表名、字段名）
     */
    private boolean isValidName(String name) {
        return name != null && NAME_PATTERN.matcher(name).matches();
    }
    
    /**
     * 检测SQL注入
     */
    private boolean containsSqlInjection(String input) {
        return SQL_INJECTION_PATTERN.matcher(input).find();
    }
} 