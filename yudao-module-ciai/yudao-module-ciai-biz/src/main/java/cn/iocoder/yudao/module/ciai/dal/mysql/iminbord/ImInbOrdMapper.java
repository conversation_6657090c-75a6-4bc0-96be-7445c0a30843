package cn.iocoder.yudao.module.ciai.dal.mysql.iminbord;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminbord.ImInbOrdDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.iminbord.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 库存管理_入库单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImInbOrdMapper extends BaseMapperX<ImInbOrdDO> {

    default PageResult<ImInbOrdDO> selectPage(ImInbOrdPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImInbOrdDO>()
                .betweenIfPresent(ImInbOrdDO::getInboundDate, reqVO.getInboundDate())
                .orderByDesc(ImInbOrdDO::getId));
    }

    IPage<ImInbOrdDetailRespVO> selectDetailPage(@Param("page") Page<?> page,
                                                 @Param("reqVO") ImInbOrdPageReqVO reqVO);

    @Select("SELECT MAX(inbound_order_number) FROM ciai_im_inb_ord WHERE inbound_order_number LIKE #{pattern}")
    String selectMaxOrderNoByDate(String pattern);
}