package cn.iocoder.yudao.module.ciai.dal.mysql.ycaccountingvoucher;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ycaccountingvoucher.YcAccountingVoucherDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.ycaccountingvoucher.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 记账凭证 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface YcAccountingVoucherMapper extends BaseMapperX<YcAccountingVoucherDO> {

    default PageResult<YcAccountingVoucherDO> selectPage(YcAccountingVoucherPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<YcAccountingVoucherDO>()
                .eqIfPresent(YcAccountingVoucherDO::getStatus, reqVO.getStatus())
                .eqIfPresent(YcAccountingVoucherDO::getVoucherNo, reqVO.getVoucherNo())
                .eqIfPresent(YcAccountingVoucherDO::getBusinessType, reqVO.getBusinessType())
                .eqIfPresent(YcAccountingVoucherDO::getBusinessId, reqVO.getBusinessId())
                .eqIfPresent(YcAccountingVoucherDO::getVoucherType, reqVO.getVoucherType())
                .orderByDesc(YcAccountingVoucherDO::getId));
    }


    @Select("SELECT voucher_no FROM ciai_yc_accounting_voucher " +
            "WHERE voucher_type = #{voucherType} AND voucher_no LIKE CONCAT(#{prefix}, '%') " +
            "ORDER BY voucher_no DESC LIMIT 1")
    String getMaxVoucherNo(@Param("voucherType") String voucherType, @Param("prefix") String prefix);

}