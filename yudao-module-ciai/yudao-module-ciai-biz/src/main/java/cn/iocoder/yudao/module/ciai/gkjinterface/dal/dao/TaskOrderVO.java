package cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TaskOrderVO {
    /**
     * 任务单编号
     */
    private String taskNumber;

    /**
     * 计划开盘时间
     */
    private LocalDateTime planOpenTime;

    /**
     * 建委合同号
     */
    private String contractNo;
    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 施工部位
     */
    private String buildPosition;

    /**
     * 浇注方式
     */
    private String pouringMethod;

    /**
     * 混凝土标号
     */
    private String grade;

    /**
     * 工程地址
     */
    private String projectAddress;

    /**
     * 强度等级
     */
    private String strengthGrade;

    /**
     * 抗渗等级
     */
    private String impermeabilityGrade;

    /**
     * 抗冻等级
     */
    private String antifreezeGrade;

    /**
     * 石子种类
     */
    private String stoneType;

    /**
     * 特殊项目
     */
    private String specialItem;
    /**
     * 特殊项目1
     */
    private String specialItem1;
    /**
     * 特殊项目2
     */
    private String specialItem2;

    /**
     * 计划方量
     */
    private Double plannedQuantity;

    /**
     * 要求坍落度
     */
    private String slumpRequired;

    /**
     * 备注
     */
    private String taskNotes;


    /**
     * 任务登记人
     */
    private String updater;

    /**
     * 运输距离
     */
    private Double shippingDistance;
}
