package cn.iocoder.yudao.module.ciai.controller.admin.emequipmenttype.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备管理_设备类型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EmEquipmentTypePageReqVO extends PageParam {

    @Schema(description = "设备类型名称", example = "王五")
    private String typeName;

    @Schema(description = "备注", example = "随便")
    private String remark;

}