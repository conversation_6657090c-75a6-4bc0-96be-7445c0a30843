package cn.iocoder.yudao.module.ciai.service.emequipmentmanagementplan;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentmanagementplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentmanagementplan.EmEquipmentManagementPlanDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.emequipmentmanagementplan.EmEquipmentManagementPlanMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 方案描述 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmEquipmentManagementPlanServiceImpl implements EmEquipmentManagementPlanService {

    @Resource
    private EmEquipmentManagementPlanMapper emEquipmentManagementPlanMapper;

    @Override
    public Long createEmEquipmentManagementPlan(EmEquipmentManagementPlanSaveReqVO createReqVO) {
        // 插入
        EmEquipmentManagementPlanDO emEquipmentManagementPlan = BeanUtils.toBean(createReqVO, EmEquipmentManagementPlanDO.class);
        emEquipmentManagementPlanMapper.insert(emEquipmentManagementPlan);
        // 返回
        return emEquipmentManagementPlan.getId();
    }

    @Override
    public void updateEmEquipmentManagementPlan(EmEquipmentManagementPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateEmEquipmentManagementPlanExists(updateReqVO.getId());
        // 更新
        EmEquipmentManagementPlanDO updateObj = BeanUtils.toBean(updateReqVO, EmEquipmentManagementPlanDO.class);
        emEquipmentManagementPlanMapper.updateById(updateObj);
    }

    @Override
    public void deleteEmEquipmentManagementPlan(Long id) {
        // 校验存在
        validateEmEquipmentManagementPlanExists(id);
        // 删除
        emEquipmentManagementPlanMapper.deleteById(id);
    }

    private void validateEmEquipmentManagementPlanExists(Long id) {
        if (emEquipmentManagementPlanMapper.selectById(id) == null) {
            throw exception(EM_EQUIPMENT_MANAGEMENT_PLAN_NOT_EXISTS);
        }
    }

    @Override
    public EmEquipmentManagementPlanDO getEmEquipmentManagementPlan(Long id) {
        return emEquipmentManagementPlanMapper.selectById(id);
    }

    @Override
    public PageResult<EmEquipmentManagementPlanDO> getEmEquipmentManagementPlanPage(EmEquipmentManagementPlanPageReqVO pageReqVO) {
        return emEquipmentManagementPlanMapper.selectPage(pageReqVO);
    }

    @Override
    public EmEquipmentManagementPlanRespVO getEmEquipmentManagementPlanById(Long id) {
        EmEquipmentManagementPlanDO emEquipmentManagementPlan = emEquipmentManagementPlanMapper.selectById(id);
        return BeanUtils.toBean(emEquipmentManagementPlan, EmEquipmentManagementPlanRespVO.class);
    }

}