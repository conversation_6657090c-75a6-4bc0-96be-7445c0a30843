package cn.iocoder.yudao.module.ciai.service.pmshiprecords;

import java.util.*;

import cn.iocoder.yudao.module.ciai.dal.dataobject.cardriver.CarDriverDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.carvehicle.CarVehicleDO;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.pmshiprecords.PmShipRecordsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.transaction.annotation.Transactional;

/**
 * 生产管理_发货记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PmShipRecordsService {


    /**
     * 创建生产管理_发货记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPmShipRecords(@Valid PmShipRecordsSaveReqVO createReqVO);

    /**
     * 更新生产管理_发货记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePmShipRecords(@Valid PmShipRecordsSaveReqVO updateReqVO);

    /**
     * 删除生产管理_发货记录
     *
     * @param id 编号
     */
    void deletePmShipRecords(Long id);

    /**
     * 获得生产管理_发货记录
     *
     * @param id 编号
     * @return 生产管理_发货记录
     */
    PmShipRecordsDO getPmShipRecords(Long id);

    /**
     * 获得生产管理_发货记录分页
     *
     * @param pageReqVO 分页查询
     * @return 生产管理_发货记录分页
     */
    PageResult<PmShipRecordsDO> getPmShipRecordsPage(PmShipRecordsPageReqVO pageReqVO);

    List<CarVehicleDO> getInfo(Long status);

    void updateLineUp(Long id);

    void getFirst(Long id);

    void getEnd(Long id);

    void getCanel(Long id);

    PmExpressVO selectExService();

    List<CarVehicleDO> getInfos();

    List<CarDriverDO> getDriverService(PmDriverRepVO pmDriverRepVO);

    List<PmShipRecordsDO> getPmShipRecordsPages(Long id);

    List<PmUnitVO> getUnitService();

    List<PmRetuVO> getRetuService(Long id);

    /**
     * 自动任务调度
     * @return
     */
    List<DispatchPlanRespVO> autoDispatch(@Valid DispatchRequestVO dispatchRequestVO);

    void updateVideo(Long id, Integer video);

    void updateWater(Long id, Integer water);

    /**
     * 获取车辆运输路线信息
     * @param id 发货记录ID
     * @return 路线信息
     */
    RouteInfoRespVO getRouteInfo(Long id);

    /**
     * 获取车辆运输路线车辆信息
     * @param taskPlanId 任务计划ID
     * @return 车辆信息
     */
    RouteVehiclesVO getRouteVehicles(Long taskPlanId);
}