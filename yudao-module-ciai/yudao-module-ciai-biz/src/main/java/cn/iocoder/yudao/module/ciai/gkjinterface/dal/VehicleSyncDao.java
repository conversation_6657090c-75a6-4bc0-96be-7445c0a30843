package cn.iocoder.yudao.module.ciai.gkjinterface.dal;

import cn.iocoder.yudao.module.ciai.gkjinterface.dal.dao.VehicleSyncVO;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.*;

@Mapper
public interface VehicleSyncDao {

    @Master
    VehicleSyncVO selectTransportDataById(@Param("id") Long id);

    @Select("SELECT COUNT(1) FROM VTglPcbSync WHERE fpcbID = #{ysd}")
    int existsCommand(@Param("ysd") String ysd, @Param("dataSourceName") String dataSourceName);

    @Delete("DELETE FROM VTglPcbSync WHERE fpcbID = #{ysd}")
    int deleteCommand(@Param("ysd") String ysd, @Param("dataSourceName") String dataSourceName);

    @Select("SELECT COUNT(1) FROM VTglPcbSync")
    int countAllCommands(@Param("dataSourceName") String dataSourceName);

    int insertCommand(
            @Param("dataSourceName") String dataSourceName,
            @Param("ysd") String ysd,
            @Param("taskId") String taskId,
            @Param("contractNo") String contractNo,
            @Param("company") String company,
            @Param("project") String project,
            @Param("buildPart") String buildPart,
            @Param("pourMethod") String pourMethod,
            @Param("buildAddr") String buildAddr,
            @Param("type") String type,
            @Param("grade") String grade,
            @Param("slump") String slump,
            @Param("remark") String remark,
            @Param("signer") String signer,
            @Param("hostId") Long hostId,
            @Param("vehicleId") String vehicleId,
            @Param("driver") String driver,
            @Param("volume") Double volume,
            @Param("order") int order,
            @Param("departureTime") String departureTime,
            @Param("cumulativeVolume") Double cumulativeVolume,
            @Param("count") int count,
            @Param("distance") String distance,
            @Param("perBatchVolume") Double perBatchVolume,
            @Param("status") String status
    );

    @Master
    @Update("update ciai_pm_ship_records set instruction_status = 1 where id = #{id}")
    int updateStatus(@Param("id") Long id);
}
