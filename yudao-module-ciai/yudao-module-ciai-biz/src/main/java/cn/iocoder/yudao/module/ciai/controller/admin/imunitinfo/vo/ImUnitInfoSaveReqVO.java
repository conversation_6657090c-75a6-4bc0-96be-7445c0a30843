package cn.iocoder.yudao.module.ciai.controller.admin.imunitinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 单位信息新增/修改 Request VO")
@Data
public class ImUnitInfoSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "单位名称")
    private String unitName;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactNumber;

    @Schema(description = "备注")
    private String remarks;

}