package cn.iocoder.yudao.module.ciai.dal.mysql.jttravelexpenses;

import cn.iocoder.yudao.module.ciai.dal.dataobject.jttravelexpenses.ExpenseTypeSubjectDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExpenseTypeSubjectMapper {

    /**
     * 根据费用类型和部门ID查找最匹配的科目映射
     */
    List<ExpenseTypeSubjectDO> selectByExpenseTypeAndDept(
            @Param("expenseType") String expenseType,
            @Param("departmentId") Integer departmentId);

    /**
     * 根据费用类型查找默认科目（不依赖部门）
     */
    List<ExpenseTypeSubjectDO> selectDefaultByExpenseType(@Param("expenseType") String expenseType);
}