package cn.iocoder.yudao.module.ciai.dal.dataobject.equipmentdeprereport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 设备折旧报表 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_em_equipment_info")
@KeySequence("ciai_em_equipment_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class equipmentdeprereportDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 设备编号
     */
    private String equipmentCode;
    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备数量
     */
    private Long quantity;
    /**
     * 规格型号
     */
    private String specificationModel;
    /**
     * 设备类型ID
     */
    private Long equipmentTypeId;
    /**
     * 设备图片
     */
    private String equipmentImage;
    /**
     * 生产厂商名称
     */
    private String manufacturer;
    /**
     * 总功率
     */
    private String totalPower;
    /**
     * 经销商名称
     */
    private String distributor;
    /**
     * 设备标识
     */
    private String equipmentIdentifier;
    /**
     * 购置时间
     */
    private LocalDateTime purchaseTime;
    /**
     * 资产原值
     */
    private Long assetOriginalValue;
    /**
     * 资产负责人
     */
    private String assetResponsiblePerson;
    /**
     * 残值率
     */
    private Double residualRate;
    /**
     * 折旧月
     */
    private Long depreciationMonths;
    /**
     * 设备状态
     */
    private String equipmentStatus;
    /**
     * 使用部门ID
     */
    private Long departmentId;
    /**
     * 操作人员姓名
     */
    private String operator;
    /**
     * 安装地点ID (关联地址表)
     */
    private Long installationLocation;
    /**
     * 备注
     */
    private String remark;
    /**
     * 所属公司ID
     */
    private Long companyId;
    /**
     * 启用状态 (1:启用 0:停用)
     */
    private Boolean enabled;

    // 新增计算字段，不对应数据库字段
    @Schema(description = "资产总值：资产原值 × 数量")
    @ExcelProperty("资产总值")
    @TableField(exist = false)
    private Double assetTotalValue;

    @Schema(description = "月折旧率：(1 - 残值率) / 总折旧月数")
    @ExcelProperty("月折旧率(%)")
    @TableField(exist = false)
    private Double monthlyDepreciationRate;

    @Schema(description = "月折旧额：资产原值 × 月折旧率")
    @ExcelProperty("月折旧额")
    @TableField(exist = false)
    private Double monthlyDepreciationAmount;

    @Schema(description = "累计折旧：月折旧额 × 已计提月数")
    @ExcelProperty("累计折旧")
    @TableField(exist = false)
    private Double accumulatedDepreciation;

    @Schema(description = "净值：资产原值 - 累计折旧")
    @ExcelProperty("净值")
    @TableField(exist = false)
    private Double netValue;

    @Schema(description = "剩余折旧：资产原值 × (1 - 残值率) - 累计折旧")
    @ExcelProperty("剩余折旧")
    @TableField(exist = false)
    private Double remainingDepreciation;

    @JsonIgnore
    @TableField(exist = false)
    private Integer usedMonths; // 内部使用字段，用于计算已使用月数

}