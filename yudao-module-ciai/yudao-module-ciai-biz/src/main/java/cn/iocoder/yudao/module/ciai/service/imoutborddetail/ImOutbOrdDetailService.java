package cn.iocoder.yudao.module.ciai.service.imoutborddetail;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.imoutborddetail.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.imoutborddetail.ImOutbOrdDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 出库明细 Service 接口
 *
 * <AUTHOR>
 */
public interface ImOutbOrdDetailService {

    /**
     * 创建出库明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImOutbOrdDetail(@Valid ImOutbOrdDetailSaveReqVO createReqVO);

    /**
     * 更新出库明细
     *
     * @param updateReqVO 更新信息
     */
    void updateImOutbOrdDetail(@Valid ImOutbOrdDetailSaveReqVO updateReqVO);

    /**
     * 删除出库明细
     *
     * @param id 编号
     */
    void deleteImOutbOrdDetail(Long id);

    /**
     * 获得出库明细
     *
     * @param id 编号
     * @return 出库明细
     */
    ImOutbOrdDetailDO getImOutbOrdDetail(Long id);

    /**
     * 获得出库明细分页
     *
     * @param pageReqVO 分页查询
     * @return 出库明细分页
     */
    PageResult<ImOutbOrdDetailDO> getImOutbOrdDetailPage(ImOutbOrdDetailPageReqVO pageReqVO);

}