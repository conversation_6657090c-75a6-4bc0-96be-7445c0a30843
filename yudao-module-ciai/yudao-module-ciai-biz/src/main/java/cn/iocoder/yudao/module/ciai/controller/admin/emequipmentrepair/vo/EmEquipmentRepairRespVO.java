package cn.iocoder.yudao.module.ciai.controller.admin.emequipmentrepair.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 设备维修记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EmEquipmentRepairRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "设备ID")
    @ExcelProperty("设备ID")
    private Long equipmentId;

    @Schema(description = "维修状态 (1:已完成 0:进行中)")
    @ExcelProperty(value = "维修状态 (1:已完成 0:进行中)", converter = DictConvert.class)
    @DictFormat("infra_boolean_string") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Boolean isCompleted;

    @Schema(description = "维修申请人")
    @ExcelProperty("维修申请人")
    private String repairApplicant;

    @Schema(description = "送修时间")
    @ExcelProperty("送修时间")
    private LocalDateTime sendRepairTime;

    @Schema(description = "故障描述")
    @ExcelProperty("故障描述")
    private String faultDescription;

    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime completionTime;

    @Schema(description = "维修级别")
    @ExcelProperty(value = "维修级别", converter = DictConvert.class)
    @DictFormat("em_repair_level") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String repairLevel;

    @Schema(description = "维修费用")
    @ExcelProperty("维修费用")
    private String repairCost;

    @Schema(description = "维修负责人")
    @ExcelProperty("维修负责人")
    private String repairResponsiblePerson;

    @Schema(description = "故障分析及工作明细")
    @ExcelProperty("故障分析及工作明细")
    private String faultAnalysis;

    @Schema(description = "故障级别")
    @ExcelProperty(value = "故障级别", converter = DictConvert.class)
    @DictFormat("em_fault_level") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String faultLevel;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

}