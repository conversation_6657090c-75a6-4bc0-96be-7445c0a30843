package cn.iocoder.yudao.module.ciai.controller.admin.equipmentdeprereport;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.equipmentdeprereport.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.equipmentdeprereport.equipmentdeprereportDO;
import cn.iocoder.yudao.module.ciai.service.equipmentdeprereport.equipmentdeprereportService;

@Tag(name = "管理后台 - 设备折旧报表")
@RestController
@RequestMapping("/ciai/equipmentdeprereport")
@Validated
public class equipmentdeprereportController {

    @Resource
    private equipmentdeprereportService equipmentdeprereportService;

    @PostMapping("/create")
    @Operation(summary = "创建设备折旧报表")
    @PreAuthorize("@ss.hasPermission('ciai:equipmentdeprereport:create')")
    public CommonResult<Long> createequipmentdeprereport(@Valid @RequestBody equipmentdeprereportSaveReqVO createReqVO) {
        return success(equipmentdeprereportService.createequipmentdeprereport(createReqVO));
    }


    @DeleteMapping("/delete")
    @Operation(summary = "删除设备折旧报表")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:equipmentdeprereport:delete')")
    public CommonResult<Boolean> deleteequipmentdeprereport(@RequestParam("id") Long id) {
        equipmentdeprereportService.deleteequipmentdeprereport(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备折旧报表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:equipmentdeprereport:query')")
    public CommonResult<equipmentdeprereportRespVO> getequipmentdeprereport(@RequestParam("id") Long id) {
        equipmentdeprereportDO equipmentdeprereport = equipmentdeprereportService.getequipmentdeprereport(id);
        return success(BeanUtils.toBean(equipmentdeprereport, equipmentdeprereportRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备折旧报表分页")
    @PreAuthorize("@ss.hasPermission('ciai:equipmentdeprereport:query')")
    public CommonResult<PageResult<equipmentdeprereportRespVO>> getequipmentdeprereportPage(@Valid equipmentdeprereportPageReqVO pageReqVO) {
        PageResult<equipmentdeprereportDO> pageResult = equipmentdeprereportService.getequipmentdeprereportPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, equipmentdeprereportRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备折旧报表 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:equipmentdeprereport:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportequipmentdeprereportExcel(@Valid equipmentdeprereportPageReqVO pageReqVO,
                                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<equipmentdeprereportDO> list = equipmentdeprereportService.getequipmentdeprereportPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "设备折旧报表.xls", "数据", equipmentdeprereportRespVO.class,
                BeanUtils.toBean(list, equipmentdeprereportRespVO.class));
    }

}