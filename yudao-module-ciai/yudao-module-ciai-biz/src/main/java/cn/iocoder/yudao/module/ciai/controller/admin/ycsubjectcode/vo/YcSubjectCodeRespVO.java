package cn.iocoder.yudao.module.ciai.controller.admin.ycsubjectcode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 科目代码 Response VO")
@Data
@ExcelIgnoreUnannotated
public class YcSubjectCodeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "科目分类")
    @ExcelProperty(value = "科目分类", converter = DictConvert.class)
    @DictFormat("ciai_subject_category") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String subjectCategory;

    @Schema(description = "会计科目代码")
    @ExcelProperty("会计科目代码")
    private String accountingSubjectCode;

    @Schema(description = "科目名称")
    @ExcelProperty("科目名称")
    private String subjectName;

    @Schema(description = "客户会计科目代码")
    @ExcelProperty("客户会计科目代码")
    private String customerAccountCode;

    @Schema(description = "客户科目名称")
    @ExcelProperty("客户科目名称")
    private String customerAccountName;

    @Schema(description = "余额方向")
    @ExcelProperty("余额方向")
    private String balanceDirection;

    @Schema(description = "辅助核算项1")
    @ExcelProperty("辅助核算项1")
    private String auxiliaryAccountingItem1;

    @Schema(description = "辅助核算项2")
    @ExcelProperty("辅助核算项2")
    private String auxiliaryAccountingItem2;

    @Schema(description = "辅助核算项3")
    @ExcelProperty("辅助核算项3")
    private String auxiliaryAccountingItem3;

    @Schema(description = "借方会计凭证附件1")
    @ExcelProperty("借方会计凭证附件1")
    private String debitVoucherAttachment1;

    @Schema(description = "借方会计凭证附件2")
    @ExcelProperty("借方会计凭证附件2")
    private String debitVoucherAttachment2;

    @Schema(description = "借方会计凭证附件3")
    @ExcelProperty("借方会计凭证附件3")
    private String debitVoucherAttachment3;

    @Schema(description = "贷方会计凭证附件1")
    @ExcelProperty("贷方会计凭证附件1")
    private String creditVoucherAttachment1;

    @Schema(description = "贷方会计凭证附件2")
    @ExcelProperty("贷方会计凭证附件2")
    private String creditVoucherAttachment2;

    @Schema(description = "贷方会计凭证附件3")
    @ExcelProperty("贷方会计凭证附件3")
    private String creditVoucherAttachment3;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}