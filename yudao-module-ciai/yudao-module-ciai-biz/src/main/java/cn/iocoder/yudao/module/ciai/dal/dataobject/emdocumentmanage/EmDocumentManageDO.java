package cn.iocoder.yudao.module.ciai.dal.dataobject.emdocumentmanage;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 设备管理_文档管理 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_em_document_manage")
@KeySequence("ciai_em_document_manage_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmDocumentManageDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 设备编号
     */
    private String equipmentCode;
    /**
     * 文档名称
     */
    private String documentName;
    /**
     * 文档类型
     */
    private String documentType;
    /**
     * 配合视频
     */
    private String videoLink;
    /**
     * 上传日期
     */
    private LocalDateTime uploadDate;
    /**
     * 文档附件
     */
    private String documentAttachment;
    /**
     * 备注
     */
    private String remark;
    /**
     * 启用状态 (1:启用 0:停用)
     */
    private Boolean enabled;

}