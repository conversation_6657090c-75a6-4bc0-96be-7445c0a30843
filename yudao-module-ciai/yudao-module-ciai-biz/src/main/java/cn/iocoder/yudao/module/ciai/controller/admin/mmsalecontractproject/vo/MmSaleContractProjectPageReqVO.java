package cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontractproject.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 工程信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MmSaleContractProjectPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "合同ID")
    private Long salesContractId;

    @Schema(description = "工程名称")
    private String projectName;

    @Schema(description = "工程简称")
    private String projectShortName;

    @Schema(description = "工程地址")
    private String projectAddress;

    @Schema(description = "工程代码")
    private String projectCode;

    @Schema(description = "工程备注")
    private String projectRemark;

    @Schema(description = "是否需要资料")
    private Integer isNeedMaterial;

}