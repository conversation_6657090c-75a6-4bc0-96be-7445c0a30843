package cn.iocoder.yudao.module.ciai.dal.dataobject.jssyflyashtest;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 粉煤灰试验 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_js_sy_fly_ash_test")
@KeySequence("ciai_js_sy_fly_ash_test_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JsSyFlyAshTestDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 试验编号
     */
    private String testNo;
    /**
     * 委托编号
     */
    private String entrustNo;
    /**
     * 试样编号
     */
    private String sampleNo;
    /**
     * 委托单位
     */
    private String entrustUnit;
    /**
     * 试验委托人
     */
    private String testClient;
    /**
     * 负责人
     */
    private String responsiblePerson;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 试验人
     */
    private String tester;
    /**
     * 工程名称
     */
    private String projectName;
    /**
     * 种类编码
     */
    private String typeCode;
    /**
     * 种类
     */
    private String type;
    /**
     * 规格
     */
    private String specification;
    /**
     * 产地
     */
    private String origin;
    /**
     * 出厂日期
     */
    private LocalDateTime factoryDate;
    /**
     * 进厂日期
     */
    private LocalDateTime entryDate;
    /**
     * 收样日期
     */
    private LocalDateTime sampleDate;
    /**
     * 代表数量
     */
    private String representQuantity;
    /**
     * 烧失量
     */
    private BigDecimal lossOnIgnition;
    /**
     * 细度
     */
    private BigDecimal fineness;
    /**
     * 需水量比
     */
    private Integer waterDemandRatio;
    /**
     * 结论
     */
    private String conclusion;
    /**
     * 试验日期
     */
    private LocalDateTime testDate;
    /**
     * 截止日期
     */
    private LocalDateTime deadlineDate;
    /**
     * 名称
     */
    private String name;
    /**
     * 28天胶砂抗压比
     */
    private String compressiveStrengthRatio28d;
    /**
     * 含水量
     */
    private BigDecimal moistureContent;
    /**
     * 报告日期
     */
    private LocalDateTime reportDate;
    /**
     * XM1
     */
    private String xm1;
    /**
     * XM2
     */
    private String xm2;
    /**
     * 出厂编号
     */
    private String factoryNo;
    /**
     * XM11
     */
    private String xm11;
    /**
     * XM21
     */
    private String xm21;
    /**
     * XM31
     */
    private String xm31;
    /**
     * XM12
     */
    private String xm12;
    /**
     * XM22
     */
    private String xm22;
    /**
     * XM32
     */
    private String xm32;
    /**
     * XM13
     */
    private String xm13;
    /**
     * XM23
     */
    private String xm23;
    /**
     * XM33
     */
    private String xm33;
    /**
     * XM14
     */
    private String xm14;
    /**
     * XM24
     */
    private String xm24;
    /**
     * XM34
     */
    private String xm34;
    /**
     * XM15
     */
    private String xm15;
    /**
     * XM25
     */
    private String xm25;
    /**
     * XM35
     */
    private String xm35;
    /**
     * XM16
     */
    private String xm16;
    /**
     * XM26
     */
    private String xm26;
    /**
     * XM36
     */
    private String xm36;
    /**
     * XM17
     */
    private String xm17;
    /**
     * XM27
     */
    private String xm27;
    /**
     * XM37
     */
    private String xm37;
    /**
     * XM18
     */
    private String xm18;
    /**
     * XM28
     */
    private String xm28;
    /**
     * XM38
     */
    private String xm38;
    /**
     * 碱含量
     */
    private BigDecimal alkaliContent;
    /**
     * 氯离子
     */
    private BigDecimal chlorideIon;
    /**
     * 附件状态
     */
    private String attachmentStatus;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 时间
     */
    private LocalDateTime modifyTime;
    /**
     * XD11
     */
    private Double xd11;
    /**
     * XD12
     */
    private Double xd12;
    /**
     * XD筛余1
     */
    private BigDecimal xdResidue1;
    /**
     * XD校正
     */
    private Double xdCorrection;
    /**
     * XD21
     */
    private Double xd21;
    /**
     * XD22
     */
    private Double xd22;
    /**
     * XD筛余2
     */
    private BigDecimal xdResidue2;
    /**
     * XDV
     */
    private BigDecimal xdV;
    /**
     * XS11
     */
    private Integer xs11;
    /**
     * XS12
     */
    private Integer xs12;
    /**
     * XS13
     */
    private Integer xs13;
    /**
     * XS14
     */
    private Integer xs14;
    /**
     * XSL1
     */
    private Integer xsL1;
    /**
     * XS21
     */
    private Integer xs21;
    /**
     * XS22
     */
    private Integer xs22;
    /**
     * XS23
     */
    private Integer xs23;
    /**
     * XSL2
     */
    private Integer xsL2;
    /**
     * XSLB
     */
    private Integer xsLb;
    /**
     * SS11
     */
    private BigDecimal ss11;
    /**
     * SS12
     */
    private BigDecimal ss12;
    /**
     * SS13
     */
    private BigDecimal ss13;
    /**
     * SS14
     */
    private BigDecimal ss14;
    /**
     * SS1烧前重
     */
    private BigDecimal ss1PreWeight;
    /**
     * SS1合重
     */
    private BigDecimal ss1TotalWeight;
    /**
     * SS15
     */
    private BigDecimal ss15;
    /**
     * SS16
     */
    private BigDecimal ss16;
    /**
     * SS17
     */
    private BigDecimal ss17;
    /**
     * SS18
     */
    private BigDecimal ss18;
    /**
     * SS1烧后重
     */
    private BigDecimal ss1PostWeight;
    /**
     * SSL1
     */
    private BigDecimal ssL1;
    /**
     * SS21
     */
    private BigDecimal ss21;
    /**
     * SS22
     */
    private BigDecimal ss22;
    /**
     * SS23
     */
    private BigDecimal ss23;
    /**
     * SS24
     */
    private BigDecimal ss24;
    /**
     * SS2烧前重
     */
    private BigDecimal ss2PreWeight;
    /**
     * SS2合重
     */
    private BigDecimal ss2TotalWeight;
    /**
     * SS2烧后重
     */
    private BigDecimal ss2PostWeight;
    /**
     * SS25
     */
    private BigDecimal ss25;
    /**
     * SS26
     */
    private BigDecimal ss26;
    /**
     * SS27
     */
    private BigDecimal ss27;
    /**
     * SS28
     */
    private BigDecimal ss28;
    /**
     * SSL2
     */
    private BigDecimal ssL2;
    /**
     * 执行标准
     */
    private String standard;
    /**
     * 试验状态
     */
    private String testStatus;
    /**
     * 使用状态
     */
    private String usageStatus;
    /**
     * 选择
     */
    private Integer selected;
    /**
     * 打印
     */
    private Integer printed;
    /**
     * Y
     */
    private Integer yValue;
    /**
     * 站别
     */
    private String stationCode;
    /**
     * 试验人2
     */
    private String tester2;
    /**
     * 执行标准2
     */
    private String standard2;
    /**
     * 工程类型
     */
    private String projectType;
    /**
     * 厂家牌号
     */
    private String factoryBrand;
    /**
     * 活性指数7
     */
    private Integer activityIndex7d;
    /**
     * 活性指数28
     */
    private Integer activityIndex28d;
    /**
     * 试验状态7天
     */
    private String testStatus7d;
    /**
     * 试验状态28天
     */
    private String testStatus28d;
    /**
     * 荷载71
     */
    private BigDecimal load71;
    /**
     * 荷载72
     */
    private BigDecimal load72;
    /**
     * 荷载73
     */
    private BigDecimal load73;
    /**
     * 荷载74
     */
    private BigDecimal load74;
    /**
     * 荷载75
     */
    private BigDecimal load75;
    /**
     * 荷载76
     */
    private BigDecimal load76;
    /**
     * 抗压71
     */
    private BigDecimal compressive71;
    /**
     * 抗压72
     */
    private BigDecimal compressive72;
    /**
     * 抗压73
     */
    private BigDecimal compressive73;
    /**
     * 抗压74
     */
    private BigDecimal compressive74;
    /**
     * 抗压75
     */
    private BigDecimal compressive75;
    /**
     * 抗压76
     */
    private BigDecimal compressive76;
    /**
     * 抗压7V
     */
    private BigDecimal compressive7v;
    /**
     * 基准荷载71
     */
    private BigDecimal baseLoad71;
    /**
     * 基准荷载72
     */
    private BigDecimal baseLoad72;
    /**
     * 基准荷载73
     */
    private BigDecimal baseLoad73;
    /**
     * 基准荷载74
     */
    private BigDecimal baseLoad74;
    /**
     * 基准荷载75
     */
    private BigDecimal baseLoad75;
    /**
     * 基准荷载76
     */
    private BigDecimal baseLoad76;
    /**
     * 基准抗压71
     */
    private BigDecimal baseCompressive71;
    /**
     * 基准抗压72
     */
    private BigDecimal baseCompressive72;
    /**
     * 基准抗压73
     */
    private BigDecimal baseCompressive73;
    /**
     * 基准抗压74
     */
    private BigDecimal baseCompressive74;
    /**
     * 基准抗压75
     */
    private BigDecimal baseCompressive75;
    /**
     * 基准抗压76
     */
    private BigDecimal baseCompressive76;
    /**
     * 基准抗压7V
     */
    private BigDecimal baseCompressive7v;
    /**
     * 基准荷载281
     */
    private BigDecimal baseLoad281;
    /**
     * 基准荷载282
     */
    private BigDecimal baseLoad282;
    /**
     * 基准荷载283
     */
    private BigDecimal baseLoad283;
    /**
     * 基准荷载284
     */
    private BigDecimal baseLoad284;
    /**
     * 基准荷载285
     */
    private BigDecimal baseLoad285;
    /**
     * 基准荷载286
     */
    private BigDecimal baseLoad286;
    /**
     * 荷载281
     */
    private BigDecimal load281;
    /**
     * 荷载282
     */
    private BigDecimal load282;
    /**
     * 荷载283
     */
    private BigDecimal load283;
    /**
     * 荷载284
     */
    private BigDecimal load284;
    /**
     * 荷载285
     */
    private BigDecimal load285;
    /**
     * 荷载286
     */
    private BigDecimal load286;
    /**
     * 抗压281
     */
    private BigDecimal compressive281;
    /**
     * 抗压282
     */
    private BigDecimal compressive282;
    /**
     * 抗压283
     */
    private BigDecimal compressive283;
    /**
     * 抗压284
     */
    private BigDecimal compressive284;
    /**
     * 抗压285
     */
    private BigDecimal compressive285;
    /**
     * 抗压286
     */
    private BigDecimal compressive286;
    /**
     * 抗压28V
     */
    private BigDecimal compressive28v;
    /**
     * 基准抗压281
     */
    private BigDecimal baseCompressive281;
    /**
     * 基准抗压282
     */
    private BigDecimal baseCompressive282;
    /**
     * 基准抗压283
     */
    private BigDecimal baseCompressive283;
    /**
     * 基准抗压284
     */
    private BigDecimal baseCompressive284;
    /**
     * 基准抗压285
     */
    private BigDecimal baseCompressive285;
    /**
     * 基准抗压286
     */
    private BigDecimal baseCompressive286;
    /**
     * 基准抗压28V
     */
    private BigDecimal baseCompressive28v;
    /**
     * BBMJV
     */
    private String bbmjv;
    /**
     * HS1
     */
    private BigDecimal hs1;
    /**
     * HS2
     */
    private BigDecimal hs2;
    /**
     * HS3
     */
    private BigDecimal hs3;
    /**
     * HSL
     */
    private BigDecimal hsL;

}