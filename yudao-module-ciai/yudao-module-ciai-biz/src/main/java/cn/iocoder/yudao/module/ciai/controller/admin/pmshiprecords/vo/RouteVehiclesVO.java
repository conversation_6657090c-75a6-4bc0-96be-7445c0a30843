package cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运输路线车辆信息
 */
@Schema(description = "运输路线车辆信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RouteVehiclesVO {

    /**
     * 去程车辆列表
     */
    @Schema(description = "去程车辆列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RouteVehicleVO> outboundVehicles;

    /**
     * 回程车辆列表
     */
    @Schema(description = "返程车辆列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RouteVehicleVO> inboundVehicles;
}