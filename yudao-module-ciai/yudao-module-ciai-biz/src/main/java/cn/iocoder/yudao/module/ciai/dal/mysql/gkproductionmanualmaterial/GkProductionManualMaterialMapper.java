package cn.iocoder.yudao.module.ciai.dal.mysql.gkproductionmanualmaterial;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionmanualmaterial.GkProductionManualMaterialDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualmaterial.vo.*;

/**
 * 生产手动消耗原材料 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GkProductionManualMaterialMapper extends BaseMapperX<GkProductionManualMaterialDO> {

    default PageResult<GkProductionManualMaterialDO> selectPage(GkProductionManualMaterialPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GkProductionManualMaterialDO>()
                .eqIfPresent(GkProductionManualMaterialDO::getNo, reqVO.getNo())
                .betweenIfPresent(GkProductionManualMaterialDO::getProductionTime, reqVO.getProductionTime())
                .eqIfPresent(GkProductionManualMaterialDO::getProductionHost, reqVO.getProductionHost())
                .eqIfPresent(GkProductionManualMaterialDO::getStorageLocation, reqVO.getStorageLocation())
                .likeIfPresent(GkProductionManualMaterialDO::getStorageLocationName, reqVO.getStorageLocationName())
                .eqIfPresent(GkProductionManualMaterialDO::getStorageLocationAlias, reqVO.getStorageLocationAlias())
                .eqIfPresent(GkProductionManualMaterialDO::getConstructionMaterialCode, reqVO.getConstructionMaterialCode())
                .likeIfPresent(GkProductionManualMaterialDO::getMaterialName, reqVO.getMaterialName())
                .eqIfPresent(GkProductionManualMaterialDO::getStorageType, reqVO.getStorageType())
                .eqIfPresent(GkProductionManualMaterialDO::getUnitPrice, reqVO.getUnitPrice())
                .eqIfPresent(GkProductionManualMaterialDO::getTheoreticalValue, reqVO.getTheoreticalValue())
                .eqIfPresent(GkProductionManualMaterialDO::getActualValue, reqVO.getActualValue())
                .eqIfPresent(GkProductionManualMaterialDO::getErrorValue, reqVO.getErrorValue())
                .eqIfPresent(GkProductionManualMaterialDO::getMoistureContent, reqVO.getMoistureContent())
                .eqIfPresent(GkProductionManualMaterialDO::getIsUnitPriceUpdated, reqVO.getIsUnitPriceUpdated())
                .eqIfPresent(GkProductionManualMaterialDO::getRemarks, reqVO.getRemarks())
                .eqIfPresent(GkProductionManualMaterialDO::getTheoreticalValue2, reqVO.getTheoreticalValue2())
                .eqIfPresent(GkProductionManualMaterialDO::getActualValue2, reqVO.getActualValue2())
                .eqIfPresent(GkProductionManualMaterialDO::getEnabled, reqVO.getEnabled())
                .orderByDesc(GkProductionManualMaterialDO::getId));
    }

}