package cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontract;

import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontract.MmSaleContractDetailDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproject.MmSaleContractProjectDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproprice.MmSaleContractProPriceDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmunitinfo.MmUnitInfoDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontract.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontract.MmSaleContractDO;
import cn.iocoder.yudao.module.ciai.service.mmsalecontract.MmSaleContractService;

@Tag(name = "管理后台 - 营销管理销售合同")
@RestController
@RequestMapping("/ciai/mm-sale-contract")
@Validated
public class MmSaleContractController {

    @Resource
    private MmSaleContractService mmSaleContractService;

    @PostMapping("/create")
    @Operation(summary = "创建营销管理销售合同")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:create')")
    public CommonResult<Long> createMmSaleContract(@Valid @RequestBody MmSaleContractSaveReqVO createReqVO) {
        return success(mmSaleContractService.createMmSaleContract(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新营销管理销售合同")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:update')")
    public CommonResult<Boolean> updateMmSaleContract(@Valid @RequestBody MmSaleContractSaveReqVO updateReqVO) {
        mmSaleContractService.updateMmSaleContract(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除营销管理销售合同")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:delete')")
    public CommonResult<Boolean> deleteMmSaleContract(@RequestParam("id") Long id) {
        mmSaleContractService.deleteMmSaleContract(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得营销管理销售合同")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:query')")
    public CommonResult<MmSaleContractRespVO> getMmSaleContract(@RequestParam("id") Long id) {
        MmSaleContractDO mmSaleContract = mmSaleContractService.getMmSaleContract(id);
        return success(BeanUtils.toBean(mmSaleContract, MmSaleContractRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得营销管理销售合同分页")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:query')")
    public CommonResult<PageResult<MmSaleContractRespVO>> getMmSaleContractPage(@Valid MmSaleContractPageReqVO pageReqVO) {
        PageResult<MmSaleContractDO> pageResult = mmSaleContractService.getMmSaleContractPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MmSaleContractRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得营销管理销售合同和项目信息")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:query')")
    public CommonResult<PageResult<MmSaleContractDetailRespVO>> selectSaleContractAndProject(@Valid MmSaleContractPageReqVO pageReqVO) {
        PageResult<MmSaleContractDetailRespVO> pageResult = mmSaleContractService.selectSaleContractAndProjectPage(pageReqVO);
        return success(pageResult);

    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出营销管理销售合同 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMmSaleContractExcel(@Valid MmSaleContractPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MmSaleContractDO> list = mmSaleContractService.getMmSaleContractPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "营销管理销售合同.xls", "数据", MmSaleContractRespVO.class,
                        BeanUtils.toBean(list, MmSaleContractRespVO.class));
    }

    // 根据合同id查询工程信息
    @GetMapping("/getProjects")
    @Operation(summary = "根据合同id查询工程信息")
    @Parameter(name = "id", description = "合同id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:query')")
    public CommonResult<PageResult<MmSaleContractProjectDO>> getProjects(@RequestParam("id") Long id,int pageNum, int pageSize) {
        IPage<MmSaleContractProjectDO> mmSaleContractDetailDOS = mmSaleContractService.getProjects(id, pageNum, pageSize);
        PageResult<MmSaleContractProjectDO> pageResult = new PageResult<>(mmSaleContractDetailDOS.getRecords(), mmSaleContractDetailDOS.getTotal());
        return success(pageResult);
    }

    // Controller
    @GetMapping("/units")
    @Operation(summary = "获取单位列表")
    public CommonResult<List<MmUnitInfoDO>> getUnitList() {
        return success(mmSaleContractService.getUnitList());
    }

    // 根据工程id查询合同信息
    @GetMapping("/getContracts")
    @Operation(summary = "根据工程id查询合同信息")
    @Parameter(name = "id", description = "工程id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:mm-sale-contract:query')")
    public CommonResult<MmSaleContractRespVO> getContracts(@RequestParam("id") Long id) {
        MmSaleContractDO contract = mmSaleContractService.getContractByProjectId(id);
        return success(BeanUtils.toBean(contract, MmSaleContractRespVO.class));
    }
}