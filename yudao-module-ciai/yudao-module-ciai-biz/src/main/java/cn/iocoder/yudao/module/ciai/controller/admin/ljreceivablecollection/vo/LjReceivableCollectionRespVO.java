package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivablecollection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 应收回款管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LjReceivableCollectionRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "工程ID")
    @ExcelProperty("工程ID")
    private Long projectId;

    @Schema(description = "回款金额")
    @ExcelProperty("回款金额")
    private BigDecimal collectionAmount;

    @Schema(description = "回款方式")
    @ExcelProperty("回款方式")
    private String collectionMethod;

    @Schema(description = "回款日期")
    @ExcelProperty("回款日期")
    private LocalDateTime collectionDate;

    @Schema(description = "会计期")
    @ExcelProperty("会计期")
    private String accountingPeriod;

    @Schema(description = "业务员")
    @ExcelProperty("业务员")
    private String salesperson;

    @Schema(description = "收款人开户行")
    @ExcelProperty("收款人开户行")
    private String payeeBank;

    @Schema(description = "收款人开户名称")
    @ExcelProperty("收款人开户名称")
    private String payeeAccountName;

    @Schema(description = "收款人帐号")
    @ExcelProperty("收款人帐号")
    private String payeeAccountNumber;

    @Schema(description = "付款人开户行")
    @ExcelProperty("付款人开户行")
    private String payerBank;

    @Schema(description = "付款人开户名称")
    @ExcelProperty("付款人开户名称")
    private String payerAccountName;

    @Schema(description = "付款人帐号")
    @ExcelProperty("付款人帐号")
    private String payerAccountNumber;

    @Schema(description = "票据号码")
    @ExcelProperty("票据号码")
    private String billNumber;

    @Schema(description = "票据种类")
    @ExcelProperty("票据种类")
    private String billType;

    @Schema(description = "冲销账款")
    @ExcelProperty("冲销账款")
    private BigDecimal writeOffAmount;

    @Schema(description = "结算状态")
    @ExcelProperty("结算状态")
    private Integer settlementStatus;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String auditor;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

}