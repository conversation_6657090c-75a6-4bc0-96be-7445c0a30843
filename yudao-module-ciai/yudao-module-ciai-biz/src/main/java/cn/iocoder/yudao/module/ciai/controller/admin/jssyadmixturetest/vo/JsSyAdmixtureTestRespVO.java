package cn.iocoder.yudao.module.ciai.controller.admin.jssyadmixturetest.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 外加剂试验 Response VO")
@Data
@ExcelIgnoreUnannotated
public class JsSyAdmixtureTestRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32721")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "单号")
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "试验编号")
    @ExcelProperty("试验编号")
    private String testNo;

    @Schema(description = "委托编号")
    @ExcelProperty("委托编号")
    private String entrustNo;

    @Schema(description = "试样编号")
    @ExcelProperty("试样编号")
    private String sampleNo;

    @Schema(description = "工程名称", example = "赵六")
    @ExcelProperty("工程名称")
    private String projectName;

    @Schema(description = "委托单位")
    @ExcelProperty("委托单位")
    private String entrustUnit;

    @Schema(description = "试验委托人")
    @ExcelProperty("试验委托人")
    private String testClient;

    @Schema(description = "负责人")
    @ExcelProperty("负责人")
    private String responsiblePerson;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String reviewer;

    @Schema(description = "试验人")
    @ExcelProperty("试验人")
    private String tester;

    @Schema(description = "种类编码")
    @ExcelProperty("种类编码")
    private String typeCode;

    @Schema(description = "种类", example = "1")
    @ExcelProperty("种类")
    private String type;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "产品名称", example = "赵六")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "产地")
    @ExcelProperty("产地")
    private String origin;

    @Schema(description = "出厂日期")
    @ExcelProperty("出厂日期")
    private LocalDateTime factoryDate;

    @Schema(description = "代表数量")
    @ExcelProperty("代表数量")
    private String representQuantity;

    @Schema(description = "收样日期")
    @ExcelProperty("收样日期")
    private LocalDateTime sampleDate;

    @Schema(description = "试验日期")
    @ExcelProperty("试验日期")
    private LocalDateTime testDate;

    @Schema(description = "截止日期")
    @ExcelProperty("截止日期")
    private LocalDateTime deadlineDate;

    @Schema(description = "试验项目")
    @ExcelProperty("试验项目")
    private String testItem;

    @Schema(description = "减水率")
    @ExcelProperty("减水率")
    private Double waterReductionRate;

    @Schema(description = "泌水率比")
    @ExcelProperty("泌水率比")
    private Double bleedingRateRatio;

    @Schema(description = "含气量")
    @ExcelProperty("含气量")
    private BigDecimal airContent;

    @Schema(description = "含气量经时损失")
    @ExcelProperty("含气量经时损失")
    private Double airContentLoss;

    @Schema(description = "含水量")
    @ExcelProperty("含水量")
    private Double moistureContent;

    @Schema(description = "密度")
    @ExcelProperty("密度")
    private BigDecimal density;

    @Schema(description = "细度")
    @ExcelProperty("细度")
    private Double fineness;

    @Schema(description = "初凝时间")
    @ExcelProperty("初凝时间")
    private String initialSettingTime;

    @Schema(description = "终凝时间")
    @ExcelProperty("终凝时间")
    private String finalSettingTime;

    @Schema(description = "凝结时间差")
    @ExcelProperty("凝结时间差")
    private Double settingTimeDiff;

    @Schema(description = "固体含量")
    @ExcelProperty("固体含量")
    private Double solidContent;

    @Schema(description = "Ph值")
    @ExcelProperty("Ph值")
    private String phValue;

    @Schema(description = "抗压强度比1天")
    @ExcelProperty("抗压强度比1天")
    private String compStrengthRatio1d;

    @Schema(description = "抗压强度比3天")
    @ExcelProperty("抗压强度比3天")
    private String compStrengthRatio3d;

    @Schema(description = "抗压强度比7天")
    @ExcelProperty("抗压强度比7天")
    private String compStrengthRatio7d;

    @Schema(description = "抗压强度比28天")
    @ExcelProperty("抗压强度比28天")
    private String compStrengthRatio28d;

    @Schema(description = "碱含量")
    @ExcelProperty("碱含量")
    private BigDecimal alkaliContent;

    @Schema(description = "氯离子")
    @ExcelProperty("氯离子")
    private BigDecimal chlorideIon;

    @Schema(description = "试验碱含量")
    @ExcelProperty("试验碱含量")
    private BigDecimal testAlkaliContent;

    @Schema(description = "试验氯含量")
    @ExcelProperty("试验氯含量")
    private BigDecimal testChlorideContent;

    @Schema(description = "坍落度60min")
    @ExcelProperty("坍落度60min")
    private Integer slump60min;

    @Schema(description = "坍落度1h经时变化值")
    @ExcelProperty("坍落度1h经时变化值")
    private Double slump1hChange;

    @Schema(description = "外加剂掺量")
    @ExcelProperty("外加剂掺量")
    private Double admixtureDosage;

    @Schema(description = "受检温度")
    @ExcelProperty("受检温度")
    private Double testTemperature;

    @Schema(description = "结论")
    @ExcelProperty("结论")
    private String conclusion;

    @Schema(description = "型号")
    @ExcelProperty("型号")
    private String model;

    @Schema(description = "其他")
    @ExcelProperty("其他")
    private String other;

    @Schema(description = "报告日期")
    @ExcelProperty("报告日期")
    private LocalDateTime reportDate;

    @Schema(description = "XM11")
    @ExcelProperty("XM11")
    private String xm11;

    @Schema(description = "XM12")
    @ExcelProperty("XM12")
    private String xm12;

    @Schema(description = "XM13")
    @ExcelProperty("XM13")
    private String xm13;

    @Schema(description = "XM21")
    @ExcelProperty("XM21")
    private String xm21;

    @Schema(description = "XM31")
    @ExcelProperty("XM31")
    private String xm31;

    @Schema(description = "XM22")
    @ExcelProperty("XM22")
    private String xm22;

    @Schema(description = "XM32")
    @ExcelProperty("XM32")
    private String xm32;

    @Schema(description = "XM23")
    @ExcelProperty("XM23")
    private String xm23;

    @Schema(description = "XM33")
    @ExcelProperty("XM33")
    private String xm33;

    @Schema(description = "XM14")
    @ExcelProperty("XM14")
    private String xm14;

    @Schema(description = "XM24")
    @ExcelProperty("XM24")
    private String xm24;

    @Schema(description = "MX34")
    @ExcelProperty("MX34")
    private String mx34;

    @Schema(description = "XM15")
    @ExcelProperty("XM15")
    private String xm15;

    @Schema(description = "XM25")
    @ExcelProperty("XM25")
    private String xm25;

    @Schema(description = "MX35")
    @ExcelProperty("MX35")
    private String mx35;

    @Schema(description = "XM16")
    @ExcelProperty("XM16")
    private String xm16;

    @Schema(description = "XM26")
    @ExcelProperty("XM26")
    private String xm26;

    @Schema(description = "MX36")
    @ExcelProperty("MX36")
    private String mx36;

    @Schema(description = "XM17")
    @ExcelProperty("XM17")
    private String xm17;

    @Schema(description = "XM27")
    @ExcelProperty("XM27")
    private String xm27;

    @Schema(description = "MX37")
    @ExcelProperty("MX37")
    private String mx37;

    @Schema(description = "XM18")
    @ExcelProperty("XM18")
    private String xm18;

    @Schema(description = "XM28")
    @ExcelProperty("XM28")
    private String xm28;

    @Schema(description = "MX38")
    @ExcelProperty("MX38")
    private String mx38;

    @Schema(description = "XM41")
    @ExcelProperty("XM41")
    private String xm41;

    @Schema(description = "XM42")
    @ExcelProperty("XM42")
    private String xm42;

    @Schema(description = "XM43")
    @ExcelProperty("XM43")
    private String xm43;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String modifier;

    @Schema(description = "时间")
    @ExcelProperty("时间")
    private LocalDateTime modifyTime;

    @Schema(description = "GT11")
    @ExcelProperty("GT11")
    private String gt11;

    @Schema(description = "GT12")
    @ExcelProperty("GT12")
    private String gt12;

    @Schema(description = "GT13")
    @ExcelProperty("GT13")
    private String gt13;

    @Schema(description = "GTHL1")
    @ExcelProperty("GTHL1")
    private String gtHl1;

    @Schema(description = "GT21")
    @ExcelProperty("GT21")
    private String gt21;

    @Schema(description = "GT22")
    @ExcelProperty("GT22")
    private String gt22;

    @Schema(description = "GT23")
    @ExcelProperty("GT23")
    private String gt23;

    @Schema(description = "GTHL2")
    @ExcelProperty("GTHL2")
    private String gtHl2;

    @Schema(description = "GTHLV")
    @ExcelProperty("GTHLV")
    private String gtHlv;

    @Schema(description = "HS11")
    @ExcelProperty("HS11")
    private String hs11;

    @Schema(description = "HS12")
    @ExcelProperty("HS12")
    private String hs12;

    @Schema(description = "HS13")
    @ExcelProperty("HS13")
    private String hs13;

    @Schema(description = "HSL1")
    @ExcelProperty("HSL1")
    private String hsL1;

    @Schema(description = "HS21")
    @ExcelProperty("HS21")
    private String hs21;

    @Schema(description = "HS22")
    @ExcelProperty("HS22")
    private String hs22;

    @Schema(description = "HS23")
    @ExcelProperty("HS23")
    private String hs23;

    @Schema(description = "HSL2")
    @ExcelProperty("HSL2")
    private String hsL2;

    @Schema(description = "HSLV")
    @ExcelProperty("HSLV")
    private String hsLv;

    @Schema(description = "LD11")
    @ExcelProperty("LD11")
    private Double ld11;

    @Schema(description = "LD12")
    @ExcelProperty("LD12")
    private Double ld12;

    @Schema(description = "LD13")
    @ExcelProperty("LD13")
    private Double ld13;

    @Schema(description = "LD14")
    @ExcelProperty("LD14")
    private Double ld14;

    @Schema(description = "LDD1")
    @ExcelProperty("LDD1")
    private Double ldD1;

    @Schema(description = "LD21")
    @ExcelProperty("LD21")
    private Double ld21;

    @Schema(description = "LD22")
    @ExcelProperty("LD22")
    private Double ld22;

    @Schema(description = "LD23")
    @ExcelProperty("LD23")
    private Double ld23;

    @Schema(description = "LD24")
    @ExcelProperty("LD24")
    private Double ld24;

    @Schema(description = "LDD2")
    @ExcelProperty("LDD2")
    private Double ldD2;

    @Schema(description = "LDDV")
    @ExcelProperty("LDDV")
    private Integer ldDv;

    @Schema(description = "JS11")
    @ExcelProperty("JS11")
    private Double js11;

    @Schema(description = "JS12")
    @ExcelProperty("JS12")
    private Double js12;

    @Schema(description = "JSL1")
    @ExcelProperty("JSL1")
    private Double jsL1;

    @Schema(description = "JS21")
    @ExcelProperty("JS21")
    private Double js21;

    @Schema(description = "JS22")
    @ExcelProperty("JS22")
    private Double js22;

    @Schema(description = "JSL2")
    @ExcelProperty("JSL2")
    private Double jsL2;

    @Schema(description = "JS31")
    @ExcelProperty("JS31")
    private Double js31;

    @Schema(description = "JS32")
    @ExcelProperty("JS32")
    private Double js32;

    @Schema(description = "JSL3")
    @ExcelProperty("JSL3")
    private Double jsL3;

    @Schema(description = "JSLV")
    @ExcelProperty("JSLV")
    private Double jsLv;

    @Schema(description = "XD11")
    @ExcelProperty("XD11")
    private Double xd11;

    @Schema(description = "XD12")
    @ExcelProperty("XD12")
    private Double xd12;

    @Schema(description = "XD1")
    @ExcelProperty("XD1")
    private Double xd1;

    @Schema(description = "XD21")
    @ExcelProperty("XD21")
    private Double xd21;

    @Schema(description = "XD22")
    @ExcelProperty("XD22")
    private Double xd22;

    @Schema(description = "XD2")
    @ExcelProperty("XD2")
    private Double xd2;

    @Schema(description = "XDV")
    @ExcelProperty("XDV")
    private Double xdV;

    @Schema(description = "MD11")
    @ExcelProperty("MD11")
    private BigDecimal md11;

    @Schema(description = "MD1")
    @ExcelProperty("MD1")
    private BigDecimal md1;

    @Schema(description = "MD21")
    @ExcelProperty("MD21")
    private BigDecimal md21;

    @Schema(description = "MD2")
    @ExcelProperty("MD2")
    private BigDecimal md2;

    @Schema(description = "MDV")
    @ExcelProperty("MDV")
    private BigDecimal mdV;

    @Schema(description = "执行标准")
    @ExcelProperty("执行标准")
    private String standard;

    @Schema(description = "试验状态", example = "1")
    @ExcelProperty("试验状态")
    private String testStatus;

    @Schema(description = "使用状态", example = "1")
    @ExcelProperty("使用状态")
    private String usageStatus;

    @Schema(description = "Y")
    @ExcelProperty("Y")
    private Integer yValue;

    @Schema(description = "项目1")
    @ExcelProperty("项目1")
    private String item1;

    @Schema(description = "结果1")
    @ExcelProperty("结果1")
    private String result1;

    @Schema(description = "项目2")
    @ExcelProperty("项目2")
    private String item2;

    @Schema(description = "结果2")
    @ExcelProperty("结果2")
    private String result2;

    @Schema(description = "项目3")
    @ExcelProperty("项目3")
    private String item3;

    @Schema(description = "结果3")
    @ExcelProperty("结果3")
    private String result3;

    @Schema(description = "项目4")
    @ExcelProperty("项目4")
    private String item4;

    @Schema(description = "结果4")
    @ExcelProperty("结果4")
    private String result4;

    @Schema(description = "项目5")
    @ExcelProperty("项目5")
    private String item5;

    @Schema(description = "结果5")
    @ExcelProperty("结果5")
    private String result5;

    @Schema(description = "项目6")
    @ExcelProperty("项目6")
    private String item6;

    @Schema(description = "结果6")
    @ExcelProperty("结果6")
    private String result6;

    @Schema(description = "项目7")
    @ExcelProperty("项目7")
    private String item7;

    @Schema(description = "结果7")
    @ExcelProperty("结果7")
    private String result7;

    @Schema(description = "项目8")
    @ExcelProperty("项目8")
    private String item8;

    @Schema(description = "结果8")
    @ExcelProperty("结果8")
    private String result8;

    @Schema(description = "项目9")
    @ExcelProperty("项目9")
    private String item9;

    @Schema(description = "结果9")
    @ExcelProperty("结果9")
    private String result9;

    @Schema(description = "项目10")
    @ExcelProperty("项目10")
    private String item10;

    @Schema(description = "结果10")
    @ExcelProperty("结果10")
    private String result10;

    @Schema(description = "项目11")
    @ExcelProperty("项目11")
    private String item11;

    @Schema(description = "结果11")
    @ExcelProperty("结果11")
    private String result11;

    @Schema(description = "项目12")
    @ExcelProperty("项目12")
    private String item12;

    @Schema(description = "结果12")
    @ExcelProperty("结果12")
    private String result12;

    @Schema(description = "项目13")
    @ExcelProperty("项目13")
    private String item13;

    @Schema(description = "结果13")
    @ExcelProperty("结果13")
    private String result13;

    @Schema(description = "项目14")
    @ExcelProperty("项目14")
    private String item14;

    @Schema(description = "结果14")
    @ExcelProperty("结果14")
    private String result14;

    @Schema(description = "项目15")
    @ExcelProperty("项目15")
    private String item15;

    @Schema(description = "结果15")
    @ExcelProperty("结果15")
    private String result15;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}