package cn.iocoder.yudao.module.ciai.dal.mysql.emequipmentmanagementplan;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentmanagementplan.EmEquipmentManagementPlanDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentmanagementplan.vo.*;

/**
 * 方案描述 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EmEquipmentManagementPlanMapper extends BaseMapperX<EmEquipmentManagementPlanDO> {

    default PageResult<EmEquipmentManagementPlanDO> selectPage(EmEquipmentManagementPlanPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EmEquipmentManagementPlanDO>()
                .likeIfPresent(EmEquipmentManagementPlanDO::getName, reqVO.getName())
                .orderByDesc(EmEquipmentManagementPlanDO::getId));
    }

}