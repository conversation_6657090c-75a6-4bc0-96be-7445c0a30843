package cn.iocoder.yudao.module.ciai.dal.dataobject.jttravelexpenses;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;

import java.time.LocalDateTime;

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ExpenseTypeSubjectDO extends BaseDO {
    @TableId
    private Integer id;

    private String expenseType;

    private Integer subjectId;

    private Integer departmentId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}