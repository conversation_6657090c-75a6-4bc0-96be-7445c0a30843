package cn.iocoder.yudao.module.ciai.dal.dataobject.carvehicleweight;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 车辆自重 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_car_vehicle_weight")
@KeySequence("ciai_car_vehicle_weight_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarVehicleWeightDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 车辆ID
     */
    private Long vehicleId;
    /**
     * 车辆自重
     */
    private Double vehicleWeight;
    /**
     * 称重时间
     */
    private LocalDateTime weighingTime;

}