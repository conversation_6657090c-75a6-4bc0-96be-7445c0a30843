package cn.iocoder.yudao.module.ciai.service.wzsupplierquotation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierquotation.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplierquotation.WzSupplierQuotationDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.wzsupplierquotation.WzSupplierQuotationMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.WZ_SUPPLIER_QUOTATION_NOT_EXISTS;

/**
 * 供应商材料报价 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WzSupplierQuotationServiceImpl implements WzSupplierQuotationService {

    @Resource
    private WzSupplierQuotationMapper wzSupplierQuotationMapper;

    @Override
    public Integer createWzSupplierQuotation(WzSupplierQuotationSaveReqVO createReqVO) {
        // 插入
        WzSupplierQuotationDO wzSupplierQuotation = BeanUtils.toBean(createReqVO, WzSupplierQuotationDO.class);
        wzSupplierQuotationMapper.insert(wzSupplierQuotation);
        // 返回
        return wzSupplierQuotation.getId();
    }

    @Override
    public void updateWzSupplierQuotation(WzSupplierQuotationSaveReqVO updateReqVO) {
        // 校验存在
        validateWzSupplierQuotationExists(updateReqVO.getId());
        // 更新
        WzSupplierQuotationDO updateObj = BeanUtils.toBean(updateReqVO, WzSupplierQuotationDO.class);
        wzSupplierQuotationMapper.updateById(updateObj);
    }

    @Override
    public void deleteWzSupplierQuotation(Integer id) {
        // 校验存在
        validateWzSupplierQuotationExists(id);
        // 删除
        wzSupplierQuotationMapper.deleteById(id);
    }

    private void validateWzSupplierQuotationExists(Integer id) {
        if (wzSupplierQuotationMapper.selectById(id) == null) {
            throw exception(WZ_SUPPLIER_QUOTATION_NOT_EXISTS);
        }
    }

    @Override
    public WzSupplierQuotationDO getWzSupplierQuotation(Integer id) {
        return wzSupplierQuotationMapper.selectById(id);
    }

    @Override
    public PageResult<WzSupplierQuotationDO> getWzSupplierQuotationPage(WzSupplierQuotationPageReqVO pageReqVO) {
        return wzSupplierQuotationMapper.selectPage(pageReqVO);
    }

    @Override
    public List<WzSupplierQuotationDO> getPurchasePlanByUserId(QuotationByIdReqVO quotation) {
        return wzSupplierQuotationMapper.selectPurchasePlanByUserId(quotation);
    }

    @Override
    public void supplierQuotationOperation(WzSupplierQuotationDO wzSupplierQuotationDO) {
        // 有id就是修改，没有id就是新增
//        if (wzSupplierQuotationDO.getId() != null) {
//            wzSupplierQuotationMapper.updateById(wzSupplierQuotationDO);
//        } else {
//            wzSupplierQuotationMapper.insert(wzSupplierQuotationDO);
//        }
        wzSupplierQuotationMapper.insert1(wzSupplierQuotationDO);
    }

    @Override
    public PageResult<WzSupplierQuotationDO> getComprise(WzSupplierCompReq wzSupplierCompReq) {
        PageResult<WzSupplierQuotationDO> pageResult = new PageResult<>();
        Long total = wzSupplierQuotationMapper.getCount(wzSupplierCompReq);
        List<WzSupplierQuotationDO> wzSupplierQuotationDOList = wzSupplierQuotationMapper.selectComprise(wzSupplierCompReq);
        pageResult.setTotal(total);
        pageResult.setList(wzSupplierQuotationDOList);
        return pageResult;
    }

    @Override
    public PageResult<WzPurchasePlanDO> getPurchasePlan(GetPurchasePlanReqVO purchasePlanReqVO) {
        PageResult<WzPurchasePlanDO> pageResult = new PageResult<>();
        Long total = wzSupplierQuotationMapper.getCount1(purchasePlanReqVO);
        List<WzPurchasePlanDO> wzPurchasePlanDOList = wzSupplierQuotationMapper.selectPurchasePlan(purchasePlanReqVO);
        pageResult.setTotal(total);
        pageResult.setList(wzPurchasePlanDOList);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrice(UpdatePriceReqVO updatePriceReqVO) {
        updatePriceReqVO.setStartDate(LocalDateTime.now());
        wzSupplierQuotationMapper.updatePrice(updatePriceReqVO);
        wzSupplierQuotationMapper.updatePricePlanStatus1(updatePriceReqVO);
        wzSupplierQuotationMapper.updateQuotationStatus(updatePriceReqVO);
    }

    @Override
    public PageResult<WzPurchasePlanDO> getPurchasePlanBySupplier(GetPurchasePlanReqVO purchasePlanReqVO) {
        PageResult<WzPurchasePlanDO> pageResult = new PageResult<>();
        Long total = wzSupplierQuotationMapper.getCount2(purchasePlanReqVO);
        List<WzPurchasePlanDO> wzPurchasePlanDOList = wzSupplierQuotationMapper.selectPurchasePlanBySupplier(purchasePlanReqVO);
        pageResult.setTotal(total);
        pageResult.setList(wzPurchasePlanDOList);
        return pageResult;
    }

    @Override
    public List<PriceTrendRespVO> getPriceTrend(String typeName) {
        return wzSupplierQuotationMapper.selectPriceTrend(typeName);
    }

}