package cn.iocoder.yudao.module.ciai.controller.admin.emdocumentmanage;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.emdocumentmanage.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emdocumentmanage.EmDocumentManageDO;
import cn.iocoder.yudao.module.ciai.service.emdocumentmanage.EmDocumentManageService;

@Tag(name = "管理后台 - 设备管理_文档管理")
@RestController
@RequestMapping("/ciai/em-document-manage")
@Validated
public class EmDocumentManageController {

    @Resource
    private EmDocumentManageService emDocumentManageService;

    @PostMapping("/create")
    @Operation(summary = "创建设备管理_文档管理")
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:create')")
    public CommonResult<Long> createEmDocumentManage(@Valid @RequestBody EmDocumentManageSaveReqVO createReqVO) {
        return success(emDocumentManageService.createEmDocumentManage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备管理_文档管理")
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:update')")
    public CommonResult<Boolean> updateEmDocumentManage(@Valid @RequestBody EmDocumentManageSaveReqVO updateReqVO) {
        emDocumentManageService.updateEmDocumentManage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备管理_文档管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:delete')")
    public CommonResult<Boolean> deleteEmDocumentManage(@RequestParam("id") Long id) {
        emDocumentManageService.deleteEmDocumentManage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备管理_文档管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:query')")
    public CommonResult<EmDocumentManageRespVO> getEmDocumentManage(@RequestParam("id") Long id) {
        EmDocumentManageDO emDocumentManage = emDocumentManageService.getEmDocumentManage(id);
        return success(BeanUtils.toBean(emDocumentManage, EmDocumentManageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备管理_文档管理分页")
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:query')")
    public CommonResult<PageResult<EmDocumentManageRespVO>> getEmDocumentManagePage(@Valid EmDocumentManagePageReqVO pageReqVO) {
        PageResult<EmDocumentManageDO> pageResult = emDocumentManageService.getEmDocumentManagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmDocumentManageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出设备管理_文档管理 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:em-document-manage:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmDocumentManageExcel(@Valid EmDocumentManagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmDocumentManageDO> list = emDocumentManageService.getEmDocumentManagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "设备管理_文档管理.xls", "数据", EmDocumentManageRespVO.class,
                        BeanUtils.toBean(list, EmDocumentManageRespVO.class));
    }

}