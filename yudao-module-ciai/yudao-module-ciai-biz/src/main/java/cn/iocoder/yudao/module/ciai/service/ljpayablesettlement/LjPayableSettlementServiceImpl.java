package cn.iocoder.yudao.module.ciai.service.ljpayablesettlement;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.bpm.enums.task.BpmTaskStatusEnum;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivableaccounting.LjReceivableAccountingDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivablesettlement.LjReceivableSettlementDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseinbound.WzPurchaseInboundDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.wzpurchaseinbound.WzPurchaseInboundMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

import cn.iocoder.yudao.module.ciai.controller.admin.ljpayablesettlement.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljpayablesettlement.LjPayableSettlementDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.ljpayablesettlement.LjPayableSettlementMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 两金管理_应付_结算管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LjPayableSettlementServiceImpl implements LjPayableSettlementService {

    @Resource
    private LjPayableSettlementMapper ljPayableSettlementMapper;

    @Resource
    private WzPurchaseInboundMapper wzPurchaseInboundMapper;


    @Override
    public Long createLjPayableSettlement(LjPayableSettlementSaveReqVO createReqVO) {
        // 插入
        LjPayableSettlementDO ljPayableSettlement = BeanUtils.toBean(createReqVO, LjPayableSettlementDO.class);
        ljPayableSettlementMapper.insert(ljPayableSettlement);
        // 返回
        return ljPayableSettlement.getId();
    }

    @Override
    public void updateLjPayableSettlement(LjPayableSettlementSaveReqVO updateReqVO) {
        // 校验存在
        validateLjPayableSettlementExists(updateReqVO.getId());
        LjPayableSettlementDO ljPayableSettlementDO = ljPayableSettlementMapper.selectById(updateReqVO.getId());
        if (ljPayableSettlementDO.getReviewStatus() == "已审核") {
            throw exception(LJ_PAYABLE_SETTLEMENT_AUDITED);
        }
        // 更新
        LjPayableSettlementDO updateObj = BeanUtils.toBean(updateReqVO, LjPayableSettlementDO.class);
        ljPayableSettlementMapper.updateById(updateObj);
    }

    @Override
    public void updateLjPayableSettlementBatch(List<LjPayableSettlementSaveReqVO> updateReqVO) {
        for (LjPayableSettlementSaveReqVO ljPayableSettlementSaveReqVO : updateReqVO) {
            // 校验存在
            validateLjPayableSettlementExists(ljPayableSettlementSaveReqVO.getId());
            LjPayableSettlementDO ljPayableSettlementDO = ljPayableSettlementMapper.selectById(ljPayableSettlementSaveReqVO.getId());
            if (ljPayableSettlementDO.getReviewStatus() == "已审核") {
                throw exception(LJ_PAYABLE_SETTLEMENT_AUDITED);
            }
        }
        List<LjPayableSettlementDO> updateObj = BeanUtils.toBean(updateReqVO, LjPayableSettlementDO.class);
        ljPayableSettlementMapper.updateBatch(updateObj);
    }

    @Override
    public void deleteLjPayableSettlement(Long id) {
        // 校验存在
        validateLjPayableSettlementExists(id);
        // 删除
        ljPayableSettlementMapper.deleteById(id);
    }

    @Override
    public void deleteLjPayableSettlementByNumber(String settlementNumber) {
        QueryWrapper<LjPayableSettlementDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("settlement_number", settlementNumber).eq("deleted", 0);
        ljPayableSettlementMapper.delete(queryWrapper);
        UpdateWrapper<WzPurchaseInboundDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("inbound_number", settlementNumber).eq("deleted", 0);
        updateWrapper.set("material_settlement", 0).set("material_settlement_number", "").set("transport_settlement", 0).set("transport_settlement_number", "");
        wzPurchaseInboundMapper.update(null, updateWrapper);
    }

    private void validateLjPayableSettlementExists(Long id) {
        if (ljPayableSettlementMapper.selectById(id) == null) {
            throw exception(LJ_PAYABLE_SETTLEMENT_NOT_EXISTS);
        }
    }

    @Override
    public LjPayableSettlementDO getLjPayableSettlement(Long id) {
        return ljPayableSettlementMapper.selectById(id);
    }

    @Override
    public PageResult<LjPayableSettlementDO> getLjPayableSettlementPage(LjPayableSettlementPageReqVO pageReqVO) {
        return ljPayableSettlementMapper.selectPage(pageReqVO);
    }

    @Override
    public List<LjPayableSettlementDO> getLjPayableSettlementByNumber(String settlementNumber) {
        QueryWrapper<LjPayableSettlementDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("settlement_number", settlementNumber).eq("deleted", 0);
        return ljPayableSettlementMapper.selectList(queryWrapper);
    }

    @Override
    public void updateLjPayableSettlementAudited(String settlementNumber, Integer type) {
        if (type == 1) {
            //审核
            UpdateWrapper<LjPayableSettlementDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("settlement_number", settlementNumber).eq("deleted", 0);
            updateWrapper.set("reviewer", SecurityFrameworkUtils.getLoginUserId())
                    .set("review_status", "已审核").set("review_time", LocalDateTime.now());
            ljPayableSettlementMapper.update(null, updateWrapper);
        } else if (type == 2) {
            //撤销审核
            UpdateWrapper<LjPayableSettlementDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("settlement_number", settlementNumber).eq("deleted", 0);
            updateWrapper.set("reviewer", "")
                    .set("review_status", "未审核").set("review_time", null);
            ljPayableSettlementMapper.update(null, updateWrapper);
        }
    }

}