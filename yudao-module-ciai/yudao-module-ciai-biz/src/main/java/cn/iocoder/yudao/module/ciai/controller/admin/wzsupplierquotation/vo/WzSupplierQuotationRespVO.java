package cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierquotation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 供应商材料报价 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WzSupplierQuotationRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32585")
    @ExcelProperty("主键ID")
    private Integer id;

    @Schema(description = "供应商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15324")
    @ExcelProperty("供应商ID")
    private Integer supplierId;

    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("供应商名称")
    private String supplierName;

    @Schema(description = "材料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23577")
    @ExcelProperty("材料ID")
    private Integer materialId;

    @Schema(description = "材料名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("材料名称")
    private String materialName;

    @Schema(description = "报价单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "32664")
    @ExcelProperty("报价单价")
    private BigDecimal unitPrice;

    @Schema(description = "交付时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交付时间")
    private LocalDateTime deliveryDays;

    @Schema(description = "质检检测报告URL", example = "https://www.iocoder.cn")
    @ExcelProperty("质检检测报告URL")
    private String qualityReportUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "送货量")
    @ExcelProperty("送货量")
    private Long deliveryVolume;
    @TableField(exist = false)
    private String typeName;

    @Schema(description = "种类")
    @ExcelProperty("种类")
    private String materialType;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String materialSpec;

}