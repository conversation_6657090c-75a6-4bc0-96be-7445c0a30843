package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableaccounting.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 应收账款结算请求 VO")
@Data
public class SettleRequestVO {
    @Schema(description = "id")
    private Long id;

    @Schema(description = "任务计划ID (可选, 如果按工程结算，可能需要此ID或工程名称)", example = "101")
    private Long taskPlanId;

    @Schema(description = "工程名称 (可选, 用于筛选)", example = "某某大桥项目")
    private String projectName;

    @Schema(description = "结算开始日期")
    private LocalDateTime startDate;

    @Schema(description = "结算结束日期")
    private LocalDateTime endDate;

    @Schema(description = "审核人", example = "张三")
    private String auditor;

    @Schema(description = "审核状态", example = "已审核")
    private String auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "结算状态 (例如：0-未结算, 1-已结算)", example = "1")
    private Integer settlementStatus;

    @Schema(description = "入账日期")
    private LocalDateTime[] accountingDate;

    @Schema(description = "出单日期")
    private LocalDateTime orderDate;

    @Schema(description = "结算单号", example = "JS2023001")
    private String settlementNumber;

}