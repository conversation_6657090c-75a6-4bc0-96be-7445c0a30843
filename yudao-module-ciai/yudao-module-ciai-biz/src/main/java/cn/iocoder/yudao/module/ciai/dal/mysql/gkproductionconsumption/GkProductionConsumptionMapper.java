package cn.iocoder.yudao.module.ciai.dal.mysql.gkproductionconsumption;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionconsumption.GkProductionConsumptionDO;
import com.baomidou.dynamic.datasource.annotation.Master;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.gkproductionconsumption.vo.*;

/**
 * 生产消耗 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface GkProductionConsumptionMapper extends BaseMapperX<GkProductionConsumptionDO> {

    default PageResult<GkProductionConsumptionDO> selectPage(GkProductionConsumptionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<GkProductionConsumptionDO>()
                .eqIfPresent(GkProductionConsumptionDO::getNo, reqVO.getNo())
                .eqIfPresent(GkProductionConsumptionDO::getProductionHost, reqVO.getProductionHost())
                .betweenIfPresent(GkProductionConsumptionDO::getProductionTime, reqVO.getProductionTime())
                .betweenIfPresent(GkProductionConsumptionDO::getOrderTime, reqVO.getOrderTime())
                .eqIfPresent(GkProductionConsumptionDO::getProductNumber, reqVO.getProductNumber())
                .eqIfPresent(GkProductionConsumptionDO::getProductionVolume, reqVO.getProductionVolume())
                .eqIfPresent(GkProductionConsumptionDO::getProductionPersonnel, reqVO.getProductionPersonnel())
                .eqIfPresent(GkProductionConsumptionDO::getCurrentBatch, reqVO.getCurrentBatch())
                .eqIfPresent(GkProductionConsumptionDO::getInvolvedBatchCount, reqVO.getInvolvedBatchCount())
                .eqIfPresent(GkProductionConsumptionDO::getIsMortar, reqVO.getIsMortar())
                .eqIfPresent(GkProductionConsumptionDO::getTaskNumber, reqVO.getTaskNumber())
                .eqIfPresent(GkProductionConsumptionDO::getMixOrderNumber, reqVO.getMixOrderNumber())
                .eqIfPresent(GkProductionConsumptionDO::getTransportOrderNumber, reqVO.getTransportOrderNumber())
                .likeIfPresent(GkProductionConsumptionDO::getProjectName, reqVO.getProjectName())
                .likeIfPresent(GkProductionConsumptionDO::getUnitName, reqVO.getUnitName())
                .eqIfPresent(GkProductionConsumptionDO::getConstructionPart, reqVO.getConstructionPart())
                .eqIfPresent(GkProductionConsumptionDO::getPouringMethod, reqVO.getPouringMethod())
                .eqIfPresent(GkProductionConsumptionDO::getTechnicalRequirements, reqVO.getTechnicalRequirements())
                .eqIfPresent(GkProductionConsumptionDO::getVehicleNumber, reqVO.getVehicleNumber())
                .eqIfPresent(GkProductionConsumptionDO::getDriver, reqVO.getDriver())
                .eqIfPresent(GkProductionConsumptionDO::getVehicleVolume, reqVO.getVehicleVolume())
                .eqIfPresent(GkProductionConsumptionDO::getRemarks, reqVO.getRemarks())
                .likeIfPresent(GkProductionConsumptionDO::getComputerName, reqVO.getComputerName())
                .betweenIfPresent(GkProductionConsumptionDO::getCollectionTime, reqVO.getCollectionTime())
                .eqIfPresent(GkProductionConsumptionDO::getIsManual, reqVO.getIsManual())
                .eqIfPresent(GkProductionConsumptionDO::getPhb1, reqVO.getPhb1())
                .eqIfPresent(GkProductionConsumptionDO::getPhb2, reqVO.getPhb2())
                .eqIfPresent(GkProductionConsumptionDO::getPhb3, reqVO.getPhb3())
                .eqIfPresent(GkProductionConsumptionDO::getJwSync, reqVO.getJwSync())
                .betweenIfPresent(GkProductionConsumptionDO::getMixingTime, reqVO.getMixingTime())
                .eqIfPresent(GkProductionConsumptionDO::getHidden, reqVO.getHidden())
                .eqIfPresent(GkProductionConsumptionDO::getEnabled, reqVO.getEnabled())
                .orderByDesc(GkProductionConsumptionDO::getId));
    }

    // 或者使用XML方式（推荐复杂查询使用）
    @Master
    MaxNoAndBatchDTO selectMaxNoAndBatch(Long host);
}