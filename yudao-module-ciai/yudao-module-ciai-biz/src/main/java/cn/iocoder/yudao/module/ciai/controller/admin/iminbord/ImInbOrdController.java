package cn.iocoder.yudao.module.ciai.controller.admin.iminbord;

import cn.iocoder.yudao.module.ciai.controller.admin.imunitinfo.vo.ImUnitInfoSaveReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.iminbord.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminbord.ImInbOrdDO;
import cn.iocoder.yudao.module.ciai.service.iminbord.ImInbOrdService;

@Tag(name = "管理后台 - 库存管理_入库单")
@RestController
@RequestMapping("/ciai/im-inb-ord")
@Validated
public class ImInbOrdController {

    @Resource
    private ImInbOrdService imInbOrdService;

    @PostMapping("/create")
    @Operation(summary = "创建库存管理_入库单")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:create')")
    public CommonResult<Long> createImInbOrd(@Valid @RequestBody ImInbOrdSaveWithDetailsReqVO createReqVO) {
        return success(imInbOrdService.createImInbOrd(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库存管理_入库单")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:update')")
    public CommonResult<Boolean> updateImInbOrd(@Valid @RequestBody ImInbOrdSaveWithDetailsReqVO updateReqVO) {
        imInbOrdService.updateImInbOrd(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库存管理_入库单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:delete')")
    public CommonResult<Boolean> deleteImInbOrd(@RequestParam("id") Long id) {
        imInbOrdService.deleteImInbOrd(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库存管理_入库单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:query')")
    public CommonResult<ImInbOrdRespVO> getImInbOrd(@RequestParam("id") Long id) {
        ImInbOrdDO imInbOrd = imInbOrdService.getImInbOrd(id);
        return success(BeanUtils.toBean(imInbOrd, ImInbOrdRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库存管理_入库单分页")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:query')")
    public CommonResult<PageResult<ImInbOrdRespVO>> getImInbOrdPage(@Valid ImInbOrdPageReqVO pageReqVO) {
        PageResult<ImInbOrdDO> pageResult = imInbOrdService.getImInbOrdPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImInbOrdRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库存管理_入库单 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImInbOrdExcel(@Valid ImInbOrdPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImInbOrdDO> list = imInbOrdService.getImInbOrdPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库存管理_入库单.xls", "数据", ImInbOrdRespVO.class,
                        BeanUtils.toBean(list, ImInbOrdRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得入库明细分页")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:query')")
    public CommonResult<PageResult<ImInbOrdDetailRespVO>> getImInbOrdDetailPage(@Valid ImInbOrdPageReqVO pageReqVO) {
        PageResult<ImInbOrdDetailRespVO> pageResult = imInbOrdService.getDetailPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 根据入库单编号，获得入库单ID
     * @param inboundOrderNumber
     * @return
     */
    @GetMapping("/get-inbound-order-id")
    @Operation(summary = "根据入库单编号，获得入库单ID")
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:query')")
    public CommonResult<Long> getInboundOrderId(@RequestParam("inboundOrderNumber") String inboundOrderNumber) {
        Long id = imInbOrdService.getInboundOrderId(inboundOrderNumber);
        return success(id);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核入库单")
    @Parameter(name = "id", description = "入库单ID", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:audit')")
    public CommonResult<Boolean> auditImInbOrd(@RequestParam("id") Long id) {
        imInbOrdService.auditImInbOrd(id);
        return success(true);
    }

    @PutMapping("/revoke-audit")
    @Operation(summary = "撤销审核入库单")
    @Parameter(name = "id", description = "入库单ID", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:im-inb-ord:revoke-audit')")
    public CommonResult<Boolean> revokeAuditImInbOrd(@RequestParam("id") Long id) {
        imInbOrdService.revokeAuditImInbOrd(id);
        return success(true);
    }

}