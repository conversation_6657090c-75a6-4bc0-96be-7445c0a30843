package cn.iocoder.yudao.module.ciai.service.wzsupplierdeliveryorder;

import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.wzsupplierdeliveryorder.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplierdeliveryorder.WzSupplierDeliveryOrderDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 物资管理_供应商发货单 Service 接口
 *
 * <AUTHOR>
 */
public interface WzSupplierDeliveryOrderService {

    /**
     * 创建物资管理_供应商发货单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWzSupplierDeliveryOrder(@Valid WzSupplierDeliveryOrderSaveReqVO createReqVO);

    /**
     * 更新物资管理_供应商发货单
     *
     * @param updateReqVO 更新信息
     */
    void updateWzSupplierDeliveryOrder(@Valid WzSupplierDeliveryOrderSaveReqVO updateReqVO);

    /**
     * 删除物资管理_供应商发货单
     *
     * @param id 编号
     */
    void deleteWzSupplierDeliveryOrder(Long id);

    /**
     * 获得物资管理_供应商发货单
     *
     * @param id 编号
     * @return 物资管理_供应商发货单
     */
    WzSupplierDeliveryOrderDO getWzSupplierDeliveryOrder(Long id);

    /**
     * 获得物资管理_供应商发货单分页
     *
     * @param pageReqVO 分页查询
     * @return 物资管理_供应商发货单分页
     */
    PageResult<WzSupplierDeliveryOrderDO> getWzSupplierDeliveryOrderPage(WzSupplierDeliveryOrderPageReqVO pageReqVO);

}