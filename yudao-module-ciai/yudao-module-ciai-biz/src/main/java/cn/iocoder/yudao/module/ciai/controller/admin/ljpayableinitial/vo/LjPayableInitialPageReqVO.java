package cn.iocoder.yudao.module.ciai.controller.admin.ljpayableinitial.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 应付期初账款分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LjPayableInitialPageReqVO extends PageParam {

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "供应商ID")
    private Long supplierId;

    @Schema(description = "发生数量")
    private BigDecimal occurrenceQuantity;

    @Schema(description = "发生金额")
    private BigDecimal occurrenceAmount;

    @Schema(description = "付款数量")
    private BigDecimal paymentQuantity;

    @Schema(description = "付款金额")
    private BigDecimal paymentAmount;

    @Schema(description = "结算数量")
    private BigDecimal settlementQuantity;

    @Schema(description = "结算金额")
    private BigDecimal settlementAmount;

    @Schema(description = "欠款金额")
    private BigDecimal arrearsAmount;

    @Schema(description = "期初日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] initialDate;

    @Schema(description = "单位名称")
    private String supplierName;

}