package cn.iocoder.yudao.module.ciai.service.mmproductcategory;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.mmproductcategory.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmproductcategory.MmProductCategoryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.mmproductcategory.MmProductCategoryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 产品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MmProductCategoryServiceImpl implements MmProductCategoryService {

    @Resource
    private MmProductCategoryMapper mmProductCategoryMapper;

    @Override
    public Long createMmProductCategory(MmProductCategorySaveReqVO createReqVO) {
        // 插入
        MmProductCategoryDO mmProductCategory = BeanUtils.toBean(createReqVO, MmProductCategoryDO.class);
        mmProductCategoryMapper.insert(mmProductCategory);
        // 返回
        return mmProductCategory.getId();
    }

    @Override
    public void updateMmProductCategory(MmProductCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateMmProductCategoryExists(updateReqVO.getId());
        // 更新
        MmProductCategoryDO updateObj = BeanUtils.toBean(updateReqVO, MmProductCategoryDO.class);
        mmProductCategoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteMmProductCategory(Long id) {
        // 校验存在
        validateMmProductCategoryExists(id);
        // 删除
        mmProductCategoryMapper.deleteById(id);
    }

    private void validateMmProductCategoryExists(Long id) {
        if (mmProductCategoryMapper.selectById(id) == null) {
            throw exception(MM_PRODUCT_CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public MmProductCategoryDO getMmProductCategory(Long id) {
        return mmProductCategoryMapper.selectById(id);
    }

    @Override
    public PageResult<MmProductCategoryDO> getMmProductCategoryPage(MmProductCategoryPageReqVO pageReqVO) {
        return mmProductCategoryMapper.selectPage(pageReqVO);
    }

}