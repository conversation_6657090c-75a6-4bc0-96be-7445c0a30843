package cn.iocoder.yudao.module.ciai.service.wzsupplier;

import java.util.*;

import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.wzsupplier.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzsupplier.WzSupplierDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 物资管理供应商信息 Service 接口
 *
 * <AUTHOR>
 */
public interface WzSupplierService {

    /**
     * 创建物资管理供应商信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createWzSupplier(@Valid WzSupplierSaveReqVO createReqVO);

    /**
     * 更新物资管理供应商信息
     *
     * @param updateReqVO 更新信息
     */
    void updateWzSupplier(@Valid WzSupplierSaveReqVO updateReqVO);

    /**
     * 删除物资管理供应商信息
     *
     * @param id 编号
     */
    void deleteWzSupplier(Long id);

    /**
     * 获得物资管理供应商信息
     *
     * @param id 编号
     * @return 物资管理供应商信息
     */
    WzSupplierDO getWzSupplier(Long id);

    /**
     * 获得物资管理供应商信息分页
     *
     * @param pageReqVO 分页查询
     * @return 物资管理供应商信息分页
     */
    PageResult<WzSupplierDO> getWzSupplierPage(WzSupplierPageReqVO pageReqVO);

    /**
     * 获得物资管理供应商信息分页已有合同
     *
     * @param pageReqVO 分页查询
     * @return 物资管理供应商信息分页
     */
    PageResult<WzSupplierDO> getWzSupplierPageHasContract(@Valid WzSupplierPageReqVO pageReqVO);
}