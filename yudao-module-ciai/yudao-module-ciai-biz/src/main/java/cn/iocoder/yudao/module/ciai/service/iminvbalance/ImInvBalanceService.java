package cn.iocoder.yudao.module.ciai.service.iminvbalance;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.iminvbalance.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.iminvbalance.ImInvBalanceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 库存余额 Service 接口
 *
 * <AUTHOR>
 */
public interface ImInvBalanceService {

    /**
     * 创建库存余额
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImInvBalance(@Valid ImInvBalanceSaveReqVO createReqVO);

    /**
     * 更新库存余额
     *
     * @param updateReqVO 更新信息
     */
    void updateImInvBalance(@Valid ImInvBalanceSaveReqVO updateReqVO);

    /**
     * 删除库存余额
     *
     * @param id 编号
     */
    void deleteImInvBalance(Long id);

    /**
     * 获得库存余额
     *
     * @param id 编号
     * @return 库存余额
     */
    ImInvBalanceDO getImInvBalance(Long id);

    /**
     * 获得库存余额分页
     *
     * @param pageReqVO 分页查询
     * @return 库存余额分页
     */
    PageResult<ImInvBalanceDO> getImInvBalancePage(ImInvBalancePageReqVO pageReqVO);

    /**
     * 获得库存报表
     * @param reportReqVO
     * @return
     */
    List<ImInvReportRespVO> getInventoryReport(ImInvReportReqVO reportReqVO);
}