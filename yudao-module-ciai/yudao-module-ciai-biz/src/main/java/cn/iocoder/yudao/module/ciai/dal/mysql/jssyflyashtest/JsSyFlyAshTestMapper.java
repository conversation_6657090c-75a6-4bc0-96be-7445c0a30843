package cn.iocoder.yudao.module.ciai.dal.mysql.jssyflyashtest;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssyflyashtest.JsSyFlyAshTestDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.jssyflyashtest.vo.*;

/**
 * 粉煤灰试验 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JsSyFlyAshTestMapper extends BaseMapperX<JsSyFlyAshTestDO> {

    default PageResult<JsSyFlyAshTestDO> selectPage(JsSyFlyAshTestPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JsSyFlyAshTestDO>()
                .eqIfPresent(JsSyFlyAshTestDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(JsSyFlyAshTestDO::getTestNo, reqVO.getTestNo())
                .eqIfPresent(JsSyFlyAshTestDO::getEntrustNo, reqVO.getEntrustNo())
                .eqIfPresent(JsSyFlyAshTestDO::getSampleNo, reqVO.getSampleNo())
                .eqIfPresent(JsSyFlyAshTestDO::getEntrustUnit, reqVO.getEntrustUnit())
                .eqIfPresent(JsSyFlyAshTestDO::getTestClient, reqVO.getTestClient())
                .eqIfPresent(JsSyFlyAshTestDO::getResponsiblePerson, reqVO.getResponsiblePerson())
                .eqIfPresent(JsSyFlyAshTestDO::getReviewer, reqVO.getReviewer())
                .eqIfPresent(JsSyFlyAshTestDO::getTester, reqVO.getTester())
                .likeIfPresent(JsSyFlyAshTestDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(JsSyFlyAshTestDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(JsSyFlyAshTestDO::getType, reqVO.getType())
                .eqIfPresent(JsSyFlyAshTestDO::getSpecification, reqVO.getSpecification())
                .betweenIfPresent(JsSyFlyAshTestDO::getFactoryDate, reqVO.getFactoryDate())
                .betweenIfPresent(JsSyFlyAshTestDO::getEntryDate, reqVO.getEntryDate())
                .betweenIfPresent(JsSyFlyAshTestDO::getSampleDate, reqVO.getSampleDate())
                .eqIfPresent(JsSyFlyAshTestDO::getRepresentQuantity, reqVO.getRepresentQuantity())
                .eqIfPresent(JsSyFlyAshTestDO::getLossOnIgnition, reqVO.getLossOnIgnition())
                .eqIfPresent(JsSyFlyAshTestDO::getFineness, reqVO.getFineness())
                .eqIfPresent(JsSyFlyAshTestDO::getWaterDemandRatio, reqVO.getWaterDemandRatio())
                .eqIfPresent(JsSyFlyAshTestDO::getConclusion, reqVO.getConclusion())
                .betweenIfPresent(JsSyFlyAshTestDO::getTestDate, reqVO.getTestDate())
                .betweenIfPresent(JsSyFlyAshTestDO::getDeadlineDate, reqVO.getDeadlineDate())
                .likeIfPresent(JsSyFlyAshTestDO::getName, reqVO.getName())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressiveStrengthRatio28d, reqVO.getCompressiveStrengthRatio28d())
                .eqIfPresent(JsSyFlyAshTestDO::getMoistureContent, reqVO.getMoistureContent())
                .betweenIfPresent(JsSyFlyAshTestDO::getReportDate, reqVO.getReportDate())
                .eqIfPresent(JsSyFlyAshTestDO::getXm1, reqVO.getXm1())
                .eqIfPresent(JsSyFlyAshTestDO::getXm2, reqVO.getXm2())
                .eqIfPresent(JsSyFlyAshTestDO::getFactoryNo, reqVO.getFactoryNo())
                .eqIfPresent(JsSyFlyAshTestDO::getXm11, reqVO.getXm11())
                .eqIfPresent(JsSyFlyAshTestDO::getXm21, reqVO.getXm21())
                .eqIfPresent(JsSyFlyAshTestDO::getXm31, reqVO.getXm31())
                .eqIfPresent(JsSyFlyAshTestDO::getXm12, reqVO.getXm12())
                .eqIfPresent(JsSyFlyAshTestDO::getXm22, reqVO.getXm22())
                .eqIfPresent(JsSyFlyAshTestDO::getXm32, reqVO.getXm32())
                .eqIfPresent(JsSyFlyAshTestDO::getXm13, reqVO.getXm13())
                .eqIfPresent(JsSyFlyAshTestDO::getXm23, reqVO.getXm23())
                .eqIfPresent(JsSyFlyAshTestDO::getXm33, reqVO.getXm33())
                .eqIfPresent(JsSyFlyAshTestDO::getXm14, reqVO.getXm14())
                .eqIfPresent(JsSyFlyAshTestDO::getXm24, reqVO.getXm24())
                .eqIfPresent(JsSyFlyAshTestDO::getXm34, reqVO.getXm34())
                .eqIfPresent(JsSyFlyAshTestDO::getXm15, reqVO.getXm15())
                .eqIfPresent(JsSyFlyAshTestDO::getXm25, reqVO.getXm25())
                .eqIfPresent(JsSyFlyAshTestDO::getXm35, reqVO.getXm35())
                .eqIfPresent(JsSyFlyAshTestDO::getXm16, reqVO.getXm16())
                .eqIfPresent(JsSyFlyAshTestDO::getXm26, reqVO.getXm26())
                .eqIfPresent(JsSyFlyAshTestDO::getXm36, reqVO.getXm36())
                .eqIfPresent(JsSyFlyAshTestDO::getXm17, reqVO.getXm17())
                .eqIfPresent(JsSyFlyAshTestDO::getXm27, reqVO.getXm27())
                .eqIfPresent(JsSyFlyAshTestDO::getXm37, reqVO.getXm37())
                .eqIfPresent(JsSyFlyAshTestDO::getXm18, reqVO.getXm18())
                .eqIfPresent(JsSyFlyAshTestDO::getXm28, reqVO.getXm28())
                .eqIfPresent(JsSyFlyAshTestDO::getXm38, reqVO.getXm38())
                .eqIfPresent(JsSyFlyAshTestDO::getAlkaliContent, reqVO.getAlkaliContent())
                .eqIfPresent(JsSyFlyAshTestDO::getChlorideIon, reqVO.getChlorideIon())
                .eqIfPresent(JsSyFlyAshTestDO::getAttachmentStatus, reqVO.getAttachmentStatus())
                .eqIfPresent(JsSyFlyAshTestDO::getModifier, reqVO.getModifier())
                .betweenIfPresent(JsSyFlyAshTestDO::getModifyTime, reqVO.getModifyTime())
                .eqIfPresent(JsSyFlyAshTestDO::getXd11, reqVO.getXd11())
                .eqIfPresent(JsSyFlyAshTestDO::getXd12, reqVO.getXd12())
                .eqIfPresent(JsSyFlyAshTestDO::getXdResidue1, reqVO.getXdResidue1())
                .eqIfPresent(JsSyFlyAshTestDO::getXdCorrection, reqVO.getXdCorrection())
                .eqIfPresent(JsSyFlyAshTestDO::getXd21, reqVO.getXd21())
                .eqIfPresent(JsSyFlyAshTestDO::getXd22, reqVO.getXd22())
                .eqIfPresent(JsSyFlyAshTestDO::getXdResidue2, reqVO.getXdResidue2())
                .eqIfPresent(JsSyFlyAshTestDO::getXdV, reqVO.getXdV())
                .eqIfPresent(JsSyFlyAshTestDO::getXs11, reqVO.getXs11())
                .eqIfPresent(JsSyFlyAshTestDO::getXs12, reqVO.getXs12())
                .eqIfPresent(JsSyFlyAshTestDO::getXs13, reqVO.getXs13())
                .eqIfPresent(JsSyFlyAshTestDO::getXs14, reqVO.getXs14())
                .eqIfPresent(JsSyFlyAshTestDO::getXsL1, reqVO.getXsL1())
                .eqIfPresent(JsSyFlyAshTestDO::getXs21, reqVO.getXs21())
                .eqIfPresent(JsSyFlyAshTestDO::getXs22, reqVO.getXs22())
                .eqIfPresent(JsSyFlyAshTestDO::getXs23, reqVO.getXs23())
                .eqIfPresent(JsSyFlyAshTestDO::getXsL2, reqVO.getXsL2())
                .eqIfPresent(JsSyFlyAshTestDO::getXsLb, reqVO.getXsLb())
                .eqIfPresent(JsSyFlyAshTestDO::getSs11, reqVO.getSs11())
                .eqIfPresent(JsSyFlyAshTestDO::getSs12, reqVO.getSs12())
                .eqIfPresent(JsSyFlyAshTestDO::getSs13, reqVO.getSs13())
                .eqIfPresent(JsSyFlyAshTestDO::getSs14, reqVO.getSs14())
                .eqIfPresent(JsSyFlyAshTestDO::getSs1PreWeight, reqVO.getSs1PreWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSs1TotalWeight, reqVO.getSs1TotalWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSs15, reqVO.getSs15())
                .eqIfPresent(JsSyFlyAshTestDO::getSs16, reqVO.getSs16())
                .eqIfPresent(JsSyFlyAshTestDO::getSs17, reqVO.getSs17())
                .eqIfPresent(JsSyFlyAshTestDO::getSs18, reqVO.getSs18())
                .eqIfPresent(JsSyFlyAshTestDO::getSs1PostWeight, reqVO.getSs1PostWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSsL1, reqVO.getSsL1())
                .eqIfPresent(JsSyFlyAshTestDO::getSs21, reqVO.getSs21())
                .eqIfPresent(JsSyFlyAshTestDO::getSs22, reqVO.getSs22())
                .eqIfPresent(JsSyFlyAshTestDO::getSs23, reqVO.getSs23())
                .eqIfPresent(JsSyFlyAshTestDO::getSs24, reqVO.getSs24())
                .eqIfPresent(JsSyFlyAshTestDO::getSs2PreWeight, reqVO.getSs2PreWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSs2TotalWeight, reqVO.getSs2TotalWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSs2PostWeight, reqVO.getSs2PostWeight())
                .eqIfPresent(JsSyFlyAshTestDO::getSs25, reqVO.getSs25())
                .eqIfPresent(JsSyFlyAshTestDO::getSs26, reqVO.getSs26())
                .eqIfPresent(JsSyFlyAshTestDO::getSs27, reqVO.getSs27())
                .eqIfPresent(JsSyFlyAshTestDO::getSs28, reqVO.getSs28())
                .eqIfPresent(JsSyFlyAshTestDO::getSsL2, reqVO.getSsL2())
                .eqIfPresent(JsSyFlyAshTestDO::getStandard, reqVO.getStandard())
                .eqIfPresent(JsSyFlyAshTestDO::getTestStatus, reqVO.getTestStatus())
                .eqIfPresent(JsSyFlyAshTestDO::getUsageStatus, reqVO.getUsageStatus())
                .eqIfPresent(JsSyFlyAshTestDO::getSelected, reqVO.getSelected())
                .eqIfPresent(JsSyFlyAshTestDO::getPrinted, reqVO.getPrinted())
                .eqIfPresent(JsSyFlyAshTestDO::getYValue, reqVO.getYValue())
                .eqIfPresent(JsSyFlyAshTestDO::getStationCode, reqVO.getStationCode())
                .eqIfPresent(JsSyFlyAshTestDO::getTester2, reqVO.getTester2())
                .eqIfPresent(JsSyFlyAshTestDO::getStandard2, reqVO.getStandard2())
                .eqIfPresent(JsSyFlyAshTestDO::getProjectType, reqVO.getProjectType())
                .eqIfPresent(JsSyFlyAshTestDO::getFactoryBrand, reqVO.getFactoryBrand())
                .eqIfPresent(JsSyFlyAshTestDO::getActivityIndex7d, reqVO.getActivityIndex7d())
                .eqIfPresent(JsSyFlyAshTestDO::getActivityIndex28d, reqVO.getActivityIndex28d())
                .eqIfPresent(JsSyFlyAshTestDO::getTestStatus7d, reqVO.getTestStatus7d())
                .eqIfPresent(JsSyFlyAshTestDO::getTestStatus28d, reqVO.getTestStatus28d())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad71, reqVO.getLoad71())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad72, reqVO.getLoad72())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad73, reqVO.getLoad73())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad74, reqVO.getLoad74())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad75, reqVO.getLoad75())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad76, reqVO.getLoad76())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive71, reqVO.getCompressive71())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive72, reqVO.getCompressive72())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive73, reqVO.getCompressive73())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive74, reqVO.getCompressive74())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive75, reqVO.getCompressive75())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive76, reqVO.getCompressive76())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive7v, reqVO.getCompressive7v())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad71, reqVO.getBaseLoad71())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad72, reqVO.getBaseLoad72())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad73, reqVO.getBaseLoad73())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad74, reqVO.getBaseLoad74())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad75, reqVO.getBaseLoad75())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad76, reqVO.getBaseLoad76())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive71, reqVO.getBaseCompressive71())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive72, reqVO.getBaseCompressive72())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive73, reqVO.getBaseCompressive73())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive74, reqVO.getBaseCompressive74())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive75, reqVO.getBaseCompressive75())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive76, reqVO.getBaseCompressive76())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive7v, reqVO.getBaseCompressive7v())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad281, reqVO.getBaseLoad281())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad282, reqVO.getBaseLoad282())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad283, reqVO.getBaseLoad283())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad284, reqVO.getBaseLoad284())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad285, reqVO.getBaseLoad285())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseLoad286, reqVO.getBaseLoad286())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad281, reqVO.getLoad281())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad282, reqVO.getLoad282())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad283, reqVO.getLoad283())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad284, reqVO.getLoad284())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad285, reqVO.getLoad285())
                .eqIfPresent(JsSyFlyAshTestDO::getLoad286, reqVO.getLoad286())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive281, reqVO.getCompressive281())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive282, reqVO.getCompressive282())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive283, reqVO.getCompressive283())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive284, reqVO.getCompressive284())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive285, reqVO.getCompressive285())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive286, reqVO.getCompressive286())
                .eqIfPresent(JsSyFlyAshTestDO::getCompressive28v, reqVO.getCompressive28v())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive281, reqVO.getBaseCompressive281())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive282, reqVO.getBaseCompressive282())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive283, reqVO.getBaseCompressive283())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive284, reqVO.getBaseCompressive284())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive285, reqVO.getBaseCompressive285())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive286, reqVO.getBaseCompressive286())
                .eqIfPresent(JsSyFlyAshTestDO::getBaseCompressive28v, reqVO.getBaseCompressive28v())
                .eqIfPresent(JsSyFlyAshTestDO::getBbmjv, reqVO.getBbmjv())
                .eqIfPresent(JsSyFlyAshTestDO::getHs1, reqVO.getHs1())
                .eqIfPresent(JsSyFlyAshTestDO::getHs2, reqVO.getHs2())
                .eqIfPresent(JsSyFlyAshTestDO::getHs3, reqVO.getHs3())
                .eqIfPresent(JsSyFlyAshTestDO::getHsL, reqVO.getHsL())
                .betweenIfPresent(JsSyFlyAshTestDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JsSyFlyAshTestDO::getId));
    }

}