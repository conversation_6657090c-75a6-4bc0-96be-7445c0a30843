package cn.iocoder.yudao.module.ciai.controller.admin.mmproductname;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.mmproductname.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmproductname.MmProductNameDO;
import cn.iocoder.yudao.module.ciai.service.mmproductname.MmProductNameService;

@Tag(name = "管理后台 - 产品名称")
@RestController
@RequestMapping("/ciai/mm-product-name")
@Validated
public class MmProductNameController {

    @Resource
    private MmProductNameService mmProductNameService;

    @PostMapping("/create")
    @Operation(summary = "创建产品名称")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:create')")
    public CommonResult<Long> createMmProductName(@Valid @RequestBody MmProductNameSaveReqVO createReqVO) {
        return success(mmProductNameService.createMmProductName(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品名称")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:update')")
    public CommonResult<Boolean> updateMmProductName(@Valid @RequestBody MmProductNameSaveReqVO updateReqVO) {
        mmProductNameService.updateMmProductName(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品名称")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:delete')")
    public CommonResult<Boolean> deleteMmProductName(@RequestParam("id") Long id) {
        mmProductNameService.deleteMmProductName(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品名称")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:query')")
    public CommonResult<MmProductNameRespVO> getMmProductName(@RequestParam("id") Long id) {
        MmProductNameDO mmProductName = mmProductNameService.getMmProductName(id);
        return success(BeanUtils.toBean(mmProductName, MmProductNameRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品名称分页")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:query')")
    public CommonResult<PageResult<MmProductNameRespVO>> getMmProductNamePage(@Valid MmProductNamePageReqVO pageReqVO) {
        PageResult<MmProductNameDO> pageResult = mmProductNameService.getMmProductNamePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MmProductNameRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品名称 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-name:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMmProductNameExcel(@Valid MmProductNamePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MmProductNameDO> list = mmProductNameService.getMmProductNamePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品名称.xls", "数据", MmProductNameRespVO.class,
                        BeanUtils.toBean(list, MmProductNameRespVO.class));
    }

}