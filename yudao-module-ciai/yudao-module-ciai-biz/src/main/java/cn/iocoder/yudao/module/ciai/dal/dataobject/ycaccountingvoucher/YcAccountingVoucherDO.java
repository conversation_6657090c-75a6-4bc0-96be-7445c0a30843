package cn.iocoder.yudao.module.ciai.dal.dataobject.ycaccountingvoucher;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 记账凭证 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_yc_accounting_voucher")
@KeySequence("ciai_yc_accounting_voucher_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YcAccountingVoucherDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 记账状态
     */
    private String status;
    /**
     * 凭证号
     */
    private String voucherNo;
    /**
     * 凭证日期
     */
    private LocalDateTime voucherDate;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    /**
     * 记账人
     */
    private String bookkeeper;
    /**
     * 记账时间
     */
    private LocalDateTime bookkeepingTime;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务单据号
     */
    private String businessId;
    /**
     * 摘要
     */
    private String description;
    /**
     * 会计期间
     */
    private Long periodId;
    /**
     * 凭证类型
     */
    private String voucherType;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 附件数量
     */
    private Integer attachmentCount;
    /**
     * 备注
     */
    private String remark;

}