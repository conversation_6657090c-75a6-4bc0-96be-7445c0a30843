package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivablecollection.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "管理后台 - 应收回款管理详细信息 Response VO")
public class LjReceivableCollectionDetailRespVO {

    @Schema(description = "回款ID", required = true, example = "1024")
    private Long id;

    @Schema(description = "工程名称", example = "中海油项目")
    private String projectName;

    @Schema(description = "合同名称", example = "2023年度合同")
    private String contractName;

    @Schema(description = "施工单位名称", example = "某某建设集团")
    private String constructionUnitName;

    @Schema(description = "建设单位名称", example = "某某地产开发")
    private String buildUnitName;

    @Schema(description = "结算单位名称", example = "某某财务公司")
    private String settlementUnitName;

    @Schema(description = "内部合同编号", example = "INT-2023-001")
    private String internalContractNumber;

    @Schema(description = "工程地址", example = "北京市朝阳区")
    private String projectAddress;

    @Schema(description = "回款金额", example = "10000.50")
    private BigDecimal collectionAmount;

    @Schema(description = "回款方式", example = "银行转账")
    private String collectionMethod;

    @Schema(description = "回款日期", example = "2023-10-01")
    private String collectionDate; // SQL中已格式化为字符串

    @Schema(description = "业务员", example = "张三")
    private String salesman;

    @Schema(description = "会计期", example = "2023-10")
    private String accountingPeriod;

    @Schema(description = "收款人开户行", example = "中国工商银行XX支行")
    private String payeeBank;

    @Schema(description = "收款人开户名称", example = "A公司")
    private String payeeAccountName;

    @Schema(description = "收款人帐号", example = "6222020000012345678")
    private String payeeAccountNumber;

    @Schema(description = "付款人开户行", example = "中国建设银行YY支行")
    private String payerBank;

    @Schema(description = "付款人开户名称", example = "B客户")
    private String payerAccountName;

    @Schema(description = "付款人帐号", example = "6227000000098765432")
    private String payerAccountNumber;

    @Schema(description = "票据号码", example = "PJ123456789")
    private String billNumber;

    @Schema(description = "票据种类", example = "银行承兑汇票")
    private String billType;

    @Schema(description = "冲销账款", example = "500.00")
    private BigDecimal writeOffAmount;

    @Schema(description = "结算状态", example = "已结算")
    private String settlementStatus;

    @Schema(description = "备注", example = "这是一条回款备注")
    private String remark;

    @Schema(description = "创建人", example = "admin")
    private String creator;

    @Schema(description = "创建时间", example = "2023-10-01 10:00:00")
    private String createTime; // SQL中已格式化为字符串

    @Schema(description = "修改人", example = "admin")
    private String updater;

    @Schema(description = "修改时间", example = "2023-10-02 11:00:00")
    private String updateTime; // SQL中已格式化为字符串
}