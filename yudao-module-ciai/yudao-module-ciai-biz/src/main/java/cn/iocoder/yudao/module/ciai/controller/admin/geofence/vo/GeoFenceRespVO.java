package cn.iocoder.yudao.module.ciai.controller.admin.geofence.vo;

import cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo.LocationPointVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 电子围栏 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GeoFenceRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private Long id;

    @Schema(description = "围栏名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("围栏名称")
    private String name;

    @Schema(description = "坐标")
    private List<LocationPointVO> multiPoints;
}