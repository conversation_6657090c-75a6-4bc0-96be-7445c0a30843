package cn.iocoder.yudao.module.ciai.controller.admin.jscompressiontest.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 技术管理_试块抗压试验新增/修改 Request VO")
@Data
public class JsCompressionTestSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13882")
    private Long id;

    @Schema(description = "任务计划ID", example = "5427")
    private Long taskPlanId;

    @Schema(description = "发货记录ID", example = "16973")
    private Long deliveryRecordId;

    @Schema(description = "样品编号")
    private String sampleNo;

    @Schema(description = "抗渗编号")
    private String impermeabilityNo;

    @Schema(description = "产品分类")
    private String productCategory;

    @Schema(description = "取样地点")
    private String samplingLocation;

    @Schema(description = "取样方式")
    private String samplingMethod;

    @Schema(description = "边长")
    private String edgeLength;

    @Schema(description = "尺寸")
    private String size;

    @Schema(description = "样品状态", example = "1")
    private String sampleStatus;

    @Schema(description = "养护方式")
    private String curingMethod;

    @Schema(description = "组数", example = "13808")
    private String groupCount;

    @Schema(description = "方量")
    private Double volume;

    @Schema(description = "代表数量")
    private String representativeQuantity;

    @Schema(description = "取样人")
    private String sampler;

    @Schema(description = "取样时间")
    private LocalDateTime samplingTime;

    @Schema(description = "龄期")
    private String age;

    @Schema(description = "实测坍落度")
    private String measuredSlump;

    @Schema(description = "试验编号")
    private String testNo;

    @Schema(description = "荷载1")
    private Integer load1;

    @Schema(description = "荷载2")
    private Integer load2;

    @Schema(description = "荷载3")
    private Integer load3;

    @Schema(description = "荷载最大值")
    private BigDecimal loadMax;

    @Schema(description = "荷载最小值")
    private BigDecimal loadMin;

    @Schema(description = "荷载中间值")
    private BigDecimal loadMiddle;

    @Schema(description = "平均荷载")
    private BigDecimal loadAverage;

    @Schema(description = "强度1")
    private BigDecimal strength1;

    @Schema(description = "强度2")
    private BigDecimal strength2;

    @Schema(description = "强度3")
    private BigDecimal strength3;

    @Schema(description = "平均强度")
    private BigDecimal strengthAverage;

    @Schema(description = "强度值")
    private BigDecimal strengthValue;

    @Schema(description = "强度比")
    private Double strengthRatio;

    @Schema(description = "负责人")
    private String personInCharge;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "试验人")
    private String tester;

    @Schema(description = "试验人2")
    private String tester2;

    @Schema(description = "成型日期")
    private LocalDateTime moldingDate;

    @Schema(description = "试验日期")
    private LocalDateTime testDate;

    @Schema(description = "报告日期")
    private LocalDateTime reportDate;

    @Schema(description = "结论")
    private String conclusion;

    @Schema(description = "试验状态", example = "1")
    private String testStatus;

    @Schema(description = "作废", example = "10726")
    private Integer isVoid;

}