package cn.iocoder.yudao.module.ciai.service.emequipmentcontentplan;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentcontentplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentcontentplan.EmEquipmentContentPlanDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentcontentplan.EmEquipmentContentPlanDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.emequipmentcontentplan.EmEquipmentContentPlanMapper;
import cn.iocoder.yudao.module.ciai.dal.mysql.emequipmentcontentplan.EmEquipmentContentPlanDetailMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 设备管理_内容计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmEquipmentContentPlanServiceImpl implements EmEquipmentContentPlanService {

    @Resource
    private EmEquipmentContentPlanMapper emEquipmentContentPlanMapper;
    @Resource
    private EmEquipmentContentPlanDetailMapper emEquipmentContentPlanDetailMapper;

    @Override
    public Long createEmEquipmentContentPlan(EmEquipmentContentPlanSaveReqVO createReqVO) {
        // 插入
        EmEquipmentContentPlanDO emEquipmentContentPlan = BeanUtils.toBean(createReqVO, EmEquipmentContentPlanDO.class);
        emEquipmentContentPlanMapper.insert(emEquipmentContentPlan);
        // 返回
        return emEquipmentContentPlan.getId();
    }

    @Override
    public void updateEmEquipmentContentPlan(EmEquipmentContentPlanSaveReqVO updateReqVO) {
        // 校验存在
        validateEmEquipmentContentPlanExists(updateReqVO.getId());
        // 更新
        EmEquipmentContentPlanDO updateObj = BeanUtils.toBean(updateReqVO, EmEquipmentContentPlanDO.class);
        emEquipmentContentPlanMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEmEquipmentContentPlan(Long id) {
        // 校验存在
        validateEmEquipmentContentPlanExists(id);
        // 删除
        emEquipmentContentPlanMapper.deleteById(id);

        // 删除子表
        deleteEmEquipmentContentPlanDetailByContentPlanId(id);
    }

    private void validateEmEquipmentContentPlanExists(Long id) {
        if (emEquipmentContentPlanMapper.selectById(id) == null) {
            throw exception(EM_EQUIPMENT_CONTENT_PLAN_NOT_EXISTS);
        }
    }

    @Override
    public EmEquipmentContentPlanDO getEmEquipmentContentPlan(Long id) {
        return emEquipmentContentPlanMapper.selectById(id);
    }

    @Override
    public PageResult<EmEquipmentContentPlanDO> getEmEquipmentContentPlanPage(EmEquipmentContentPlanPageReqVO pageReqVO) {
        return emEquipmentContentPlanMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（设备管理_内容计划明细） ====================

    @Override
    public PageResult<EmEquipmentContentPlanDetailDO> getEmEquipmentContentPlanDetailPage(PageParam pageReqVO, Long contentPlanId) {
        return emEquipmentContentPlanDetailMapper.selectPage(pageReqVO, contentPlanId);
    }

    @Override
    public Long createEmEquipmentContentPlanDetail(EmEquipmentContentPlanDetailDO emEquipmentContentPlanDetail) {
        emEquipmentContentPlanDetailMapper.insert(emEquipmentContentPlanDetail);
        return emEquipmentContentPlanDetail.getId();
    }

    @Override
    public void updateEmEquipmentContentPlanDetail(EmEquipmentContentPlanDetailDO emEquipmentContentPlanDetail) {
        // 校验存在
        validateEmEquipmentContentPlanDetailExists(emEquipmentContentPlanDetail.getId());
        // 更新
        emEquipmentContentPlanDetail.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        emEquipmentContentPlanDetailMapper.updateById(emEquipmentContentPlanDetail);
    }

    @Override
    public void deleteEmEquipmentContentPlanDetail(Long id) {
        // 校验存在
        validateEmEquipmentContentPlanDetailExists(id);
        // 删除
        emEquipmentContentPlanDetailMapper.deleteById(id);
    }

    @Override
    public EmEquipmentContentPlanDetailDO getEmEquipmentContentPlanDetail(Long id) {
        return emEquipmentContentPlanDetailMapper.selectById(id);
    }

    private void validateEmEquipmentContentPlanDetailExists(Long id) {
        if (emEquipmentContentPlanDetailMapper.selectById(id) == null) {
            throw exception(EM_EQUIPMENT_CONTENT_PLAN_DETAIL_NOT_EXISTS);
        }
    }

    private void deleteEmEquipmentContentPlanDetailByContentPlanId(Long contentPlanId) {
        emEquipmentContentPlanDetailMapper.deleteByContentPlanId(contentPlanId);
    }

}