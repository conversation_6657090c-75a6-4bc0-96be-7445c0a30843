package cn.iocoder.yudao.module.ciai.controller.admin.wzweighbridgeconfig;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.wzweighbridgeconfig.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzweighbridgeconfig.WzWeighbridgeConfigDO;
import cn.iocoder.yudao.module.ciai.service.wzweighbridgeconfig.WzWeighbridgeConfigService;

@Tag(name = "管理后台 - 物资管理_地磅配置")
@RestController
@RequestMapping("/ciai/wz-weighbridge-config")
@Validated
public class WzWeighbridgeConfigController {

    @Resource
    private WzWeighbridgeConfigService wzWeighbridgeConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建物资管理_地磅配置")
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:create')")
    public CommonResult<Integer> createWzWeighbridgeConfig(@Valid @RequestBody WzWeighbridgeConfigSaveReqVO createReqVO) {
        return success(wzWeighbridgeConfigService.createWzWeighbridgeConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物资管理_地磅配置")
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:update')")
    public CommonResult<Boolean> updateWzWeighbridgeConfig(@Valid @RequestBody WzWeighbridgeConfigSaveReqVO updateReqVO) {
        wzWeighbridgeConfigService.updateWzWeighbridgeConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物资管理_地磅配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:delete')")
    public CommonResult<Boolean> deleteWzWeighbridgeConfig(@RequestParam("id") Integer id) {
        wzWeighbridgeConfigService.deleteWzWeighbridgeConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物资管理_地磅配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:query')")
    public CommonResult<WzWeighbridgeConfigRespVO> getWzWeighbridgeConfig(@RequestParam("id") Integer id) {
        WzWeighbridgeConfigDO wzWeighbridgeConfig = wzWeighbridgeConfigService.getWzWeighbridgeConfig(id);
        return success(BeanUtils.toBean(wzWeighbridgeConfig, WzWeighbridgeConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物资管理_地磅配置分页")
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:query')")
    public CommonResult<PageResult<WzWeighbridgeConfigRespVO>> getWzWeighbridgeConfigPage(@Valid WzWeighbridgeConfigPageReqVO pageReqVO) {
        PageResult<WzWeighbridgeConfigDO> pageResult = wzWeighbridgeConfigService.getWzWeighbridgeConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WzWeighbridgeConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物资管理_地磅配置 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:wz-weighbridge-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWzWeighbridgeConfigExcel(@Valid WzWeighbridgeConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WzWeighbridgeConfigDO> list = wzWeighbridgeConfigService.getWzWeighbridgeConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物资管理_地磅配置.xls", "数据", WzWeighbridgeConfigRespVO.class,
                        BeanUtils.toBean(list, WzWeighbridgeConfigRespVO.class));
    }

}