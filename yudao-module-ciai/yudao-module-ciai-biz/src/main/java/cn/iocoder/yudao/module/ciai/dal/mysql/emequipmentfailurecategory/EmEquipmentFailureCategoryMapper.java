package cn.iocoder.yudao.module.ciai.dal.mysql.emequipmentfailurecategory;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentfailurecategory.EmEquipmentFailureCategoryDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentfailurecategory.vo.*;

/**
 * 设备管理_故障类别 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EmEquipmentFailureCategoryMapper extends BaseMapperX<EmEquipmentFailureCategoryDO> {

    default PageResult<EmEquipmentFailureCategoryDO> selectPage(EmEquipmentFailureCategoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EmEquipmentFailureCategoryDO>()
                .likeIfPresent(EmEquipmentFailureCategoryDO::getFailureName, reqVO.getFailureName())
                .orderByDesc(EmEquipmentFailureCategoryDO::getId));
    }

}