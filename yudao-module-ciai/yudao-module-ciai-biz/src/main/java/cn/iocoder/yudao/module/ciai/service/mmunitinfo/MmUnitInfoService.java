package cn.iocoder.yudao.module.ciai.service.mmunitinfo;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.mmunitinfo.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmunitinfo.MmUnitInfoDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 营销管理单位信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MmUnitInfoService {

    /**
     * 创建营销管理单位信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMmUnitInfo(@Valid MmUnitInfoSaveReqVO createReqVO);

    /**
     * 更新营销管理单位信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMmUnitInfo(@Valid MmUnitInfoSaveReqVO updateReqVO);

    /**
     * 删除营销管理单位信息
     *
     * @param id 编号
     */
    void deleteMmUnitInfo(Long id);

    /**
     * 获得营销管理单位信息
     *
     * @param id 编号
     * @return 营销管理单位信息
     */
    MmUnitInfoDO getMmUnitInfo(Long id);

    /**
     * 获得营销管理单位信息分页
     *
     * @param pageReqVO 分页查询
     * @return 营销管理单位信息分页
     */
    PageResult<MmUnitInfoDO> getMmUnitInfoPage(MmUnitInfoPageReqVO pageReqVO);

}