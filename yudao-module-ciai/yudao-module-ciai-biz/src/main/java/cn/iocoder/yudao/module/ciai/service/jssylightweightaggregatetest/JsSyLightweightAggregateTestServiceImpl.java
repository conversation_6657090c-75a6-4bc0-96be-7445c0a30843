package cn.iocoder.yudao.module.ciai.service.jssylightweightaggregatetest;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.jssylightweightaggregatetest.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssylightweightaggregatetest.JsSyLightweightAggregateTestDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.jssylightweightaggregatetest.JsSyLightweightAggregateTestMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 轻集料试验 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JsSyLightweightAggregateTestServiceImpl implements JsSyLightweightAggregateTestService {

    @Resource
    private JsSyLightweightAggregateTestMapper jsSyLightweightAggregateTestMapper;

    @Override
    public Long createJsSyLightweightAggregateTest(JsSyLightweightAggregateTestSaveReqVO createReqVO) {
        // 插入
        JsSyLightweightAggregateTestDO jsSyLightweightAggregateTest = BeanUtils.toBean(createReqVO, JsSyLightweightAggregateTestDO.class);
        jsSyLightweightAggregateTestMapper.insert(jsSyLightweightAggregateTest);
        // 返回
        return jsSyLightweightAggregateTest.getId();
    }

    @Override
    public void updateJsSyLightweightAggregateTest(JsSyLightweightAggregateTestSaveReqVO updateReqVO) {
        // 校验存在
        validateJsSyLightweightAggregateTestExists(updateReqVO.getId());
        // 更新
        JsSyLightweightAggregateTestDO updateObj = BeanUtils.toBean(updateReqVO, JsSyLightweightAggregateTestDO.class);
        jsSyLightweightAggregateTestMapper.updateById(updateObj);
    }

    @Override
    public void deleteJsSyLightweightAggregateTest(Long id) {
        // 校验存在
        validateJsSyLightweightAggregateTestExists(id);
        // 删除
        jsSyLightweightAggregateTestMapper.deleteById(id);
    }

    private void validateJsSyLightweightAggregateTestExists(Long id) {
        if (jsSyLightweightAggregateTestMapper.selectById(id) == null) {
            throw exception(JS_SY_LIGHTWEIGHT_AGGREGATE_TEST_NOT_EXISTS);
        }
    }

    @Override
    public JsSyLightweightAggregateTestDO getJsSyLightweightAggregateTest(Long id) {
        return jsSyLightweightAggregateTestMapper.selectById(id);
    }

    @Override
    public PageResult<JsSyLightweightAggregateTestDO> getJsSyLightweightAggregateTestPage(JsSyLightweightAggregateTestPageReqVO pageReqVO) {
        return jsSyLightweightAggregateTestMapper.selectPage(pageReqVO);
    }

}