package cn.iocoder.yudao.module.ciai.service.jsfactorydataselection;

import java.util.*;

import cn.iocoder.yudao.module.ciai.dal.dataobject.standardmixratio.StandardMixRatioDO;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.jsfactorydataselection.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsfactorydataselection.JsFactoryDataSelectionDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 技术管理_出厂资料选用 Service 接口
 *
 * <AUTHOR>
 */
public interface JsFactoryDataSelectionService {

    /**
     * 创建技术管理_出厂资料选用
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJsFactoryDataSelection(@Valid JsFactoryDataSelectionSaveReqVO createReqVO);

    /**
     * 更新技术管理_出厂资料选用
     *
     * @param updateReqVO 更新信息
     */
    void updateJsFactoryDataSelection(@Valid JsFactoryDataSelectionSaveReqVO updateReqVO);

    /**
     * 删除技术管理_出厂资料选用
     *
     * @param id 编号
     */
    void deleteJsFactoryDataSelection(Long id);

    /**
     * 获得技术管理_出厂资料选用
     *
     * @param id 编号
     * @return 技术管理_出厂资料选用
     */
    JsFactoryDataSelectionDO getJsFactoryDataSelection(Long id);

    /**
     * 获得技术管理_出厂资料选用分页
     *
     * @param pageReqVO 分页查询
     * @return 技术管理_出厂资料选用分页
     */
    PageResult<JsFactoryDataSelectionDO> getJsFactoryDataSelectionPage(JsFactoryDataSelectionPageReqVO pageReqVO);

    PageResult<JsFactoryDataSelectionDO> getJsFactoryDataSelectionPages(JsFactoryDataSelectionPageReqVO pageReqVO);

    List<StandardMixRatioDO> getProductData(String strength);

    MoistVO getMoistDataService();
}