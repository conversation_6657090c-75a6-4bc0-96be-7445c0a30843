package cn.iocoder.yudao.module.ciai.service.jttravelexpenses;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.jttravelexpenses.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jttravelexpenses.JtTravelExpensesDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 交通费 Service 接口
 *
 * <AUTHOR>
 */
public interface JtTravelExpensesService {

    /**
     * 创建交通费
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJtTravelExpenses(@Valid JtTravelExpensesSaveReqVO createReqVO);

    /**
     * 更新交通费
     *
     * @param updateReqVO 更新信息
     */
    void updateJtTravelExpenses(@Valid JtTravelExpensesSaveReqVO updateReqVO);

    void updateLeaveStatus(Long id, Integer status);

    /**
     * 删除交通费
     *
     * @param id 编号
     */
    void deleteJtTravelExpenses(Long id);

    /**
     * 获得交通费
     *
     * @param id 编号
     * @return 交通费
     */
    JtTravelExpensesDO getJtTravelExpenses(Integer id);

    /**
     * 获得交通费分页
     *
     * @param pageReqVO 分页查询
     * @return 交通费分页
     */
    PageResult<JtTravelExpensesDO> getJtTravelExpensesPage(JtTravelExpensesPageReqVO pageReqVO);
    /**
     * 生成会计凭证
     *
     * @param id 编号
     */
    void generateAccountingVoucher(Integer id);
}