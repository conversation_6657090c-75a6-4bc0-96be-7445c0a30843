package cn.iocoder.yudao.module.ciai.controller.admin.wzpurchasecontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchasecontract.WzContractMaterialDO;

@Schema(description = "管理后台 - 物资管理_采购合同新增/修改 Request VO")
@Data
public class WzPurchaseContractSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "内部合同编号")
    private String internalContractNumber;

    @Schema(description = "合同名称")
    private String contractName;

    @Schema(description = "合同类型")
    private String contractType;

    @Schema(description = "合同状态")
    private String contractStatus;

    @Schema(description = "供货人")
    private String supplier;

    @Schema(description = "签订单位ID")
    private Long signingUnitId;

    @Schema(description = "结算依据")
    private String settlementBasis;

    @Schema(description = "结算单位ID")
    private Long settlementUnitId;

    @Schema(description = "是否自运")
    private Boolean isSelfTransport;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "运输单位ID")
    private Long transportUnitId;

    @Schema(description = "运输联系人")
    private String transportContactPerson;

    @Schema(description = "运输联系电话")
    private String transportContactPhone;

}