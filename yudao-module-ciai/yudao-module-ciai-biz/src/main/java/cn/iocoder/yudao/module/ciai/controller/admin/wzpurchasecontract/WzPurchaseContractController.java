package cn.iocoder.yudao.module.ciai.controller.admin.wzpurchasecontract;

import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchasecontract.*;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.wzpurchasecontract.vo.*;
import cn.iocoder.yudao.module.ciai.service.wzpurchasecontract.WzPurchaseContractService;

@Tag(name = "管理后台 - 物资管理_采购合同")
@RestController
@RequestMapping("/ciai/wz-purchase-contract")
@Validated
public class WzPurchaseContractController {

    @Resource
    private WzPurchaseContractService wzPurchaseContractService;

    @PostMapping("/create")
    @Operation(summary = "创建物资管理_采购合同")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:create')")
    public CommonResult<Long> createWzPurchaseContract(@Valid @RequestBody WzPurchaseContractSaveReqVO createReqVO) {
        return success(wzPurchaseContractService.createWzPurchaseContract(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物资管理_采购合同")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:update')")
    public CommonResult<Boolean> updateWzPurchaseContract(@Valid @RequestBody WzPurchaseContractSaveReqVO updateReqVO) {
        wzPurchaseContractService.updateWzPurchaseContract(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物资管理_采购合同")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:delete')")
    public CommonResult<Boolean> deleteWzPurchaseContract(@RequestParam("id") Long id) {
        wzPurchaseContractService.deleteWzPurchaseContract(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物资管理_采购合同")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<WzPurchaseContractRespVO> getWzPurchaseContract(@RequestParam("id") Long id) {
        WzPurchaseContractDO wzPurchaseContract = wzPurchaseContractService.getWzPurchaseContract(id);
        return success(BeanUtils.toBean(wzPurchaseContract, WzPurchaseContractRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得物资管理_采购合同分页")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzPurchaseContractRespVO>> getWzPurchaseContractPage(@Valid WzPurchaseContractPageReqVO pageReqVO) {
        PageResult<WzPurchaseContractDO> pageResult = wzPurchaseContractService.getWzPurchaseContractPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WzPurchaseContractRespVO.class));
    }

    @GetMapping("/join-page")
    @Operation(summary = "获得物资管理_采购合同联合查询分页")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzPurchaseContractRespJoinVO>> getWzPurchaseContractJoinPage(@Valid WzPurchaseContractPageReqVO pageReqVO) {
        PageResult<WzPurchaseContractJoinDO> pageResult = wzPurchaseContractService.getWzPurchaseContractJoinPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WzPurchaseContractRespJoinVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出物资管理_采购合同 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWzPurchaseContractExcel(@Valid WzPurchaseContractPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WzPurchaseContractJoinDO> list = wzPurchaseContractService.getWzPurchaseContractJoinPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物资管理_采购合同.xls", "数据", WzPurchaseContractRespJoinVO.class,
                        BeanUtils.toBean(list, WzPurchaseContractRespJoinVO.class));
    }

    @GetMapping("/join-material-page")
    @Operation(summary = "获得物资管理_采购合同_材料信息联合合同查询分页")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzPurchaseContractRespJoinVO>> getWzContractMaterialJoinContractPage(@Valid WzPurchaseContractPageReqVO pageReqVO) {
        PageResult<WzPurchaseContractJoinDO> pageResult = wzPurchaseContractService.getWzContractMaterialJoinContractPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WzPurchaseContractRespJoinVO.class));
    }

    @GetMapping("/join-material-list")
    @Operation(summary = "获得物资管理_采购合同_材料信息联合合同查询List")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<List<WzPurchaseContractRespJoinVO>> getWzContractMaterialJoinContractList(@Valid WzPurchaseContractPageReqVO pageReqVO) {
        List<WzPurchaseContractJoinDO> ListResult = wzPurchaseContractService.getWzContractMaterialJoinContractList(pageReqVO);
        return success(BeanUtils.toBean(ListResult, WzPurchaseContractRespJoinVO.class));
    }
    // ==================== 子表（物资管理_采购合同_材料信息） ====================

    @GetMapping("/wz-contract-material/page")
    @Operation(summary = "获得物资管理_采购合同_材料信息分页")
    @Parameter(name = "purchaseContractId", description = "采购合同ID")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzContractMaterialDO>> getWzContractMaterialPage(PageParam pageReqVO,
                                                                                        @RequestParam("purchaseContractId") Long purchaseContractId) {
        return success(wzPurchaseContractService.getWzContractMaterialPage(pageReqVO, purchaseContractId));
    }

    @GetMapping("/wz-contract-material/join-page")
    @Operation(summary = "获得物资管理_采购合同_材料信息联合查询分页")
    @Parameter(name = "purchaseContractId", description = "采购合同ID")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzContractMaterialJoinDO>> getWzContractMaterialJoinPage(@Valid WzContractMaterialPageReqJoinVO pageReqVO) {
        return success(wzPurchaseContractService.getWzContractMaterialJoinPage(pageReqVO));
    }

    @GetMapping("/wz-contract-material/get-settlement-basis")
    @Operation(summary = "获得物资管理_采购合同_材料信息结算依据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<String> getWzContractMaterialSettlementBasis(@RequestParam("id") Long id) {
        return success(wzPurchaseContractService.getWzContractMaterialSettlementBasis(id));
    }

    @GetMapping("/wz-contract-material/join-page-all")
    @Operation(summary = "获得物资管理_采购合同_材料信息联合查询分页")
    @Parameter(name = "purchaseContractId", description = "采购合同ID")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzContractMaterialJoinDO>> getWzContractMaterialJoinAllPage(@Valid WzContractMaterialPageAllReqJoinVO pageReqVO) {
        return success(wzPurchaseContractService.getWzContractMaterialJoinAllPage(pageReqVO));
    }


    @PostMapping("/wz-contract-material/create")
    @Operation(summary = "创建物资管理_采购合同_材料信息")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:create')")
    public CommonResult<Long> createWzContractMaterial(@Valid @RequestBody WzContractMaterialDO wzContractMaterial) {
        return success(wzPurchaseContractService.createWzContractMaterial(wzContractMaterial));
    }

    @PutMapping("/wz-contract-material/update")
    @Operation(summary = "更新物资管理_采购合同_材料信息")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:update')")
    public CommonResult<Boolean> updateWzContractMaterial(@Valid @RequestBody WzContractMaterialDO wzContractMaterial) {
        wzPurchaseContractService.updateWzContractMaterial(wzContractMaterial);
        return success(true);
    }

    @DeleteMapping("/wz-contract-material/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除物资管理_采购合同_材料信息")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:delete')")
    public CommonResult<Boolean> deleteWzContractMaterial(@RequestParam("id") Long id) {
        wzPurchaseContractService.deleteWzContractMaterial(id);
        return success(true);
    }

	@GetMapping("/wz-contract-material/get")
	@Operation(summary = "获得物资管理_采购合同_材料信息")
	@Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
	public CommonResult<WzContractMaterialDO> getWzContractMaterial(@RequestParam("id") Long id) {
	    return success(wzPurchaseContractService.getWzContractMaterial(id));
	}

    @GetMapping("/wz-contract-material/join-get")
    @Operation(summary = "获得物资管理_采购合同_材料信息联表查询")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<WzContractMaterialJoinDO> getWzContractMaterialJoin(@RequestParam("id") Long id) {
        return success(wzPurchaseContractService.getWzContractMaterialJoin(id));
    }


    @GetMapping("/wz-contract-material/export-excel")
    @Operation(summary = "导出物资管理_采购合同_材料信息 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWzContractMaterialExcel(@Valid WzContractMaterialPageReqJoinVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WzContractMaterialJoinDO> list = wzPurchaseContractService.getWzContractMaterialJoinPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物资管理_采购合同_材料信息.xls", "数据", WzContractMaterialExportVO.class,
                BeanUtils.toBean(list, WzContractMaterialExportVO.class));
    }

    // ==================== 子表（物资管理_采购合同_材料信息_定价） ====================

    @GetMapping("/wz-contract-material/wz-material-pricing/page")
    @Operation(summary = "获得物资管理_采购合同_材料信息_定价分页")
    @Parameter(name = "materialInfoId", description = "材料信息ID")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<PageResult<WzMaterialPricingDO>> getWzMaterialPricingPage(@Valid WzMaterialPricingReqVO pageReqVO) {
        return success(wzPurchaseContractService.getWzMaterialPricingPage(pageReqVO));
    }

    @PostMapping("/wz-contract-material/wz-material-pricing/create")
    @Operation(summary = "创建物资管理_采购合同_材料信息_定价")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:create')")
    public CommonResult<Long> createWzMaterialPricing(@Valid @RequestBody WzMaterialPricingDO wzMaterialPricing) {
        return success(wzPurchaseContractService.createWzMaterialPricing(wzMaterialPricing));
    }

    @PutMapping("/wz-contract-material/wz-material-pricing/update")
    @Operation(summary = "更新物资管理_采购合同_材料信息_定价")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:update')")
    public CommonResult<Boolean> updateWzMaterialPricing(@Valid @RequestBody WzMaterialPricingDO wzMaterialPricing) {
        wzPurchaseContractService.updateWzMaterialPricing(wzMaterialPricing);
        return success(true);
    }

    @DeleteMapping("/wz-contract-material/wz-material-pricing/delete")
    @Parameter(name = "id", description = "编号", required = true)
    @Operation(summary = "删除物资管理_采购合同_材料信息_定价")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:delete')")
    public CommonResult<Boolean> deleteWzMaterialPricing(@RequestParam("id") Long id) {
        wzPurchaseContractService.deleteWzMaterialPricing(id);
        return success(true);
    }

    @GetMapping("/wz-contract-material/wz-material-pricing/get")
    @Operation(summary = "获得物资管理_采购合同_材料信息_定价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:query')")
    public CommonResult<WzMaterialPricingDO> getWzMaterialPricing(@RequestParam("id") Long id) {
        return success(wzPurchaseContractService.getWzMaterialPricing(id));
    }

    @GetMapping("/wz-contract-material/wz-material-pricing/export-excel")
    @Operation(summary = "导出物资管理_采购合同_材料信息_材料定价 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:wz-purchase-contract:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWzMaterialPricingExcel(@Valid WzMaterialPricingReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WzMaterialPricingDO> list = wzPurchaseContractService.getWzMaterialPricingPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "物资管理_采购合同_材料信息_材料定价.xls", "数据", WzMaterialPricingDO.class,
                BeanUtils.toBean(list, WzMaterialPricingDO.class));
    }

}