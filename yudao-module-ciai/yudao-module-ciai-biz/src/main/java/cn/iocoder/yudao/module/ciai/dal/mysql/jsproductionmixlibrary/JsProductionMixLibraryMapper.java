package cn.iocoder.yudao.module.ciai.dal.mysql.jsproductionmixlibrary;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsproductionmixlibrary.JsProductionMixLibraryDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsproductionmixlibrary.JsUnitDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.jsproductionmixlibrary.vo.*;

/**
 * 技术管理_生产配合比库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JsProductionMixLibraryMapper extends BaseMapperX<JsProductionMixLibraryDO> {

    default PageResult<JsUnitDO> selectPage(JsProductionMixLibraryPageReqVO reqVO) {
        return selectJoinPage(reqVO, JsUnitDO.class ,new MPJLambdaWrapperX<JsProductionMixLibraryDO>()
                .selectAll(JsProductionMixLibraryDO.class)
                .selectAs("unit.unit_name", JsUnitDO::getUnitName)
                .likeIfPresent(JsProductionMixLibraryDO::getMixMarkNo, reqVO.getMixMarkNo())
                .likeIfPresent(JsProductionMixLibraryDO::getTrialNo, reqVO.getTrialNo())
                .eqIfPresent(JsProductionMixLibraryDO::getStrengthGrade, reqVO.getStrengthGrade())
                .eqIfPresent(JsProductionMixLibraryDO::getProductionHost, reqVO.getProductionHost())
                .leftJoin("ciai_em_production_unit AS unit ON unit.id = t.production_host")
                .orderByDesc(JsProductionMixLibraryDO::getId));
    }


}