package cn.iocoder.yudao.module.ciai.dal.dataobject.emaccidentlevel;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 设备管理_事故级别 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_em_accident_level")
@KeySequence("ciai_em_accident_level_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmAccidentLevelDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 事故级别名称
     */
    private String levelName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 启用状态 (1:启用 0:停用)
     */
    private Boolean enabled;

}