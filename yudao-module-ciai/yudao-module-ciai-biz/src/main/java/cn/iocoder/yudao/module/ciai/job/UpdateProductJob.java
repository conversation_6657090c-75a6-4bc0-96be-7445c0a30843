package cn.iocoder.yudao.module.ciai.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.ciai.gkjinterface.service.UpdateProductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UpdateProductJob implements JobHandler {

    @Resource
    private UpdateProductService updateProductService;

    @Override
    public String execute(String param) throws Exception {
        updateProductService.updateProduct(param);
        return "success";
    }
}
