package cn.iocoder.yudao.module.ciai.controller.admin.mmproductprofile;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.mmproductprofile.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmproductprofile.MmProductProfileDO;
import cn.iocoder.yudao.module.ciai.service.mmproductprofile.MmProductProfileService;

@Tag(name = "管理后台 - 营销管理产品档案")
@RestController
@RequestMapping("/ciai/mm-product-profile")
@Validated
public class MmProductProfileController {

    @Resource
    private MmProductProfileService mmProductProfileService;

    @PostMapping("/create")
    @Operation(summary = "创建营销管理产品档案")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:create')")
    public CommonResult<Long> createMmProductProfile(@Valid @RequestBody MmProductProfileSaveReqVO createReqVO) {
        return success(mmProductProfileService.createMmProductProfile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新营销管理产品档案")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:update')")
    public CommonResult<Boolean> updateMmProductProfile(@Valid @RequestBody MmProductProfileSaveReqVO updateReqVO) {
        mmProductProfileService.updateMmProductProfile(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除营销管理产品档案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:delete')")
    public CommonResult<Boolean> deleteMmProductProfile(@RequestParam("id") Long id) {
        mmProductProfileService.deleteMmProductProfile(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得营销管理产品档案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:query')")
    public CommonResult<MmProductProfileRespVO> getMmProductProfile(@RequestParam("id") Long id) {
        MmProductProfileDO mmProductProfile = mmProductProfileService.getMmProductProfile(id);
        return success(BeanUtils.toBean(mmProductProfile, MmProductProfileRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得营销管理产品档案分页")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:query')")
    public CommonResult<PageResult<MmProductProfileRespVO>> getMmProductProfilePage(@Valid MmProductProfilePageReqVO pageReqVO) {
        PageResult<MmProductProfileDO> pageResult = mmProductProfileService.getMmProductProfilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MmProductProfileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出营销管理产品档案 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:mm-product-profile:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMmProductProfileExcel(@Valid MmProductProfilePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MmProductProfileDO> list = mmProductProfileService.getMmProductProfilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "营销管理产品档案.xls", "数据", MmProductProfileRespVO.class,
                        BeanUtils.toBean(list, MmProductProfileRespVO.class));
    }

}