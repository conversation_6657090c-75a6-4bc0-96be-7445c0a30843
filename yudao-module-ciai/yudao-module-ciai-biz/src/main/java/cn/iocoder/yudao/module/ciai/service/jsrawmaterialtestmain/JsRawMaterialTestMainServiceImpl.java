package cn.iocoder.yudao.module.ciai.service.jsrawmaterialtestmain;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo.JsRawMaterialTestDetailDO;
import cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo.JsRawMaterialTestItemDO;
import cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo.JsRawMaterialTestMainPageReqVO;
import cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo.JsRawMaterialTestMainSaveReqVO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsrawmaterialtestmain.JsRawMaterialTestDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsrawmaterialtestmain.JsRawMaterialTestMainDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.jsrawmaterialtestmain.JsRawMaterialTestMainMapper;
import cn.iocoder.yudao.module.ciai.dal.mysql.jsrawmaterialtestmain.JsRawMaterialTestMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.JS_RAW_MATERIAL_TEST_MAIN_NOT_EXISTS;

/**
 * 原材试验 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class JsRawMaterialTestMainServiceImpl implements JsRawMaterialTestMainService {

    @Resource
    private JsRawMaterialTestMainMapper jsRawMaterialTestMainMapper;
    @Resource
    private JsRawMaterialTestMapper jsRawMaterialTestMapper;

    @Override
    public Long createJsRawMaterialTestMain(JsRawMaterialTestMainSaveReqVO createReqVO) {
        // 插入
        JsRawMaterialTestMainDO jsRawMaterialTestMain = BeanUtils.toBean(createReqVO, JsRawMaterialTestMainDO.class);
        jsRawMaterialTestMainMapper.insert(jsRawMaterialTestMain);
        // 返回
        return jsRawMaterialTestMain.getId();
    }

    @Override
    public void updateJsRawMaterialTestMain(JsRawMaterialTestMainSaveReqVO updateReqVO) {
        // 校验存在
        validateJsRawMaterialTestMainExists(updateReqVO.getId());
        jsRawMaterialTestMainMapper.setCommissionStatus(updateReqVO.getId());
        // 更新
        JsRawMaterialTestMainDO updateObj = BeanUtils.toBean(updateReqVO, JsRawMaterialTestMainDO.class);
        jsRawMaterialTestMainMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJsRawMaterialTestMain(Long id) {
        // 校验存在
        validateJsRawMaterialTestMainExists(id);
        // 删除
        jsRawMaterialTestMainMapper.deleteById(id);

        // 删除子表
        deleteJsRawMaterialTestByCommissionNo(id);
    }

    private void validateJsRawMaterialTestMainExists(Long id) {
        if (jsRawMaterialTestMainMapper.selectById(id) == null) {
            throw exception(JS_RAW_MATERIAL_TEST_MAIN_NOT_EXISTS);
        }
    }

    @Override
    public JsRawMaterialTestMainDO getJsRawMaterialTestMain(Long id) {
        return jsRawMaterialTestMainMapper.selectById(id);
    }

    @Override
    public PageResult<JsRawMaterialTestMainDO> getJsRawMaterialTestMainPage(JsRawMaterialTestMainPageReqVO pageReqVO) {
        return jsRawMaterialTestMainMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（技术管理_原材试验） ====================

    @Override
    public PageResult<JsRawMaterialTestDO> getJsRawMaterialTestPage(PageParam pageReqVO, String commissionNo) {
        return jsRawMaterialTestMapper.selectPage(pageReqVO, commissionNo);
    }

    @Override
    @Transactional // 添加事务注解确保数据一致性
    public Long createJsRawMaterialTest(JsRawMaterialTestDO jsRawMaterialTest) {

        // 插入主表数据
        jsRawMaterialTestMapper.insert(jsRawMaterialTest);
        // 获取插入后的ID
        Long testId = jsRawMaterialTest.getId();
        
        // 获取试验项目列表
        List<JsRawMaterialTestDetailDO> testItems = jsRawMaterialTest.getTestItems();
        if (testItems != null && !testItems.isEmpty()) {
            // 设置每个明细的关联ID和创建时间
            LocalDateTime now = LocalDateTime.now();
            for (JsRawMaterialTestDetailDO detail : testItems) {
                detail.setRawMaterialTestId(testId);
                detail.setCreateTime(now);
                detail.setCreator(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
            }
            // 批量插入明细
            jsRawMaterialTestMainMapper.insertTestDetails(testItems, testId.intValue());
        }
        
        return testId;
    }

    @Override
    public void updateJsRawMaterialTest(JsRawMaterialTestDO jsRawMaterialTest) {
        // 校验存在
        validateJsRawMaterialTestExists(jsRawMaterialTest.getId());
        // 更新
        jsRawMaterialTest.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        jsRawMaterialTestMapper.updateById(jsRawMaterialTest);
    }

    @Override
    public void deleteJsRawMaterialTest(Long id) {
        // 校验存在
        validateJsRawMaterialTestExists(id);
        // 删除
        jsRawMaterialTestMapper.deleteById(id);
    }

    @Override
    public JsRawMaterialTestDO getJsRawMaterialTest(Long id) {
        return jsRawMaterialTestMapper.selectById(id);
    }

    @Override
    public List<JsRawMaterialTestItemDO> getJsRawMaterialTestItem(Long rawMaterialTypeId) {
        return jsRawMaterialTestMainMapper.selectItemList(rawMaterialTypeId);
    }

    @Override
    public List<JsRawMaterialTestDetailDO> getJsRawMaterialTestDetail(Long rawMaterialTestId) {
        return jsRawMaterialTestMainMapper.selectDetailList(rawMaterialTestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateJsRawMaterialTestDetail(JsRawMaterialTestDetailDO jsRawMaterialTestDetail) {
        // 带id的是修改  修改人 修改时间  ，不带id的是新增 创建人 创建时间  SecurityFrameworkUtils.getLoginUserId()
        if (jsRawMaterialTestDetail.getId() != null ) {
            jsRawMaterialTestDetail.setUpdater(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
            jsRawMaterialTestDetail.setUpdateTime(LocalDateTime.now());
            jsRawMaterialTestMainMapper.updateDetail(jsRawMaterialTestDetail);
        } else {
            jsRawMaterialTestDetail.setCreator(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
            jsRawMaterialTestDetail.setCreateTime(LocalDateTime.now());
            jsRawMaterialTestMainMapper.insertDetail(jsRawMaterialTestDetail);
        }
    }

    @Override
    public void deleteJsRawMaterialTestDetail(Long id) {
        jsRawMaterialTestMainMapper.deleteDetail(id);
    }

    private void validateJsRawMaterialTestExists(Long id) {
        if (jsRawMaterialTestMapper.selectById(id) == null) {
            throw exception(JS_RAW_MATERIAL_TEST_MAIN_NOT_EXISTS);
        }
    }

    private void deleteJsRawMaterialTestByCommissionNo(Long commissionNo) {
        jsRawMaterialTestMapper.deleteByCommissionNo(String.valueOf(commissionNo));
    }

}