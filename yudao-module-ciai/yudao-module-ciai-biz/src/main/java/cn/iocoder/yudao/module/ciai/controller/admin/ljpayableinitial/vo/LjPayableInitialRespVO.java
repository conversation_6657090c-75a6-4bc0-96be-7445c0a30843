package cn.iocoder.yudao.module.ciai.controller.admin.ljpayableinitial.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 应付期初账款 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LjPayableInitialRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "供应商ID")
    @ExcelProperty("供应商ID")
    private Long supplierId;

    @Schema(description = "发生数量")
    @ExcelProperty("发生数量")
    private BigDecimal occurrenceQuantity;

    @Schema(description = "发生金额")
    @ExcelProperty("发生金额")
    private BigDecimal occurrenceAmount;

    @Schema(description = "付款数量")
    @ExcelProperty("付款数量")
    private BigDecimal paymentQuantity;

    @Schema(description = "付款金额")
    @ExcelProperty("付款金额")
    private BigDecimal paymentAmount;

    @Schema(description = "结算数量")
    @ExcelProperty("结算数量")
    private BigDecimal settlementQuantity;

    @Schema(description = "结算金额")
    @ExcelProperty("结算金额")
    private BigDecimal settlementAmount;

    @Schema(description = "欠款金额")
    @ExcelProperty("欠款金额")
    private BigDecimal arrearsAmount;

    @Schema(description = "期初日期")
    @ExcelProperty("期初日期")
    private LocalDateTime initialDate;

}