package cn.iocoder.yudao.module.ciai.controller.admin.jssylightweightaggregatetest.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 轻集料试验 Response VO")
@Data
@ExcelIgnoreUnannotated
public class JsSyLightweightAggregateTestRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22716")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "单号")
    @ExcelProperty("单号")
    private String orderNo;

    @Schema(description = "试验编号")
    @ExcelProperty("试验编号")
    private String testNo;

    @Schema(description = "委托编号")
    @ExcelProperty("委托编号")
    private String entrustNo;

    @Schema(description = "委托单位")
    @ExcelProperty("委托单位")
    private String entrustUnit;

    @Schema(description = "工程名称", example = "张三")
    @ExcelProperty("工程名称")
    private String projectName;

    @Schema(description = "种类编码")
    @ExcelProperty("种类编码")
    private String typeCode;

    @Schema(description = "种类", example = "2")
    @ExcelProperty("种类")
    private String type;

    @Schema(description = "规格")
    @ExcelProperty("规格")
    private String specification;

    @Schema(description = "产地")
    @ExcelProperty("产地")
    private String origin;

    @Schema(description = "代表数量")
    @ExcelProperty("代表数量")
    private String representQuantity;

    @Schema(description = "收样日期")
    @ExcelProperty("收样日期")
    private LocalDateTime sampleDate;

    @Schema(description = "试验日期")
    @ExcelProperty("试验日期")
    private LocalDateTime testDate;

    @Schema(description = "试样编号")
    @ExcelProperty("试样编号")
    private String sampleNo;

    @Schema(description = "截止日期")
    @ExcelProperty("截止日期")
    private LocalDateTime deadlineDate;

    @Schema(description = "结论")
    @ExcelProperty("结论")
    private String conclusion;

    @Schema(description = "试验委托人")
    @ExcelProperty("试验委托人")
    private String testClient;

    @Schema(description = "负责人")
    @ExcelProperty("负责人")
    private String responsiblePerson;

    @Schema(description = "委托人")
    @ExcelProperty("委托人")
    private String client;

    @Schema(description = "审核人")
    @ExcelProperty("审核人")
    private String reviewer;

    @Schema(description = "试验人")
    @ExcelProperty("试验人")
    private String tester;

    @Schema(description = "碱含量")
    @ExcelProperty("碱含量")
    private BigDecimal alkaliContent;

    @Schema(description = "氯离子")
    @ExcelProperty("氯离子")
    private BigDecimal chlorideIon;

    @Schema(description = "其它")
    @ExcelProperty("其它")
    private String other;

    @Schema(description = "报告日期")
    @ExcelProperty("报告日期")
    private LocalDateTime reportDate;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String modifier;

    @Schema(description = "时间")
    @ExcelProperty("时间")
    private LocalDateTime modifyTime;

    @Schema(description = "检测标准")
    @ExcelProperty("检测标准")
    private String testStandard;

    @Schema(description = "试验状态", example = "1")
    @ExcelProperty("试验状态")
    private String testStatus;

    @Schema(description = "使用状态", example = "2")
    @ExcelProperty("使用状态")
    private String usageStatus;

    @Schema(description = "Y")
    @ExcelProperty("Y")
    private Integer yValue;

    @Schema(description = "SHAI11")
    @ExcelProperty("SHAI11")
    private Integer shai11;

    @Schema(description = "SHAI12")
    @ExcelProperty("SHAI12")
    private Integer shai12;

    @Schema(description = "SHAI13")
    @ExcelProperty("SHAI13")
    private Integer shai13;

    @Schema(description = "SHAI14")
    @ExcelProperty("SHAI14")
    private Integer shai14;

    @Schema(description = "SHAI15")
    @ExcelProperty("SHAI15")
    private Integer shai15;

    @Schema(description = "SHAI16")
    @ExcelProperty("SHAI16")
    private Integer shai16;

    @Schema(description = "SHAI17")
    @ExcelProperty("SHAI17")
    private Integer shai17;

    @Schema(description = "SHAI18")
    @ExcelProperty("SHAI18")
    private Integer shai18;

    @Schema(description = "SHAI21")
    @ExcelProperty("SHAI21")
    private Double shai21;

    @Schema(description = "SHAI22")
    @ExcelProperty("SHAI22")
    private Double shai22;

    @Schema(description = "SHAI23")
    @ExcelProperty("SHAI23")
    private Double shai23;

    @Schema(description = "SHAI24")
    @ExcelProperty("SHAI24")
    private Double shai24;

    @Schema(description = "SHAI25")
    @ExcelProperty("SHAI25")
    private Double shai25;

    @Schema(description = "SHAI26")
    @ExcelProperty("SHAI26")
    private Double shai26;

    @Schema(description = "SHAI27")
    @ExcelProperty("SHAI27")
    private Double shai27;

    @Schema(description = "SHAI28")
    @ExcelProperty("SHAI28")
    private Double shai28;

    @Schema(description = "SHAI31")
    @ExcelProperty("SHAI31")
    private Double shai31;

    @Schema(description = "SHAI32")
    @ExcelProperty("SHAI32")
    private Double shai32;

    @Schema(description = "SHAI33")
    @ExcelProperty("SHAI33")
    private Double shai33;

    @Schema(description = "SHAI34")
    @ExcelProperty("SHAI34")
    private Double shai34;

    @Schema(description = "SHAI35")
    @ExcelProperty("SHAI35")
    private Double shai35;

    @Schema(description = "SHAI36")
    @ExcelProperty("SHAI36")
    private Double shai36;

    @Schema(description = "SHAI37")
    @ExcelProperty("SHAI37")
    private Double shai37;

    @Schema(description = "SHAI38")
    @ExcelProperty("SHAI38")
    private Double shai38;

    @Schema(description = "SHAI41")
    @ExcelProperty("SHAI41")
    private Integer shai41;

    @Schema(description = "SHAI42")
    @ExcelProperty("SHAI42")
    private Integer shai42;

    @Schema(description = "SHAI43")
    @ExcelProperty("SHAI43")
    private Integer shai43;

    @Schema(description = "SHAI44")
    @ExcelProperty("SHAI44")
    private Integer shai44;

    @Schema(description = "SHAI45")
    @ExcelProperty("SHAI45")
    private Integer shai45;

    @Schema(description = "SHAI46")
    @ExcelProperty("SHAI46")
    private Integer shai46;

    @Schema(description = "SHAI47")
    @ExcelProperty("SHAI47")
    private Integer shai47;

    @Schema(description = "SHAI48")
    @ExcelProperty("SHAI48")
    private Integer shai48;

    @Schema(description = "SHAI51")
    @ExcelProperty("SHAI51")
    private Double shai51;

    @Schema(description = "SHAI52")
    @ExcelProperty("SHAI52")
    private Double shai52;

    @Schema(description = "SHAI53")
    @ExcelProperty("SHAI53")
    private Double shai53;

    @Schema(description = "SHAI54")
    @ExcelProperty("SHAI54")
    private Double shai54;

    @Schema(description = "SHAI55")
    @ExcelProperty("SHAI55")
    private Double shai55;

    @Schema(description = "SHAI56")
    @ExcelProperty("SHAI56")
    private Double shai56;

    @Schema(description = "SHAI57")
    @ExcelProperty("SHAI57")
    private Double shai57;

    @Schema(description = "SHAI58")
    @ExcelProperty("SHAI58")
    private Double shai58;

    @Schema(description = "SHAI61")
    @ExcelProperty("SHAI61")
    private Double shai61;

    @Schema(description = "SHAI62")
    @ExcelProperty("SHAI62")
    private Double shai62;

    @Schema(description = "SHAI63")
    @ExcelProperty("SHAI63")
    private Double shai63;

    @Schema(description = "SHAI64")
    @ExcelProperty("SHAI64")
    private Double shai64;

    @Schema(description = "SHAI65")
    @ExcelProperty("SHAI65")
    private Double shai65;

    @Schema(description = "SHAI66")
    @ExcelProperty("SHAI66")
    private Double shai66;

    @Schema(description = "SHAI67")
    @ExcelProperty("SHAI67")
    private Double shai67;

    @Schema(description = "SHAI68")
    @ExcelProperty("SHAI68")
    private Double shai68;

    @Schema(description = "SHAIV1")
    @ExcelProperty("SHAIV1")
    private Double shaiV1;

    @Schema(description = "SHAIV2")
    @ExcelProperty("SHAIV2")
    private Double shaiV2;

    @Schema(description = "SHAIV3")
    @ExcelProperty("SHAIV3")
    private Double shaiV3;

    @Schema(description = "SHAIV4")
    @ExcelProperty("SHAIV4")
    private Double shaiV4;

    @Schema(description = "SHAIV5")
    @ExcelProperty("SHAIV5")
    private Double shaiV5;

    @Schema(description = "SHAIV6")
    @ExcelProperty("SHAIV6")
    private Double shaiV6;

    @Schema(description = "SHAIV7")
    @ExcelProperty("SHAIV7")
    private Double shaiV7;

    @Schema(description = "SHAIV8")
    @ExcelProperty("SHAIV8")
    private Double shaiV8;

    @Schema(description = "XDMS1")
    @ExcelProperty("XDMS1")
    private Double xdms1;

    @Schema(description = "XDMS2")
    @ExcelProperty("XDMS2")
    private Double xdms2;

    @Schema(description = "细度模数")
    @ExcelProperty("细度模数")
    private Double finenessModulus;

    @Schema(description = "级配情况")
    @ExcelProperty("级配情况")
    private String gradationCondition;

    @Schema(description = "最大粒径")
    @ExcelProperty("最大粒径")
    private String maxParticleSize;

    @Schema(description = "DJ11")
    @ExcelProperty("DJ11")
    private Double dj11;

    @Schema(description = "DJ12")
    @ExcelProperty("DJ12")
    private Double dj12;

    @Schema(description = "DJ13")
    @ExcelProperty("DJ13")
    private Double dj13;

    @Schema(description = "DJ21")
    @ExcelProperty("DJ21")
    private Double dj21;

    @Schema(description = "DJ22")
    @ExcelProperty("DJ22")
    private Double dj22;

    @Schema(description = "DJ23")
    @ExcelProperty("DJ23")
    private Double dj23;

    @Schema(description = "DJMD1")
    @ExcelProperty("DJMD1")
    private Double djMd1;

    @Schema(description = "DJMD2")
    @ExcelProperty("DJMD2")
    private Double djMd2;

    @Schema(description = "堆积密度")
    @ExcelProperty("堆积密度")
    private Double bulkDensity;

    @Schema(description = "XS11")
    @ExcelProperty("XS11")
    private Double xs11;

    @Schema(description = "XS12")
    @ExcelProperty("XS12")
    private Double xs12;

    @Schema(description = "XS13")
    @ExcelProperty("XS13")
    private Double xs13;

    @Schema(description = "XS21")
    @ExcelProperty("XS21")
    private Double xs21;

    @Schema(description = "XS22")
    @ExcelProperty("XS22")
    private Double xs22;

    @Schema(description = "XS23")
    @ExcelProperty("XS23")
    private Double xs23;

    @Schema(description = "XS31")
    @ExcelProperty("XS31")
    private Double xs31;

    @Schema(description = "XS32")
    @ExcelProperty("XS32")
    private Double xs32;

    @Schema(description = "XS33")
    @ExcelProperty("XS33")
    private Double xs33;

    @Schema(description = "吸水率")
    @ExcelProperty("吸水率")
    private Double waterAbsorption;

    @Schema(description = "BG11")
    @ExcelProperty("BG11")
    private Double bg11;

    @Schema(description = "BG12")
    @ExcelProperty("BG12")
    private Double bg12;

    @Schema(description = "BG13")
    @ExcelProperty("BG13")
    private Double bg13;

    @Schema(description = "BG14")
    @ExcelProperty("BG14")
    private Double bg14;

    @Schema(description = "BGMD1")
    @ExcelProperty("BGMD1")
    private Double bgMd1;

    @Schema(description = "BG21")
    @ExcelProperty("BG21")
    private Double bg21;

    @Schema(description = "BG22")
    @ExcelProperty("BG22")
    private Double bg22;

    @Schema(description = "BG23")
    @ExcelProperty("BG23")
    private Double bg23;

    @Schema(description = "BG24")
    @ExcelProperty("BG24")
    private Double bg24;

    @Schema(description = "BGMD2")
    @ExcelProperty("BGMD2")
    private Double bgMd2;

    @Schema(description = "表观密度")
    @ExcelProperty("表观密度")
    private Double apparentDensity;

    @Schema(description = "TY11")
    @ExcelProperty("TY11")
    private Double ty11;

    @Schema(description = "TY12")
    @ExcelProperty("TY12")
    private Double ty12;

    @Schema(description = "TY1")
    @ExcelProperty("TY1")
    private Double ty1;

    @Schema(description = "TY21")
    @ExcelProperty("TY21")
    private Double ty21;

    @Schema(description = "TY22")
    @ExcelProperty("TY22")
    private Double ty22;

    @Schema(description = "TY2")
    @ExcelProperty("TY2")
    private Double ty2;

    @Schema(description = "TY31")
    @ExcelProperty("TY31")
    private Double ty31;

    @Schema(description = "TY32")
    @ExcelProperty("TY32")
    private Double ty32;

    @Schema(description = "TY3")
    @ExcelProperty("TY3")
    private Double ty3;

    @Schema(description = "筒压强度")
    @ExcelProperty("筒压强度")
    private Double cylinderStrength;

    @Schema(description = "HN11")
    @ExcelProperty("HN11")
    private Double hn11;

    @Schema(description = "HN12")
    @ExcelProperty("HN12")
    private Double hn12;

    @Schema(description = "HN1")
    @ExcelProperty("HN1")
    private Double hn1;

    @Schema(description = "HN21")
    @ExcelProperty("HN21")
    private Double hn21;

    @Schema(description = "HN22")
    @ExcelProperty("HN22")
    private Double hn22;

    @Schema(description = "HN2")
    @ExcelProperty("HN2")
    private Double hn2;

    @Schema(description = "含泥量")
    @ExcelProperty("含泥量")
    private Double mudContent;

    @Schema(description = "执行标准")
    @ExcelProperty("执行标准")
    private String standard;

    @Schema(description = "QZL1")
    @ExcelProperty("QZL1")
    private Double qzl1;

    @Schema(description = "QZL2")
    @ExcelProperty("QZL2")
    private Double qzl2;

    @Schema(description = "QZL3")
    @ExcelProperty("QZL3")
    private Double qzl3;

    @Schema(description = "试验人2")
    @ExcelProperty("试验人2")
    private String tester2;

    @Schema(description = "执行标准2")
    @ExcelProperty("执行标准2")
    private String standard2;

    @Schema(description = "站别")
    @ExcelProperty("站别")
    private String stationCode;

    @Schema(description = "工程类型", example = "1")
    @ExcelProperty("工程类型")
    private String projectType;

    @Schema(description = "厂家牌号")
    @ExcelProperty("厂家牌号")
    private String factoryBrand;

    @Schema(description = "出厂编号")
    @ExcelProperty("出厂编号")
    private String factoryNo;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}