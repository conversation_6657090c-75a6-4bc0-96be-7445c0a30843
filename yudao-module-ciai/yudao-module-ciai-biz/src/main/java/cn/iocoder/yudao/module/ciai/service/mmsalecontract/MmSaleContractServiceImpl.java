package cn.iocoder.yudao.module.ciai.service.mmsalecontract;

import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontract.MmSaleContractDetailDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproject.MmSaleContractProjectDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmunitinfo.MmUnitInfoDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.mmsalecontractproject.MmSaleContractProjectMapper;
import cn.iocoder.yudao.module.ciai.dal.mysql.mmunitinfo.MmUnitInfoMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontract.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontract.MmSaleContractDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.mmsalecontract.MmSaleContractMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 营销管理销售合同 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MmSaleContractServiceImpl implements MmSaleContractService {

    @Resource
    private MmSaleContractMapper mmSaleContractMapper;

    @Resource
    private MmUnitInfoMapper unitMapper;

    @Resource
    private MmSaleContractProjectMapper mmSaleContractProjectMapper;

    @Override
    public Long createMmSaleContract(MmSaleContractSaveReqVO createReqVO) {
        // 插入
        MmSaleContractDO mmSaleContract = BeanUtils.toBean(createReqVO, MmSaleContractDO.class);
        mmSaleContractMapper.insert(mmSaleContract);
        // 返回
        return mmSaleContract.getId();
    }

    @Override
    public void updateMmSaleContract(MmSaleContractSaveReqVO updateReqVO) {
        // 校验存在
        validateMmSaleContractExists(updateReqVO.getId());
        // 更新
        MmSaleContractDO updateObj = BeanUtils.toBean(updateReqVO, MmSaleContractDO.class);
        mmSaleContractMapper.updateById(updateObj);
    }

    @Override
    public void deleteMmSaleContract(Long id) {
        // 校验存在
        validateMmSaleContractExists(id);
        // 删除
        mmSaleContractMapper.deleteById(id);
    }

    private void validateMmSaleContractExists(Long id) {
        if (mmSaleContractMapper.selectById(id) == null) {
            throw exception(MM_SALE_CONTRACT_NOT_EXISTS);
        }
    }

    @Override
    public MmSaleContractDO getMmSaleContract(Long id) {
        return mmSaleContractMapper.selectById(id);
    }

    @Override
    public PageResult<MmSaleContractDO> getMmSaleContractPage(MmSaleContractPageReqVO pageReqVO) {
        return mmSaleContractMapper.selectPage(pageReqVO);
    }


    @Override
    public PageResult<MmSaleContractDetailRespVO> selectSaleContractAndProjectPage(MmSaleContractPageReqVO pageReqVO) {
        // 使用MyBatis-Plus的自动分页功能
        Page<MmSaleContractDetailRespVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        // 调用Mapper自定义分页方法
        IPage<MmSaleContractDetailRespVO> pageResult = mmSaleContractMapper.selectSaleContractAndProjectPage(page, pageReqVO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public IPage<MmSaleContractProjectDO> getProjects(Long id, int pageNum, int pageSize) {
        Page<MmSaleContractProjectDO> page = new Page<>(pageNum, pageSize);
        return mmSaleContractMapper.getProjects(page,id);
    }


    @Override
    public List<MmUnitInfoDO> getUnitList() {
        List<MmUnitInfoDO> units = unitMapper.selectList(new LambdaQueryWrapper<MmUnitInfoDO>()
                .select(MmUnitInfoDO::getId, MmUnitInfoDO::getCompanyName)
                .eq(MmUnitInfoDO::getDeleted, 0)
                .orderByAsc(MmUnitInfoDO::getCompanyName));

        // Add empty option
        List<MmUnitInfoDO> result = new ArrayList<>();
        result.add(new MmUnitInfoDO(0L, ""));
        result.addAll(units.stream()
                .map(u -> new MmUnitInfoDO(u.getId(), u.getCompanyName()))
                .collect(Collectors.toList()));

        return result;
    }

    @Override
    public MmSaleContractDO getContractByProjectId(Long id) {
        // 先根据工程id获取合同id
        MmSaleContractProjectDO project = mmSaleContractProjectMapper.selectById(id);
        if (project == null) {
            return null;
        }
        // 根据合同id查询合同信息
        return mmSaleContractMapper.selectById(project.getSalesContractId());
    }

}