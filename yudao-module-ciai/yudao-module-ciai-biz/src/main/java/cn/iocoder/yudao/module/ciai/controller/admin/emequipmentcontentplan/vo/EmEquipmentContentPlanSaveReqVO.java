package cn.iocoder.yudao.module.ciai.controller.admin.emequipmentcontentplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentcontentplan.EmEquipmentContentPlanDetailDO;

@Schema(description = "管理后台 - 设备管理_内容计划新增/修改 Request VO")
@Data
public class EmEquipmentContentPlanSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13753")
    private Long id;

    @Schema(description = "计划名称", example = "赵六")
    private String name;

    @Schema(description = "计划描述", example = "你猜")
    private String remark;

}