package cn.iocoder.yudao.module.ciai.controller.admin.imwhinfo;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.imwhinfo.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.imwhinfo.ImWhInfoDO;
import cn.iocoder.yudao.module.ciai.service.imwhinfo.ImWhInfoService;

@Tag(name = "管理后台 - 库房信息")
@RestController
@RequestMapping("/ciai/im-wh-info")
@Validated
public class ImWhInfoController {

    @Resource
    private ImWhInfoService imWhInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建库房信息")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:create')")
    public CommonResult<Long> createImWhInfo(@Valid @RequestBody ImWhInfoSaveReqVO createReqVO) {
        return success(imWhInfoService.createImWhInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新库房信息")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:update')")
    public CommonResult<Boolean> updateImWhInfo(@Valid @RequestBody ImWhInfoSaveReqVO updateReqVO) {
        imWhInfoService.updateImWhInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库房信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:delete')")
    public CommonResult<Boolean> deleteImWhInfo(@RequestParam("id") Long id) {
        imWhInfoService.deleteImWhInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库房信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:query')")
    public CommonResult<ImWhInfoRespVO> getImWhInfo(@RequestParam("id") Long id) {
        ImWhInfoDO imWhInfo = imWhInfoService.getImWhInfo(id);
        return success(BeanUtils.toBean(imWhInfo, ImWhInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库房信息分页")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:query')")
    public CommonResult<PageResult<ImWhInfoRespVO>> getImWhInfoPage(@Valid ImWhInfoPageReqVO pageReqVO) {
        PageResult<ImWhInfoDO> pageResult = imWhInfoService.getImWhInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImWhInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库房信息 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImWhInfoExcel(@Valid ImWhInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImWhInfoDO> list = imWhInfoService.getImWhInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库房信息.xls", "数据", ImWhInfoRespVO.class,
                        BeanUtils.toBean(list, ImWhInfoRespVO.class));
    }

    // 库房列表
    @GetMapping("/list")
    @Operation(summary = "获得库房列表")
    @PreAuthorize("@ss.hasPermission('ciai:im-wh-info:query')")
    public CommonResult<List<ImWhInfoRespVO>> getImWhInfoList() {
        List<ImWhInfoDO> list = imWhInfoService.getImWhInfoList();
        return success(BeanUtils.toBean(list, ImWhInfoRespVO.class));
    }

}