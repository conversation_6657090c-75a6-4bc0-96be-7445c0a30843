package cn.iocoder.yudao.module.ciai.service.emvehicledriver;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emvehicledriver.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emvehicledriver.EmVehicleDriverDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 车辆司机 Service 接口
 *
 * <AUTHOR>
 */
public interface EmVehicleDriverService {

    /**
     * 创建车辆司机
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmVehicleDriver(@Valid EmVehicleDriverSaveReqVO createReqVO);

    /**
     * 更新车辆司机
     *
     * @param updateReqVO 更新信息
     */
    void updateEmVehicleDriver(@Valid EmVehicleDriverSaveReqVO updateReqVO);

    /**
     * 删除车辆司机
     *
     * @param id 编号
     */
    void deleteEmVehicleDriver(Long id);

    /**
     * 获得车辆司机
     *
     * @param id 编号
     * @return 车辆司机
     */
    EmVehicleDriverDO getEmVehicleDriver(Long id);

    /**
     * 获得车辆司机分页
     *
     * @param pageReqVO 分页查询
     * @return 车辆司机分页
     */
    PageResult<EmVehicleDriverDO> getEmVehicleDriverPage(EmVehicleDriverPageReqVO pageReqVO);

    /**
     * 获得车辆司机分页
     * @param reqVO
     * @return
     */
    PageResult<EmVehicleDriverDO> getVehicleDriverPage(EmVehicleDriverPageReqVO reqVO);
}