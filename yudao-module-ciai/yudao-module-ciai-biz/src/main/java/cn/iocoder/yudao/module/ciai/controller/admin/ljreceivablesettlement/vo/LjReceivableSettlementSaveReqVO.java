package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivablesettlement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 两金管理_应收_结算管理新增/修改 Request VO")
@Data
public class LjReceivableSettlementSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "结算单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "结算单号不能为空")
    private String settlementNumber;

    @Schema(description = "起始日期")
    private LocalDateTime startDate;

    @Schema(description = "结束日期")
    private LocalDateTime endDate;

    @Schema(description = "结算日期")
    private LocalDateTime settlementDate;

    @Schema(description = "出单日期")
    private LocalDateTime orderDate;

    @Schema(description = "任务计划ID")
    private Long taskPlanId;

    @Schema(description = "任务单号")
    private String taskNumber;

    @Schema(description = "施工部位")
    private String constructionPart;

    @Schema(description = "标号")
    private String serialNumber;

    @Schema(description = "产品分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "产品分类不能为空")
    private String productCategory;

    @Schema(description = "浇注方式")
    private String pouringMethod;

    @Schema(description = "泵送类型")
    private String pumpingType;

    @Schema(description = "车数")
    private Integer vehicleCount;

    @Schema(description = "生产方量")
    private BigDecimal productionVolume;

    @Schema(description = "砂浆方量")
    private BigDecimal mortarVolume;

    @Schema(description = "转入方量")
    private BigDecimal transferVolume;

    @Schema(description = "运输方量")
    private BigDecimal transportVolume;

    @Schema(description = "退货方量")
    private BigDecimal returnVolume;

    @Schema(description = "剩货方量")
    private BigDecimal remainingVolume;

    @Schema(description = "验收方量")
    private BigDecimal acceptanceVolume;

    @Schema(description = "原始单价")
    private BigDecimal originalUnitPrice;

    @Schema(description = "原始金额")
    private BigDecimal originalAmount;

    @Schema(description = "混凝土单价")
    private BigDecimal concreteUnitPrice;

    @Schema(description = "混凝土金额")
    private BigDecimal concreteAmount;

    @Schema(description = "泵送方量")
    private BigDecimal pumpingVolume;

    @Schema(description = "是否台班")
    private Integer isShift;

    @Schema(description = "泵送单价")
    private BigDecimal pumpingUnitPrice;

    @Schema(description = "泵送金额")
    private BigDecimal pumpingAmount;

    @Schema(description = "泵送剂量")
    private BigDecimal pumpingDose;

    @Schema(description = "泵送剂单价")
    private BigDecimal pumpingAgentUnitPrice;

    @Schema(description = "泵送剂金额")
    private BigDecimal pumpingAgentAmount;

    @Schema(description = "泵送台班费")
    private BigDecimal pumpingShiftFee;

    @Schema(description = "其他费用")
    private BigDecimal otherFee;

    @Schema(description = "其他费用2")
    private BigDecimal otherFee2;

    @Schema(description = "其他金额")
    private BigDecimal otherAmount;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "结算金额")
    private BigDecimal settlementAmount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "审核人")
    private String auditor;

    @Schema(description = "审核状态")
    private String auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

}