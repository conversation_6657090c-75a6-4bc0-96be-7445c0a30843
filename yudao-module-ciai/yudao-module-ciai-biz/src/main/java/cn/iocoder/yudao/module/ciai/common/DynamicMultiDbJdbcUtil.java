package cn.iocoder.yudao.module.ciai.common;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

public class DynamicMultiDbJdbcUtil {
    // 基础配置模板
    // 临时解决方案（禁用证书验证）
    private static final String GPS_DB_URL_TEMPLATE = "********************************************************************************************";
    private static final String GPS_USER = "sa";
    private static final String GPS_PASSWORD = "qhkjdb@2025";

    private static final String SSS_DB_URL = "***********************************************************";
    private static final String SSS_USER = "sa";
    private static final String SSS_PASSWORD = "qhkjdb@2025";

    // 连接缓存
    private static Connection gpsConnCache;
    private static String cachedGpsDbName;
    private static final Object lock = new Object();

    /**
     * 获取当前GPS库名（格式：NEWECOMGPSyyyyMM）
     */
    private static String getCurrentGpsDbName() {
        //return "NEWECOMGPS" + new SimpleDateFormat("yyyyMM").format(new Date());
        return "NEWECOMGPS2505";
    }

    /**
     * 获取GPS表名（格式：gps_data_yyyyMMdd）
     * @param date 指定日期（null表示当天）
     */
    public static String getGpsTableName(Date date) {
        String pattern = "yyMMdd"; // 修改为2位年份+月日
        String dateStr = date == null ?
                new SimpleDateFormat(pattern).format(new Date()) :
                new SimpleDateFormat(pattern).format(date);
        return "gps" + dateStr; // 拼接前缀 "gps"
    }

    /**
     * 获取GPS数据库连接（带自动刷新机制）
     */
    public static Connection getGpsConnection() throws SQLException {
        synchronized (lock) {
            String currentDbName = getCurrentGpsDbName();

            // 需要刷新连接的三种情况
            if (gpsConnCache == null ||
                    !currentDbName.equals(cachedGpsDbName) ||
                    gpsConnCache.isClosed()) {

                closeResources(gpsConnCache, null, null);

                String url = String.format(GPS_DB_URL_TEMPLATE, currentDbName);
                gpsConnCache = DriverManager.getConnection(url, GPS_USER, GPS_PASSWORD);
                cachedGpsDbName = currentDbName;

                System.out.println("创建新GPS连接：" + currentDbName);
            }
            return gpsConnCache;
        }
    }

    /**
     * 获取SSS数据库连接
     */
    public static Connection getSssConnection() throws SQLException {
        return DriverManager.getConnection(SSS_DB_URL, SSS_USER, SSS_PASSWORD);
    }

    /**
     * 安全执行GPS查询（自动处理动态表名）
     * @param date 指定表日期
     * @param whereClause WHERE条件（不含WHERE关键字）
     * @param params 查询参数
     */
    public static List<Map<String, Object>> safeGpsQuery(Date date, String whereClause, Object... params) throws SQLException {
        // String tableName = validateTableName(getGpsTableName(date));
        String sql = String.format("SELECT * FROM gps250501 WHERE %s", whereClause);
        return executeQuery(getGpsConnection(), sql, params);
    }

    /**
     * 验证表名合法性（防止SQL注入）
     */
    private static String validateTableName(String tableName) {
        if (!tableName.matches("[a-zA-Z0-9_]+")) {
            throw new IllegalArgumentException("非法表名: " + tableName);
        }
        return tableName;
    }

    /**
     * 通用查询执行
     */
    private static List<Map<String, Object>> executeQuery(Connection conn, String sql, Object... params) {
        List<Map<String, Object>> result = new ArrayList<>();
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            pstmt = conn.prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            rs = pstmt.executeQuery();

            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new LinkedHashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    row.put(meta.getColumnLabel(i), rs.getObject(i));
                }
                result.add(row);
            }
        } catch (SQLException e) {
            handleSQLException("查询执行失败", e, sql);
        } finally {
            closeResources(null, pstmt, rs); // 保持连接开放供复用
        }
        return result;
    }

    /**
     * 跨库数据同步示例
     */
    public static void syncDailyData() {
        Connection gpsConn = null;
        Connection sssConn = null;

        try {
            // 1. 获取连接
            gpsConn = getGpsConnection();
            sssConn = getSssConnection();

            // 2. 获取当天数据
            String todayTable = getGpsTableName(null);
            List<Map<String, Object>> data = executeQuery(gpsConn,
                    "SELECT vehicle_id, track_time FROM " + todayTable + " WHERE sync_flag = 0");

            // 3. 插入SSS库
            try (PreparedStatement pstmt = sssConn.prepareStatement(
                    "INSERT INTO history_tracks(vehicle_id, track_time) VALUES (?, ?)")) {

                sssConn.setAutoCommit(false);

                for (Map<String, Object> row : data) {
                    pstmt.setString(1, (String) row.get("vehicle_id"));
                    pstmt.setTimestamp(2, (Timestamp) row.get("track_time"));
                    pstmt.addBatch();
                }

                pstmt.executeBatch();
                sssConn.commit();

                // 4. 标记已同步
                try (Statement stmt = gpsConn.createStatement()) {
                    int updated = stmt.executeUpdate(
                            "UPDATE " + todayTable + " SET sync_flag = 1 WHERE sync_flag = 0");
                    System.out.println("标记同步记录数: " + updated);
                }
            }

        } catch (SQLException e) {
            handleSQLException("数据同步失败", e, null);
            rollbackQuietly(sssConn);
        } finally {
            closeResources(gpsConn, null, null);
            closeResources(sssConn, null, null);
        }
    }

    /**
     * 异常处理统一方法
     */
    private static void handleSQLException(String message, SQLException e, String sql) {
        String errorInfo = String.format("%s\nSQL状态: %s\n错误码: %s\n消息: %s\nSQL: %s",
                message,
                e.getSQLState(),
                e.getErrorCode(),
                e.getMessage(),
                sql);

        System.err.println(errorInfo);
        // 此处可接入监控系统报警
    }

    private static void rollbackQuietly(Connection conn) {
        try {
            if (conn != null) conn.rollback();
        } catch (SQLException ex) {
            System.err.println("回滚失败: " + ex.getMessage());
        }
    }

    /**
     * 资源关闭方法（改进版）
     */
    public static void closeResources(Connection conn, Statement stmt, ResultSet rs) {
        try {
            if (rs != null && !rs.isClosed()) rs.close();
            if (stmt != null && !stmt.isClosed()) stmt.close();
            // 连接由连接池管理时不关闭
        } catch (SQLException e) {
            System.err.println("资源关闭异常: " + e.getMessage());
        }
    }

    public static void main(String[] args) throws SQLException {
        // 示例1：查询当天数据
        List<Map<String, Object>> todayData = safeGpsQuery(null,
                "speed > ? AND status = ?", 60, 1);
        System.out.println("当天超速记录：" + todayData.size() + " 条");

        // 示例2：查询历史数据
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1); // 昨天
        List<Map<String, Object>> historyData = safeGpsQuery(cal.getTime(),
                "vehicle_id = 'VH_1001'");
        System.out.println("昨日车辆轨迹：" + historyData);

        // 示例3：执行数据同步
        syncDailyData();
    }
}