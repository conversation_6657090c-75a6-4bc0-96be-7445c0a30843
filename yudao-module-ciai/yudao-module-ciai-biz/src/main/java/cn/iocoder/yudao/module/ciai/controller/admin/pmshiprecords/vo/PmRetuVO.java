package cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PmRetuVO {

    @Schema(description = "发货单ID")
    private Long id;

    @Schema(description = "工程名称")
    private String projectName;

    @Schema(description = "施工单位名称")
    private String companyName;

    @Schema(description = "标号")
    private String grade;

    @Schema(description = "车辆自编号")
    private String selfNumber;

    @Schema(description = "司机姓名")
    private String driverName;

    @Schema(description = "任务单ID")
    private Long taskPlanId;

    @Schema(description = "发货单号")
    private String deliveryNumber;

    @Schema(description = "车辆ID")
    private Long vehicleId;

    @Schema(description = "司机ID")
    private Long driverId;

    @Schema(description = "回厂余料方量")
    private Double returnMaterialVolume;

    @Schema(description = "剩余余料量")
    private Double remainingMaterialVolume;


}
