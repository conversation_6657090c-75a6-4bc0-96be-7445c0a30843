package cn.iocoder.yudao.module.ciai.service.ljreceivableaccounting;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableaccounting.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.ljreceivableaccounting.LjReceivableAccountingDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 应收入账管理 Service 接口
 *
 * <AUTHOR>
 */
public interface LjReceivableAccountingService {

    /**
     * 创建应收入账管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLjReceivableAccounting(@Valid LjReceivableAccountingSaveReqVO createReqVO);

    /**
     * 更新应收入账管理
     *
     * @param updateReqVO 更新信息
     */
    void updateLjReceivableAccounting(@Valid LjReceivableAccountingSaveReqVO updateReqVO);

    /**
     * 删除应收入账管理
     *
     * @param id 编号
     */
    void deleteLjReceivableAccounting(Long id);

    /**
     * 获得应收入账管理
     *
     * @param id 编号
     * @return 应收入账管理
     */
    LjReceivableAccountingDO getLjReceivableAccounting(Long id);

    /**
     * 获得应收入账管理分页
     *
     * @param pageReqVO 分页查询
     * @return 应收入账管理分页
     */
    PageResult<LjReceivableAccountingDO> getLjReceivableAccountingPage(LjReceivableAccountingPageReqVO pageReqVO);


    /**
     * 获取详细的联机应收账款会计分录分页结果。
     *
     * @param pageReqVO 分页请求参数对象，包含查询条件和分页信息。
     * @return 包含详细联机应收账款会计分录信息的分页结果对象。
     */
    PageResult<DetailedLjReceivableAccountingRespVO> getLjReceivableAccountingDetailedPage(@Valid LjReceivableAccountingPageReqVO pageReqVO);

    DetailedLjReceivableAccountingRespVO getLjReceivableAccountingDetails(Long id);

    /**
     * 执行应收账款结算
     *
     * @param settleRequestVO 结算请求参数
     * @return 结算结果
     */
    String performSettlement(@Valid SettleRequestVO settleRequestVO);
}