package cn.iocoder.yudao.module.ciai.controller.admin.iminborddetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 入库明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImInbOrdDetailRespVO {

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "入库单ID", example = "4622")
    @ExcelProperty("入库单ID")
    private Long inboundOrderId;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "355")
    @ExcelProperty("物料ID")
    private Long materialId;

    @Schema(description = "数量")
    @ExcelProperty("数量")
    private BigDecimal quantity;

    @Schema(description = "单价", example = "6451")
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @Schema(description = "金额")
    @ExcelProperty("金额")
    private BigDecimal amount;

    @Schema(description = "库房ID", example = "3248")
    @ExcelProperty("库房ID")
    private Long warehouseId;

    @Schema(description = "库位ID", example = "21007")
    @ExcelProperty("库位ID")
    private Long storageLocationId;

    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remarks;

}