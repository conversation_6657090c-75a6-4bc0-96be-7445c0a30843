package cn.iocoder.yudao.module.ciai.controller.admin.wzpurchaseplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 物资管理_采购计划新增/修改 Request VO")
@Data
public class WzPurchasePlanSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "供应商合同ID")
    private Integer supplierContractId;

    @Schema(description = "计划采购数量")
    private BigDecimal plannedPurchaseQuantity;

    @Schema(description = "采购单价")
    private BigDecimal purchaseUnitPrice;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "计划采购日期")
    private LocalDateTime plannedPurchaseDate;

    @Schema(description = "要求到货日期")
    private LocalDateTime requiredDeliveryDate;

    @Schema(description = "实际到货日期")
    private LocalDateTime actualDeliveryDate;

    @Schema(description = "运输方式")
    private String transportMethod;

    @Schema(description = "质量要求")
    private String qualityRequirements;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "申请人")
    private String applicant;

    @Schema(description = "审批人")
    private String approver;

}