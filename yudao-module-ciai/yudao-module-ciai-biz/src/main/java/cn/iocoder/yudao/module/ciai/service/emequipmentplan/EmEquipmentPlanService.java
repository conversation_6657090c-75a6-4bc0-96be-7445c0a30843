package cn.iocoder.yudao.module.ciai.service.emequipmentplan;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentplan.EmEquipmentPlanDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 设备管理_设备计划 Service 接口
 *
 * <AUTHOR>
 */
public interface EmEquipmentPlanService {

    /**
     * 创建设备管理_设备计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmEquipmentPlan(@Valid EmEquipmentPlanSaveReqVO createReqVO);

    /**
     * 更新设备管理_设备计划
     *
     * @param updateReqVO 更新信息
     */
    void updateEmEquipmentPlan(@Valid EmEquipmentPlanSaveReqVO updateReqVO);

    /**
     * 删除设备管理_设备计划
     *
     * @param id 编号
     */
    void deleteEmEquipmentPlan(Long id);

    /**
     * 获得设备管理_设备计划
     *
     * @param id 编号
     * @return 设备管理_设备计划
     */
    EmEquipmentPlanDO getEmEquipmentPlan(Long id);

    /**
     * 获得设备管理_设备计划分页
     *
     * @param pageReqVO 分页查询
     * @return 设备管理_设备计划分页
     */
    PageResult<EmEquipmentPlanDO> getEmEquipmentPlanPage(EmEquipmentPlanPageReqVO pageReqVO);

}