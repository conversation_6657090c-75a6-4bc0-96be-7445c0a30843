package cn.iocoder.yudao.module.ciai.controller.admin.jsmixsilomapping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 技术管理_配比仓位对应 Response VO")
@Data
@ExcelIgnoreUnannotated
public class JsMixSiloMappingRespVO {

    @Schema(description = "生产主机ID")
    @ExcelProperty("生产主机ID")
    private Long productionHostId;

    @Schema(description = "水泥1种类")
    @ExcelProperty("水泥1种类")
    private String cement1Type;

    @Schema(description = "水泥2种类")
    @ExcelProperty("水泥2种类")
    private String cement2Type;

    @Schema(description = "水泥3种类")
    @ExcelProperty("水泥3种类")
    private String cement3Type;

    @Schema(description = "水泥1规格")
    @ExcelProperty("水泥1规格")
    private String cement1Spec;

    @Schema(description = "水泥2规格")
    @ExcelProperty("水泥2规格")
    private String cement2Spec;

    @Schema(description = "水泥3规格")
    @ExcelProperty("水泥3规格")
    private String cement3Spec;

    @Schema(description = "水泥1仓号")
    @ExcelProperty("水泥1仓号")
    private String cement1Silo;

    @Schema(description = "水泥2仓号")
    @ExcelProperty("水泥2仓号")
    private String cement2Silo;

    @Schema(description = "水泥3仓号")
    @ExcelProperty("水泥3仓号")
    private String cement3Silo;

    @Schema(description = "掺合料1种类")
    @ExcelProperty("掺合料1种类")
    private String admixture1Type;

    @Schema(description = "掺合料2种类")
    @ExcelProperty("掺合料2种类")
    private String admixture2Type;

    @Schema(description = "掺合料3种类")
    @ExcelProperty("掺合料3种类")
    private String admixture3Type;

    @Schema(description = "掺合料1规格")
    @ExcelProperty("掺合料1规格")
    private String admixture1Spec;

    @Schema(description = "掺合料2规格")
    @ExcelProperty("掺合料2规格")
    private String admixture2Spec;

    @Schema(description = "掺合料3规格")
    @ExcelProperty("掺合料3规格")
    private String admixture3Spec;

    @Schema(description = "掺合料1仓号")
    @ExcelProperty("掺合料1仓号")
    private String admixture1Silo;

    @Schema(description = "掺合料2仓号")
    @ExcelProperty("掺合料2仓号")
    private String admixture2Silo;

    @Schema(description = "掺合料3仓号")
    @ExcelProperty("掺合料3仓号")
    private String admixture3Silo;

    @Schema(description = "砂1种类")
    @ExcelProperty("砂1种类")
    private String sand1Type;

    @Schema(description = "砂2种类")
    @ExcelProperty("砂2种类")
    private String sand2Type;

    @Schema(description = "砂3种类")
    @ExcelProperty("砂3种类")
    private String sand3Type;

    @Schema(description = "砂4种类")
    @ExcelProperty("砂4种类")
    private String sand4Type;

    @Schema(description = "砂1规格")
    @ExcelProperty("砂1规格")
    private String sand1Spec;

    @Schema(description = "砂2规格")
    @ExcelProperty("砂2规格")
    private String sand2Spec;

    @Schema(description = "砂3规格")
    @ExcelProperty("砂3规格")
    private String sand3Spec;

    @Schema(description = "砂4规格")
    @ExcelProperty("砂4规格")
    private String sand4Spec;

    @Schema(description = "砂1仓号")
    @ExcelProperty("砂1仓号")
    private String sand1Silo;

    @Schema(description = "砂2仓号")
    @ExcelProperty("砂2仓号")
    private String sand2Silo;

    @Schema(description = "砂3仓号")
    @ExcelProperty("砂3仓号")
    private String sand3Silo;

    @Schema(description = "砂4仓号")
    @ExcelProperty("砂4仓号")
    private String sand4Silo;

    @Schema(description = "石1种类")
    @ExcelProperty("石1种类")
    private String stone1Type;

    @Schema(description = "石2种类")
    @ExcelProperty("石2种类")
    private String stone2Type;

    @Schema(description = "石3种类")
    @ExcelProperty("石3种类")
    private String stone3Type;

    @Schema(description = "石4种类")
    @ExcelProperty("石4种类")
    private String stone4Type;

    @Schema(description = "石1规格")
    @ExcelProperty("石1规格")
    private String stone1Spec;

    @Schema(description = "石2规格")
    @ExcelProperty("石2规格")
    private String stone2Spec;

    @Schema(description = "石3规格")
    @ExcelProperty("石3规格")
    private String stone3Spec;

    @Schema(description = "石4规格")
    @ExcelProperty("石4规格")
    private String stone4Spec;

    @Schema(description = "石1仓号")
    @ExcelProperty("石1仓号")
    private String stone1Silo;

    @Schema(description = "石2仓号")
    @ExcelProperty("石2仓号")
    private String stone2Silo;

    @Schema(description = "石3仓号")
    @ExcelProperty("石3仓号")
    private String stone3Silo;

    @Schema(description = "石4仓号")
    @ExcelProperty("石4仓号")
    private String stone4Silo;

    @Schema(description = "水1种类")
    @ExcelProperty("水1种类")
    private String water1Type;

    @Schema(description = "水2种类")
    @ExcelProperty("水2种类")
    private String water2Type;

    @Schema(description = "水1规格")
    @ExcelProperty("水1规格")
    private String water1Spec;

    @Schema(description = "水2规格")
    @ExcelProperty("水2规格")
    private String water2Spec;

    @Schema(description = "水1仓号")
    @ExcelProperty("水1仓号")
    private String water1Silo;

    @Schema(description = "水2仓号")
    @ExcelProperty("水2仓号")
    private String water2Silo;

    @Schema(description = "外加剂1种类")
    @ExcelProperty("外加剂1种类")
    private String additive1Type;

    @Schema(description = "外加剂2种类")
    @ExcelProperty("外加剂2种类")
    private String additive2Type;

    @Schema(description = "外加剂3种类")
    @ExcelProperty("外加剂3种类")
    private String additive3Type;

    @Schema(description = "外加剂4种类")
    @ExcelProperty("外加剂4种类")
    private String additive4Type;

    @Schema(description = "外加剂1规格")
    @ExcelProperty("外加剂1规格")
    private String additive1Spec;

    @Schema(description = "外加剂2规格")
    @ExcelProperty("外加剂2规格")
    private String additive2Spec;

    @Schema(description = "外加剂3规格")
    @ExcelProperty("外加剂3规格")
    private String additive3Spec;

    @Schema(description = "外加剂4规格")
    @ExcelProperty("外加剂4规格")
    private String additive4Spec;

    @Schema(description = "外加剂1仓号")
    @ExcelProperty("外加剂1仓号")
    private String additive1Silo;

    @Schema(description = "外加剂2仓号")
    @ExcelProperty("外加剂2仓号")
    private String additive2Silo;

    @Schema(description = "外加剂3仓号")
    @ExcelProperty("外加剂3仓号")
    private String additive3Silo;

    @Schema(description = "外加剂4仓号")
    @ExcelProperty("外加剂4仓号")
    private String additive4Silo;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @ExcelProperty("修改人")
    private String updater;

    @Schema(description = "修改时间")
    @ExcelProperty("修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "启用状态 (1:启用 0:停用)")
    @ExcelProperty("启用状态 (1:启用 0:停用)")
    private Boolean enabled;

}