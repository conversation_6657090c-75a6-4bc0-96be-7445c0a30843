package cn.iocoder.yudao.module.ciai.controller.admin.jsrawmaterialtestmain.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 原材试验分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JsRawMaterialTestMainPageReqVO extends PageParam {

    @Schema(description = "原材类型ID", example = "4496")
    private Long rawMaterialTypeId;

    @Schema(description = "原材类型", example = "2")
    private String rawMaterialType;

    @Schema(description = "委托日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] commissionDate;

}