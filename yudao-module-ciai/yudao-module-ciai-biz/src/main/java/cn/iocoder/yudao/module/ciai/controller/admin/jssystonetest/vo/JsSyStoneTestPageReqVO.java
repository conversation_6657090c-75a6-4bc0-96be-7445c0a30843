package cn.iocoder.yudao.module.ciai.controller.admin.jssystonetest.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 技术管理_原材试验_石试验分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class JsSyStoneTestPageReqVO extends PageParam {

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "试验编号")
    private String testNo;

    @Schema(description = "委托编号")
    private String entrustNo;

    @Schema(description = "委托单位")
    private String entrustUnit;

    @Schema(description = "工程名称", example = "张三")
    private String projectName;

    @Schema(description = "种类编码")
    private String typeCode;

    @Schema(description = "种类", example = "2")
    private String type;

    @Schema(description = "规格")
    private String specification;

    @Schema(description = "产地")
    private String origin;

    @Schema(description = "粒径")
    private String particleSize;

    @Schema(description = "最大粒径")
    private String maxParticleSize;

    @Schema(description = "代表数量")
    private String representQuantity;

    @Schema(description = "收样日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sampleDate;

    @Schema(description = "试验日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] testDate;

    @Schema(description = "试样编号")
    private String sampleNo;

    @Schema(description = "截止日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] deadlineDate;

    @Schema(description = "针片状含量")
    private BigDecimal needleFlakeContent;

    @Schema(description = "含泥量")
    private BigDecimal mudContent;

    @Schema(description = "泥块含量")
    private BigDecimal clayLumpContent;

    @Schema(description = "压碎值指标")
    private BigDecimal crushingValueIndex;

    @Schema(description = "结论")
    private String conclusion;

    @Schema(description = "试验委托人")
    private String testClient;

    @Schema(description = "负责人")
    private String responsiblePerson;

    @Schema(description = "委托人")
    private String client;

    @Schema(description = "审核人")
    private String reviewer;

    @Schema(description = "试验人")
    private String tester;

    @Schema(description = "级配情况")
    private String gradingSituation;

    @Schema(description = "级配结果")
    private String gradingResult;

    @Schema(description = "表观密度")
    private Integer apparentDensity;

    @Schema(description = "堆积密度")
    private Integer bulkDensity;

    @Schema(description = "堆积密度空隙率")
    private Integer bulkDensityVoidRatio;

    @Schema(description = "密度")
    private String density;

    @Schema(description = "紧密密度")
    private Integer compactDensity;

    @Schema(description = "紧密密度空隙率")
    private Integer compactDensityVoidRatio;

    @Schema(description = "碱活性指标")
    private BigDecimal alkaliActivityIndex;

    @Schema(description = "碱含量")
    private BigDecimal alkaliContent;

    @Schema(description = "氯离子")
    private BigDecimal chlorideIon;

    @Schema(description = "空隙率")
    private BigDecimal voidRatio;

    @Schema(description = "含水率")
    private String moistureContent;

    @Schema(description = "坚固性")
    private String soundness;

    @Schema(description = "其它")
    private String others;

    @Schema(description = "报告日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] reportDate;

    @Schema(description = "ZL")
    private String zl;

    @Schema(description = "ZHL")
    private String zhl;

    @Schema(description = "HN11")
    private Double hn11;

    @Schema(description = "HN12")
    private Double hn12;

    @Schema(description = "HN13")
    private Double hn13;

    @Schema(description = "HN21")
    private Double hn21;

    @Schema(description = "HN22")
    private Double hn22;

    @Schema(description = "HN23")
    private Double hn23;

    @Schema(description = "HNV")
    private BigDecimal hnV;

    @Schema(description = "NK11")
    private Double nk11;

    @Schema(description = "NK12")
    private Double nk12;

    @Schema(description = "NK13")
    private Double nk13;

    @Schema(description = "NK21")
    private Double nk21;

    @Schema(description = "NK22")
    private Double nk22;

    @Schema(description = "NK23")
    private Double nk23;

    @Schema(description = "NKV")
    private BigDecimal nkV;

    @Schema(description = "ZHP1")
    private String zhp1;

    @Schema(description = "ZHP2")
    private String zhp2;

    @Schema(description = "ZHP3")
    private String zhp3;

    @Schema(description = "ZHP4")
    private String zhp4;

    @Schema(description = "YSZ11")
    private String ysz11;

    @Schema(description = "YSZ21")
    private String ysz21;

    @Schema(description = "YSZ31")
    private String ysz31;

    @Schema(description = "YSZ12")
    private String ysz12;

    @Schema(description = "YSZ22")
    private String ysz22;

    @Schema(description = "YSZ32")
    private String ysz32;

    @Schema(description = "YSZ13")
    private String ysz13;

    @Schema(description = "YSZ23")
    private String ysz23;

    @Schema(description = "YSZ33")
    private String ysz33;

    @Schema(description = "YSZV")
    private String yszV;

    @Schema(description = "修改人")
    private String modifier;

    @Schema(description = "时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] modifyTime;

    @Schema(description = "检测标准")
    private String testStandard;

    @Schema(description = "筛分析重量")
    private Integer sieveAnalysisWeight;

    @Schema(description = "SHAI101")
    private Double sieve101;

    @Schema(description = "SHAI102")
    private Double sieve102;

    @Schema(description = "SHAI103")
    private Double sieve103;

    @Schema(description = "SHAI104")
    private Double sieve104;

    @Schema(description = "SHAI105")
    private Double sieve105;

    @Schema(description = "SHAI106")
    private Double sieve106;

    @Schema(description = "SHAI107")
    private Double sieve107;

    @Schema(description = "SHAI108")
    private Double sieve108;

    @Schema(description = "SHAI109")
    private Double sieve109;

    @Schema(description = "SHAI110")
    private Double sieve110;

    @Schema(description = "SHAI111")
    private Double sieve111;

    @Schema(description = "SHAI112")
    private Double sieve112;

    @Schema(description = "SHAI113")
    private Double sieve113;

    @Schema(description = "SHAI201")
    private Double sieve201;

    @Schema(description = "SHAI202")
    private Double sieve202;

    @Schema(description = "SHAI203")
    private Double sieve203;

    @Schema(description = "SHAI204")
    private Double sieve204;

    @Schema(description = "SHAI205")
    private Double sieve205;

    @Schema(description = "SHAI206")
    private Double sieve206;

    @Schema(description = "SHAI207")
    private Double sieve207;

    @Schema(description = "SHAI208")
    private Double sieve208;

    @Schema(description = "SHAI209")
    private Double sieve209;

    @Schema(description = "SHAI210")
    private Double sieve210;

    @Schema(description = "SHAI211")
    private Double sieve211;

    @Schema(description = "SHAI212")
    private Double sieve212;

    @Schema(description = "SHAI213")
    private Double sieve213;

    @Schema(description = "SHAI301")
    private Double sieve301;

    @Schema(description = "SHAI302")
    private Double sieve302;

    @Schema(description = "SHAI303")
    private Double sieve303;

    @Schema(description = "SHAI304")
    private Double sieve304;

    @Schema(description = "SHAI305")
    private Double sieve305;

    @Schema(description = "SHAI306")
    private Double sieve306;

    @Schema(description = "SHAI307")
    private Double sieve307;

    @Schema(description = "SHAI308")
    private Double sieve308;

    @Schema(description = "SHAI309")
    private Double sieve309;

    @Schema(description = "SHAI310")
    private Double sieve310;

    @Schema(description = "SHAI311")
    private Double sieve311;

    @Schema(description = "SHAI312")
    private Double sieve312;

    @Schema(description = "SHAI313")
    private Double sieve313;

    @Schema(description = "SHAIL01")
    private Double sieveL01;

    @Schema(description = "SHAIL02")
    private Double sieveL02;

    @Schema(description = "SHAIL03")
    private Double sieveL03;

    @Schema(description = "SHAIL04")
    private Double sieveL04;

    @Schema(description = "SHAIL05")
    private Double sieveL05;

    @Schema(description = "SHAIL06")
    private Double sieveL06;

    @Schema(description = "SHAIL07")
    private Double sieveL07;

    @Schema(description = "SHAIL08")
    private Double sieveL08;

    @Schema(description = "SHAIL09")
    private Double sieveL09;

    @Schema(description = "SHAIL10")
    private Double sieveL10;

    @Schema(description = "SHAIL11")
    private Double sieveL11;

    @Schema(description = "SHAIL12")
    private Double sieveL12;

    @Schema(description = "SHAID")
    private Double sieveD;

    @Schema(description = "BG11")
    private Double bg11;

    @Schema(description = "BG12")
    private Double bg12;

    @Schema(description = "BG13")
    private Double bg13;

    @Schema(description = "BG14")
    private Double bg14;

    @Schema(description = "BG15")
    private Double bg15;

    @Schema(description = "BGMD1")
    private Integer bgMd1;

    @Schema(description = "BG21")
    private Double bg21;

    @Schema(description = "BG22")
    private Double bg22;

    @Schema(description = "BG23")
    private Double bg23;

    @Schema(description = "BG24")
    private Double bg24;

    @Schema(description = "BG25")
    private Double bg25;

    @Schema(description = "BGMD2")
    private Integer bgMd2;

    @Schema(description = "BGMDV")
    private Integer bgMdv;

    @Schema(description = "DJ11")
    private Double dj11;

    @Schema(description = "DJ12")
    private Double dj12;

    @Schema(description = "DJ13")
    private Double dj13;

    @Schema(description = "DJMD1")
    private Integer djMd1;

    @Schema(description = "DJ21")
    private Double dj21;

    @Schema(description = "DJ22")
    private Double dj22;

    @Schema(description = "DJ23")
    private Double dj23;

    @Schema(description = "DJMD2")
    private Integer djMd2;

    @Schema(description = "DJMDV")
    private Integer djMdv;

    @Schema(description = "ZPSYZL")
    private Integer zpSyzl;

    @Schema(description = "ZP1")
    private Integer zp1;

    @Schema(description = "ZP2")
    private Integer zp2;

    @Schema(description = "ZPZL")
    private Integer zpZl;

    @Schema(description = "ZP")
    private Integer zp;

    @Schema(description = "YS11")
    private Integer ys11;

    @Schema(description = "YS12")
    private Integer ys12;

    @Schema(description = "YSZB1")
    private BigDecimal ysZb1;

    @Schema(description = "YS21")
    private Integer ys21;

    @Schema(description = "YS22")
    private Integer ys22;

    @Schema(description = "YSZB2")
    private BigDecimal ysZb2;

    @Schema(description = "YS31")
    private Integer ys31;

    @Schema(description = "YS32")
    private Integer ys32;

    @Schema(description = "YSZB3")
    private BigDecimal ysZb3;

    @Schema(description = "YSZBV")
    private BigDecimal ysZbv;

    @Schema(description = "JM11")
    private Double jm11;

    @Schema(description = "JM12")
    private Double jm12;

    @Schema(description = "JM13")
    private Double jm13;

    @Schema(description = "JMMD1")
    private Integer jmMd1;

    @Schema(description = "JM21")
    private Double jm21;

    @Schema(description = "JM22")
    private Double jm22;

    @Schema(description = "JM23")
    private Double jm23;

    @Schema(description = "JMMD2")
    private Integer jmMd2;

    @Schema(description = "JMMDV")
    private Integer jmMdv;

    @Schema(description = "执行标准")
    private String standard;

    @Schema(description = "试验状态", example = "2")
    private String testStatus;

    @Schema(description = "使用状态", example = "1")
    private String usageStatus;

    @Schema(description = "选择")
    private Integer selected;

    @Schema(description = "打印")
    private Integer printed;

    @Schema(description = "Y")
    private Integer yValue;

    @Schema(description = "站别")
    private String stationCode;

    @Schema(description = "试验人2")
    private String tester2;

    @Schema(description = "执行标准2")
    private String standard2;

    @Schema(description = "工程类型", example = "2")
    private String projectType;

    @Schema(description = "出厂编号")
    private String factoryNo;

    @Schema(description = "厂家牌号")
    private String factoryBrand;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}