package cn.iocoder.yudao.module.ciai.dal.mysql.jssyexpansiveagenttest;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jssyexpansiveagenttest.JsSyExpansiveAgentTestDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.jssyexpansiveagenttest.vo.*;

/**
 * 膨胀剂试验 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JsSyExpansiveAgentTestMapper extends BaseMapperX<JsSyExpansiveAgentTestDO> {

    default PageResult<JsSyExpansiveAgentTestDO> selectPage(JsSyExpansiveAgentTestPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JsSyExpansiveAgentTestDO>()
                .eqIfPresent(JsSyExpansiveAgentTestDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTestNo, reqVO.getTestNo())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getEntrustNo, reqVO.getEntrustNo())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSampleNo, reqVO.getSampleNo())
                .likeIfPresent(JsSyExpansiveAgentTestDO::getProjectName, reqVO.getProjectName())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getEntrustUnit, reqVO.getEntrustUnit())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTestClient, reqVO.getTestClient())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getResponsiblePerson, reqVO.getResponsiblePerson())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getReviewer, reqVO.getReviewer())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTester, reqVO.getTester())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTypeCode, reqVO.getTypeCode())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getType, reqVO.getType())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSpecification, reqVO.getSpecification())
                .likeIfPresent(JsSyExpansiveAgentTestDO::getProductName, reqVO.getProductName())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getFactoryDate, reqVO.getFactoryDate())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getRepresentQuantity, reqVO.getRepresentQuantity())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getSampleDate, reqVO.getSampleDate())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getTestDate, reqVO.getTestDate())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getDeadlineDate, reqVO.getDeadlineDate())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTestItem, reqVO.getTestItem())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getInitialSettingTime, reqVO.getInitialSettingTime())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getFinalSettingTime, reqVO.getFinalSettingTime())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getExpansiveAgentDosage, reqVO.getExpansiveAgentDosage())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getRecommendedDosage, reqVO.getRecommendedDosage())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getRebarCorrosionNo, reqVO.getRebarCorrosionNo())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getDensity, reqVO.getDensity())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCertification, reqVO.getCertification())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getFlexuralStrengthRatio7d, reqVO.getFlexuralStrengthRatio7d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getFlexuralStrengthRatio28d, reqVO.getFlexuralStrengthRatio28d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCompressiveStrengthRatio7d, reqVO.getCompressiveStrengthRatio7d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCompressiveStrengthRatio28d, reqVO.getCompressiveStrengthRatio28d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getConclusion, reqVO.getConclusion())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getModel, reqVO.getModel())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getOther, reqVO.getOther())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getReportDate, reqVO.getReportDate())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getAlkaliContent, reqVO.getAlkaliContent())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getChlorideIon, reqVO.getChlorideIon())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getModifier, reqVO.getModifier())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getModifyTime, reqVO.getModifyTime())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getAge, reqVO.getAge())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getWater7dCalcEvaluation, reqVO.getWater7dCalcEvaluation())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getWater28dCalcEvaluation, reqVO.getWater28dCalcEvaluation())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getAir21dCalcEvaluation, reqVO.getAir21dCalcEvaluation())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSpecificSurfaceArea, reqVO.getSpecificSurfaceArea())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getScreenResidue, reqVO.getScreenResidue())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getPressureTestDate, reqVO.getPressureTestDate())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl1, reqVO.getZl1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl2, reqVO.getZl2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl3, reqVO.getZl3())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZlV, reqVO.getZlV())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl1, reqVO.getYl1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl2, reqVO.getYl2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl3, reqVO.getYl3())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl4, reqVO.getYl4())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl5, reqVO.getYl5())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl6, reqVO.getYl6())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYlV, reqVO.getYlV())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl11, reqVO.getYl11())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl21, reqVO.getYl21())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl31, reqVO.getYl31())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl41, reqVO.getYl41())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl51, reqVO.getYl51())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYl61, reqVO.getYl61())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYlV1, reqVO.getYlV1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCscd1, reqVO.getCscd1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCscd2, reqVO.getCscd2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getCscd3, reqVO.getCscd3())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz71, reqVO.getXz71())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz72, reqVO.getXz72())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz73, reqVO.getXz73())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz7v, reqVO.getXz7v())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getS7dl1, reqVO.getS7dl1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getS7dl2, reqVO.getS7dl2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getS7dl3, reqVO.getS7dl3())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz211, reqVO.getXz211())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz212, reqVO.getXz212())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz213, reqVO.getXz213())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXz21v, reqVO.getXz21v())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getK21dl1, reqVO.getK21dl1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getK21dl2, reqVO.getK21dl2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getK21dl3, reqVO.getK21dl3())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSampleWeight, reqVO.getSampleWeight())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getScreenResidueWeight, reqVO.getScreenResidueWeight())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSy1, reqVO.getSy1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getXd1, reqVO.getXd1())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getUsageStatus, reqVO.getUsageStatus())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getSelected, reqVO.getSelected())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getPrinted, reqVO.getPrinted())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getYValue, reqVO.getYValue())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getStationCode, reqVO.getStationCode())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getTester2, reqVO.getTester2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getStandard2, reqVO.getStandard2())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getStandard, reqVO.getStandard())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getReportDate7d, reqVO.getReportDate7d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getReportDate28d, reqVO.getReportDate28d())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getProjectType, reqVO.getProjectType())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl4, reqVO.getZl4())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl5, reqVO.getZl5())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZl6, reqVO.getZl6())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getZlV28, reqVO.getZlV28())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getFactoryBrand, reqVO.getFactoryBrand())
                .eqIfPresent(JsSyExpansiveAgentTestDO::getFactoryNo, reqVO.getFactoryNo())
                .betweenIfPresent(JsSyExpansiveAgentTestDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(JsSyExpansiveAgentTestDO::getId));
    }

}