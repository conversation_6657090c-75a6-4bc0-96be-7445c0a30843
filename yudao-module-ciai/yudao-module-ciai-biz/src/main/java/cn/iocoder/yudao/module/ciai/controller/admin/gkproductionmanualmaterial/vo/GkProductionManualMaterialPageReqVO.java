package cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualmaterial.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 生产手动消耗原材料分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GkProductionManualMaterialPageReqVO extends PageParam {

    @Schema(description = "编号")
    private Integer no;

    @Schema(description = "生产时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] productionTime;

    @Schema(description = "生产主机")
    private Long productionHost;

    @Schema(description = "仓位编码")
    private Long storageLocation;

    @Schema(description = "仓位名称")
    private String storageLocationName;

    @Schema(description = "仓位别名")
    private String storageLocationAlias;

    @Schema(description = "建委材料代码")
    private Integer constructionMaterialCode;

    @Schema(description = "材料名称")
    private String materialName;

    @Schema(description = "仓位类型")
    private String storageType;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @Schema(description = "理论值")
    private BigDecimal theoreticalValue;

    @Schema(description = "实际值")
    private BigDecimal actualValue;

    @Schema(description = "误差值")
    private BigDecimal errorValue;

    @Schema(description = "含水量")
    private BigDecimal moistureContent;

    @Schema(description = "是否更新单价")
    private Integer isUnitPriceUpdated;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "理论值2")
    private BigDecimal theoreticalValue2;

    @Schema(description = "实际值2")
    private BigDecimal actualValue2;

    @Schema(description = "启用状态")
    private Integer enabled;

}