package cn.iocoder.yudao.module.ciai.controller.admin.iminvbalance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存余额新增/修改 Request VO")
@Data
public class ImInvBalanceSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "物料ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    @Schema(description = "库房ID")
    private Long warehouseId;

    @Schema(description = "库位ID")
    private Long storageLocationId;

    @Schema(description = "条码")
    private String barcode;

    @Schema(description = "批号")
    private String batchNumber;

    @Schema(description = "序列号")
    private String sn;

    @Schema(description = "日期")
    private LocalDateTime date;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "单价", example = "28434")
    private BigDecimal unitPrice;

    @Schema(description = "金额")
    private BigDecimal amount;

}