package cn.iocoder.yudao.module.ciai.controller.admin.ljreceivableinitial.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "应收期初账款明细 Response VO，包含合同与单位名称等信息")
public class LjReceivableInitialDetailRespVO {

    @Schema(description = "主键ID", example = "1024")
    private Long id;

    @Schema(description = "工程名称", example = "滨海项目")
    private String projectName;

    @Schema(description = "合同名称", example = "2023年二期工程合同")
    private String contractName;

    @Schema(description = "施工单位名称", example = "某某建设集团")
    private String constructionUnitName;

    @Schema(description = "建设单位名称", example = "某某开发有限公司")
    private String buildUnitName;

    @Schema(description = "结算单位名称", example = "某某财务结算中心")
    private String settlementUnitName;

    @Schema(description = "内部合同编号", example = "INT-202308-001")
    private String internalContractNumber;

    @Schema(description = "工程地址", example = "上海市浦东新区")
    private String projectAddress;

    @Schema(description = "累计发生数量", example = "100.00")
    private BigDecimal accumulatedOccurrenceQuantity;

    @Schema(description = "累计发生金额", example = "10000.00")
    private BigDecimal accumulatedOccurrenceAmount;

    @Schema(description = "累计回款金额", example = "8000.00")
    private BigDecimal accumulatedCollectionAmount;

    @Schema(description = "累计发票金额", example = "5000.00")
    private BigDecimal accumulatedInvoiceAmount;

    @Schema(description = "累计认签数量", example = "90.00")
    private BigDecimal accumulatedRecognitionQuantity;

    @Schema(description = "累计认签金额", example = "9000.00")
    private BigDecimal accumulatedRecognitionAmount;

    @Schema(description = "期初日期", example = "2023-08-01 00:00:00")
    private Date initialDate;

    @Schema(description = "创建人", example = "张三")
    private String creator;

    @Schema(description = "创建时间", example = "2023-08-01 09:30:00")
    private Date createTime;

    @Schema(description = "修改人", example = "李四")
    private String updater;

    @Schema(description = "修改时间", example = "2023-08-05 16:45:00")
    private Date updateTime;
}