package cn.iocoder.yudao.module.ciai.dal.dataobject.carvehicledriver;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 车辆信息 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_car_vehicle_driver")
@KeySequence("ciai_car_vehicle_driver_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CarVehicleDriverDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 车辆ID
     */
    private Long vehicleId;
    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 司机姓名
     */
    private String driverName; // 司机姓名（可选）
}