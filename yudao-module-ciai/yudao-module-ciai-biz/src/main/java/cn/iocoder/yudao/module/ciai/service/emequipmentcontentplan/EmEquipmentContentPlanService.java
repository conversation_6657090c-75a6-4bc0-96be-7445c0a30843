package cn.iocoder.yudao.module.ciai.service.emequipmentcontentplan;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.emequipmentcontentplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentcontentplan.EmEquipmentContentPlanDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmentcontentplan.EmEquipmentContentPlanDetailDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 设备管理_内容计划 Service 接口
 *
 * <AUTHOR>
 */
public interface EmEquipmentContentPlanService {

    /**
     * 创建设备管理_内容计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmEquipmentContentPlan(@Valid EmEquipmentContentPlanSaveReqVO createReqVO);

    /**
     * 更新设备管理_内容计划
     *
     * @param updateReqVO 更新信息
     */
    void updateEmEquipmentContentPlan(@Valid EmEquipmentContentPlanSaveReqVO updateReqVO);

    /**
     * 删除设备管理_内容计划
     *
     * @param id 编号
     */
    void deleteEmEquipmentContentPlan(Long id);

    /**
     * 获得设备管理_内容计划
     *
     * @param id 编号
     * @return 设备管理_内容计划
     */
    EmEquipmentContentPlanDO getEmEquipmentContentPlan(Long id);

    /**
     * 获得设备管理_内容计划分页
     *
     * @param pageReqVO 分页查询
     * @return 设备管理_内容计划分页
     */
    PageResult<EmEquipmentContentPlanDO> getEmEquipmentContentPlanPage(EmEquipmentContentPlanPageReqVO pageReqVO);

    // ==================== 子表（设备管理_内容计划明细） ====================

    /**
     * 获得设备管理_内容计划明细分页
     *
     * @param pageReqVO 分页查询
     * @param contentPlanId 内容计划ID
     * @return 设备管理_内容计划明细分页
     */
    PageResult<EmEquipmentContentPlanDetailDO> getEmEquipmentContentPlanDetailPage(PageParam pageReqVO, Long contentPlanId);

    /**
     * 创建设备管理_内容计划明细
     *
     * @param emEquipmentContentPlanDetail 创建信息
     * @return 编号
     */
    Long createEmEquipmentContentPlanDetail(@Valid EmEquipmentContentPlanDetailDO emEquipmentContentPlanDetail);

    /**
     * 更新设备管理_内容计划明细
     *
     * @param emEquipmentContentPlanDetail 更新信息
     */
    void updateEmEquipmentContentPlanDetail(@Valid EmEquipmentContentPlanDetailDO emEquipmentContentPlanDetail);

    /**
     * 删除设备管理_内容计划明细
     *
     * @param id 编号
     */
    void deleteEmEquipmentContentPlanDetail(Long id);

	/**
	 * 获得设备管理_内容计划明细
	 *
	 * @param id 编号
     * @return 设备管理_内容计划明细
	 */
    EmEquipmentContentPlanDetailDO getEmEquipmentContentPlanDetail(Long id);

}