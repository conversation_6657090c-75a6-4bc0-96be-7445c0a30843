package cn.iocoder.yudao.module.ciai.controller.admin.pmtaskplan.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 生产管理_任务计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PmTaskPlanPageReqVO extends PageParam {

    @Schema(description = "任务类型", example = "2")
    private String taskType;

    @Schema(description = "计划开盘时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planOpenTime;

    @Schema(description = "任务状态", example = "1")
    private String taskStatus;

    @Schema(description = "审核状态", example = "2")
    private String auditStatus;

}