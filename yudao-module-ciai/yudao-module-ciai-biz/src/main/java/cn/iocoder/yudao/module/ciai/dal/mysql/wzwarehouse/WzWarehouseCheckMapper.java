package cn.iocoder.yudao.module.ciai.dal.mysql.wzwarehouse;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzwarehouse.WzWarehouseCheckDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 物资管理料仓管理盘点记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WzWarehouseCheckMapper extends BaseMapperX<WzWarehouseCheckDO> {

    default PageResult<WzWarehouseCheckDO> selectPage(PageParam reqVO, Long warehouseId) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WzWarehouseCheckDO>()
            .eq(WzWarehouseCheckDO::getWarehouseId, warehouseId)
            .orderByDesc(WzWarehouseCheckDO::getId));
    }

    default int deleteByWarehouseId(Long warehouseId) {
        return delete(WzWarehouseCheckDO::getWarehouseId, warehouseId);
    }

}
