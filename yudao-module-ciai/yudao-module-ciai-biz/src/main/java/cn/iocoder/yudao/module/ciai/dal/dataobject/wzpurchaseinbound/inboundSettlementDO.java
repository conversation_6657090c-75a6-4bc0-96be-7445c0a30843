package cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseinbound;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购入库结算 DO
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class inboundSettlementDO extends BaseDO {

    /**
     * 结算单号
     */
    private String settlementNumber;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 原材类型
     */
    private String materialType;
    /**
     * 种类
     */
    private String category;
    /**
     * 规格
     */
    private String specification;
    /**
     * 原材标识
     */
    private String materialIdentifier;
    /**
     * 折方系数
     */
    private Double conversionFactor;
    /**
     * 计量单位
     */
    private String measurementUnit;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 车次
     */
    private Integer tripCount;
    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;
    /**
     * 结算数量
     */
    private BigDecimal settlementQuantity;
    /**
     * 结算金额
     */
    private BigDecimal settlementAmount;
    /**
     * 结算车次
     */
    private Integer settlementTripCount;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 起始日期
     */
    private LocalDateTime startDate;
    /**
     * 结束日期
     */
    private LocalDateTime endDate;
    /**
     * 结算日期
     */
    private LocalDateTime settlementDate;
    /**
     * 制单人
     */
    private String preparer;
    /**
     * 制单时间
     */
    private LocalDateTime prepareTime;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 审核状态
     */
    private String reviewStatus;
    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;
    /**
     * 结算备注
     */
    private String settlementRemarks;
    /**
     * 是否有发票
     */
    private Integer hasInvoice;
    /**
     * 发票号
     */
    private String invoiceNumber;
    /**
     * 启用状态
     */
    private Integer enabled;

}