package cn.iocoder.yudao.module.ciai.service.pmtaskplan;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.ciai.controller.admin.pmtaskplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.pmtaskplan.PmTaskPlanDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 生产管理_任务计划 Service 接口
 *
 * <AUTHOR>
 */
public interface PmTaskPlanService {

    /**
     * 创建生产管理_任务计划
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPmTaskPlan(@Valid PmTaskPlanSaveReqVO createReqVO);

    /**
     * 更新生产管理_任务计划
     *
     * @param updateReqVO 更新信息
     */
    void updatePmTaskPlan(@Valid PmTaskPlanSaveReqVO updateReqVO);

    /**
     * 删除生产管理_任务计划
     *
     * @param id 编号
     */
    void deletePmTaskPlan(Long id);

    /**
     * 获得生产管理_任务计划
     *
     * @param id 编号
     * @return 生产管理_任务计划
     */
    PmTaskPlanDO getPmTaskPlan(Long id);

    /**
     * 获得生产管理_任务计划分页
     *
     * @param pageReqVO 分页查询
     * @return 生产管理_任务计划分页
     */
    PageResult<PmTaskPlanDO> getPmTaskPlanPage(PmTaskPlanPageReqVO pageReqVO);

    String getTaskNum();

    List<PmPartsVO>getPmParts(Long projectId);

    PmGradesRespVO getGradesCate(PmGradesReqVO pmGradesReqVO);

    /**
     * 获取未关联电子围栏的任务计划列表
     *
     * @return 任务计划列表
     */
    List<PmTaskPlanDO> getTaskPlansWithoutFence();

    /**
     * 获取任务计划的起点和终点位置
     *
     * @param id 任务ID
     * @return 位置信息
     */
    TaskPlanLocationRespVO getTaskPlanLocations(Long id);
}