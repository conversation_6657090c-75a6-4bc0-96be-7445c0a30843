package cn.iocoder.yudao.module.ciai.dal.mysql.jsmoisturesetting;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jsmoisturesetting.JsMoistureSettingDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.jsmoisturesetting.vo.*;

/**
 * 技术管理_含水设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface JsMoistureSettingMapper extends BaseMapperX<JsMoistureSettingDO> {

    default PageResult<JsMoistureSettingDO> selectPage(JsMoistureSettingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JsMoistureSettingDO>()
                .orderByAsc(JsMoistureSettingDO::getId));
    }

}