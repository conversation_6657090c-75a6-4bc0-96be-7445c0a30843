package cn.iocoder.yudao.module.ciai.dal.dataobject.pmcontract;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 营销管理销售合同 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_mm_sale_contract")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PmcontractDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 内部合同编号
     */
    private String internalContractNumber;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 运输距离
     */
    private Double transportDistance;

    @TableField(exist = false)
    private String projectName;

    @TableField(exist = false)
    private String companyName;

    @TableField(exist = false)
    private String projectAddress;

    @TableField(exist = false)
    private Long projectId;

}