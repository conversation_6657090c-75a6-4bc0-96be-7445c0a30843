package cn.iocoder.yudao.module.ciai.dal.dataobject.jssysandtest;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 砂试验 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_js_sy_sand_test")
@KeySequence("ciai_js_sy_sand_test_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JsSySandTestDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 单号
     */
    private String orderNo;
    /**
     * 试验编号
     */
    private String testNo;
    /**
     * 委托编号
     */
    private String entrustNo;
    /**
     * 报告编号
     */
    private String reportNo;
    /**
     * 试样编号
     */
    private String sampleNo;
    /**
     * 委托单位
     */
    private String entrustUnit;
    /**
     * 工程名称
     */
    private String projectName;
    /**
     * 种类编码
     */
    private String typeCode;
    /**
     * 种类
     */
    private String type;
    /**
     * 规格
     */
    private String specification;
    /**
     * 产地
     */
    private String origin;
    /**
     * 代表数量
     */
    private String representQuantity;
    /**
     * 收样日期
     */
    private LocalDateTime sampleDate;
    /**
     * 试验日期
     */
    private LocalDateTime testDate;
    /**
     * 截止日期
     */
    private LocalDateTime deadlineDate;
    /**
     * 细度模数
     */
    private BigDecimal finenessModulus;
    /**
     * 含泥量
     */
    private String mudContent;
    /**
     * 泥块含量
     */
    private String clayLumpContent;
    /**
     * 石粉含量
     */
    private BigDecimal stonePowderContent;
    /**
     * 结论
     */
    private String conclusion;
    /**
     * 级配区域
     */
    private String gradingArea;
    /**
     * 级配情况
     */
    private String gradingSituation;
    /**
     * 表观密度
     */
    private Integer apparentDensity;
    /**
     * 碱含量
     */
    private BigDecimal alkaliContent;
    /**
     * 氯离子
     */
    private BigDecimal chlorideIon;
    /**
     * 含水率
     */
    private String moistureContent;
    /**
     * 坚固性
     */
    private String soundness;
    /**
     * 轻物质含量
     */
    private String lightMaterialContent;
    /**
     * 云母含量
     */
    private String micaContent;
    /**
     * 堆积密度
     */
    private Integer bulkDensity;
    /**
     * 堆积密度空隙率
     */
    private Integer bulkDensityVoidRatio;
    /**
     * 紧密密度
     */
    private Integer compactDensity;
    /**
     * 紧密密度空隙率
     */
    private Integer compactDensityVoidRatio;
    /**
     * 碱活性指标
     */
    private String alkaliActivityIndex;
    /**
     * 其它
     */
    private String others;
    /**
     * 试验委托人
     */
    private String testClient;
    /**
     * 负责人
     */
    private String responsiblePerson;
    /**
     * 审核人
     */
    private String reviewer;
    /**
     * 试验人
     */
    private String tester;
    /**
     * 报告日期
     */
    private LocalDateTime reportDate;
    /**
     * ZHL
     */
    private String zhl;
    /**
     * SHAI11
     */
    private Double sieve11;
    /**
     * SHAI21
     */
    private Double sieve21;
    /**
     * SHAI31
     */
    private Double sieve31;
    /**
     * SHAI41
     */
    private Double sieve41;
    /**
     * SHAI51
     */
    private Double sieve51;
    /**
     * SHAI61
     */
    private Double sieve61;
    /**
     * SHAI71
     */
    private Double sieve71;
    /**
     * SHAI12
     */
    private Double sieve12;
    /**
     * SHAI22
     */
    private Double sieve22;
    /**
     * SHAI32
     */
    private Double sieve32;
    /**
     * SHAI42
     */
    private Double sieve42;
    /**
     * SHAI52
     */
    private Double sieve52;
    /**
     * SHAI62
     */
    private Double sieve62;
    /**
     * SHAI72
     */
    private Double sieve72;
    /**
     * SHAI13
     */
    private Double sieve13;
    /**
     * SHAI23
     */
    private Double sieve23;
    /**
     * SHAI33
     */
    private Double sieve33;
    /**
     * SHAI43
     */
    private Double sieve43;
    /**
     * SHAI53
     */
    private Double sieve53;
    /**
     * SHAI63
     */
    private Double sieve63;
    /**
     * SHAI73
     */
    private Double sieve73;
    /**
     * SHAIM1
     */
    private Double sieveM1;
    /**
     * SHAI14
     */
    private Double sieve14;
    /**
     * SHAI24
     */
    private Double sieve24;
    /**
     * SHAI34
     */
    private Double sieve34;
    /**
     * SHAI44
     */
    private Double sieve44;
    /**
     * SHAI54
     */
    private Double sieve54;
    /**
     * SHAI64
     */
    private Double sieve64;
    /**
     * SHAI74
     */
    private Double sieve74;
    /**
     * SHAI15
     */
    private Double sieve15;
    /**
     * SHAI25
     */
    private Double sieve25;
    /**
     * SHAI35
     */
    private Double sieve35;
    /**
     * SHAI45
     */
    private Double sieve45;
    /**
     * SHAI55
     */
    private Double sieve55;
    /**
     * SHAI65
     */
    private Double sieve65;
    /**
     * SHAI75
     */
    private Double sieve75;
    /**
     * SHAI16
     */
    private Double sieve16;
    /**
     * SHAI26
     */
    private Double sieve26;
    /**
     * SHAI36
     */
    private Double sieve36;
    /**
     * SHAI46
     */
    private Double sieve46;
    /**
     * SHAI56
     */
    private Double sieve56;
    /**
     * SHAI66
     */
    private Double sieve66;
    /**
     * SHAI76
     */
    private Double sieve76;
    /**
     * SHAIM2
     */
    private Double sieveM2;
    /**
     * SHAI17
     */
    private Double sieve17;
    /**
     * SHAI27
     */
    private Double sieve27;
    /**
     * SHAI37
     */
    private Double sieve37;
    /**
     * SHAI47
     */
    private Double sieve47;
    /**
     * SHAI57
     */
    private Double sieve57;
    /**
     * SHAI67
     */
    private Double sieve67;
    /**
     * SHAI77
     */
    private Double sieve77;
    /**
     * SHAIMV
     */
    private BigDecimal sieveMv;
    /**
     * HN11
     */
    private BigDecimal hn11;
    /**
     * HN12
     */
    private BigDecimal hn12;
    /**
     * HN13
     */
    private Double hn13;
    /**
     * HN21
     */
    private BigDecimal hn21;
    /**
     * HN22
     */
    private BigDecimal hn22;
    /**
     * HN23
     */
    private Double hn23;
    /**
     * HNV
     */
    private BigDecimal hnV;
    /**
     * NK11
     */
    private BigDecimal nk11;
    /**
     * NK12
     */
    private BigDecimal nk12;
    /**
     * NK13
     */
    private Double nk13;
    /**
     * NK21
     */
    private BigDecimal nk21;
    /**
     * NK22
     */
    private BigDecimal nk22;
    /**
     * NK23
     */
    private Double nk23;
    /**
     * NKV
     */
    private BigDecimal nkV;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 时间
     */
    private LocalDateTime modifyTime;
    /**
     * BG11
     */
    private Double bg11;
    /**
     * BG12
     */
    private Double bg12;
    /**
     * BG13
     */
    private Double bg13;
    /**
     * BG14
     */
    private Double bg14;
    /**
     * BG15
     */
    private Double bg15;
    /**
     * BGMD1
     */
    private Integer bgMd1;
    /**
     * BG21
     */
    private Double bg21;
    /**
     * BG22
     */
    private Double bg22;
    /**
     * BG23
     */
    private Double bg23;
    /**
     * BG24
     */
    private Double bg24;
    /**
     * BG25
     */
    private Double bg25;
    /**
     * BGMD2
     */
    private Integer bgMd2;
    /**
     * BGMDV
     */
    private Integer bgMdv;
    /**
     * DJ11
     */
    private Double dj11;
    /**
     * DJ12
     */
    private Double dj12;
    /**
     * DJ13
     */
    private Double dj13;
    /**
     * DJMD1
     */
    private Integer djMd1;
    /**
     * DJ21
     */
    private Double dj21;
    /**
     * DJ22
     */
    private Double dj22;
    /**
     * DJ23
     */
    private Double dj23;
    /**
     * DJMD2
     */
    private Integer djMd2;
    /**
     * DJMDV
     */
    private Integer djMdv;
    /**
     * JM11
     */
    private Double jm11;
    /**
     * JM12
     */
    private Double jm12;
    /**
     * JM13
     */
    private Double jm13;
    /**
     * JMMD1
     */
    private Integer jmMd1;
    /**
     * JM21
     */
    private Double jm21;
    /**
     * JM22
     */
    private Double jm22;
    /**
     * JM23
     */
    private Double jm23;
    /**
     * JMMD2
     */
    private Integer jmMd2;
    /**
     * JMMDV
     */
    private Integer jmMdv;
    /**
     * SF11
     */
    private Double sf11;
    /**
     * SF12
     */
    private Double sf12;
    /**
     * SF13
     */
    private Double sf13;
    /**
     * SF21
     */
    private Double sf21;
    /**
     * SF22
     */
    private Double sf22;
    /**
     * SF23
     */
    private Double sf23;
    /**
     * 石粉试样质量
     */
    private Integer stonePowderSampleMass;
    /**
     * 溶液质量
     */
    private Integer solutionMass;
    /**
     * MB值
     */
    private BigDecimal mbValue;
    /**
     * 压碎值
     */
    private BigDecimal crushingValue;
    /**
     * 执行标准
     */
    private String standard;
    /**
     * 试验状态
     */
    private String testStatus;
    /**
     * 使用状态
     */
    private String usageStatus;
    /**
     * 选择
     */
    private Integer selected;
    /**
     * 打印
     */
    private Integer printed;
    /**
     * Y
     */
    private Integer yValue;
    /**
     * 站别
     */
    private String stationCode;
    /**
     * 试验人2
     */
    private String tester2;
    /**
     * 执行标准2
     */
    private String standard2;
    /**
     * 工程类型
     */
    private String projectType;
    /**
     * 出厂编号
     */
    private String factoryNo;
    /**
     * 厂家牌号
     */
    private String factoryBrand;
    /**
     * YS11
     */
    private BigDecimal ys11;
    /**
     * YS12
     */
    private BigDecimal ys12;
    /**
     * YS13
     */
    private BigDecimal ys13;
    /**
     * YS21
     */
    private BigDecimal ys21;
    /**
     * YS22
     */
    private BigDecimal ys22;
    /**
     * YS23
     */
    private BigDecimal ys23;
    /**
     * YS31
     */
    private BigDecimal ys31;
    /**
     * YS32
     */
    private BigDecimal ys32;
    /**
     * YS33
     */
    private BigDecimal ys33;
    /**
     * YS41
     */
    private BigDecimal ys41;
    /**
     * YS42
     */
    private BigDecimal ys42;
    /**
     * YS43
     */
    private BigDecimal ys43;
    /**
     * YS51
     */
    private BigDecimal ys51;
    /**
     * YS52
     */
    private BigDecimal ys52;
    /**
     * YS53
     */
    private BigDecimal ys53;
    /**
     * YS61
     */
    private BigDecimal ys61;
    /**
     * YS62
     */
    private BigDecimal ys62;
    /**
     * YS63
     */
    private BigDecimal ys63;
    /**
     * YS71
     */
    private BigDecimal ys71;
    /**
     * YS72
     */
    private BigDecimal ys72;
    /**
     * YS73
     */
    private BigDecimal ys73;
    /**
     * YS81
     */
    private BigDecimal ys81;
    /**
     * YS82
     */
    private BigDecimal ys82;
    /**
     * YS83
     */
    private BigDecimal ys83;
    /**
     * YS91
     */
    private BigDecimal ys91;
    /**
     * YS92
     */
    private BigDecimal ys92;
    /**
     * YS93
     */
    private BigDecimal ys93;
    /**
     * YS101
     */
    private BigDecimal ys101;
    /**
     * YS102
     */
    private BigDecimal ys102;
    /**
     * YS103
     */
    private BigDecimal ys103;
    /**
     * YS111
     */
    private BigDecimal ys111;
    /**
     * YS112
     */
    private BigDecimal ys112;
    /**
     * YS113
     */
    private BigDecimal ys113;
    /**
     * YS121
     */
    private BigDecimal ys121;
    /**
     * YS122
     */
    private BigDecimal ys122;
    /**
     * YS123
     */
    private BigDecimal ys123;
    /**
     * YSPJ1
     */
    private BigDecimal ysPj1;
    /**
     * YSPJ2
     */
    private BigDecimal ysPj2;
    /**
     * YSPJ3
     */
    private BigDecimal ysPj3;
    /**
     * YSPJ4
     */
    private BigDecimal ysPj4;

}