package cn.iocoder.yudao.module.ciai.service.pmshiprecords;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.ciai.common.VehiclePerformanceCacheService;
import cn.iocoder.yudao.module.ciai.controller.admin.pmshiprecords.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.cardriver.CarDriverDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.carvehicle.CarVehicleDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.emproductionunit.EmProductionUnitDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gps.GpsDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.pmshiprecords.PmShipRecordsDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.pmtaskplan.PmTaskPlanDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.vehicleperformancestats.VehiclePerformanceStatsDO;
import cn.iocoder.yudao.module.ciai.dal.mysql.carvehicle.CarVehicleMapper;
import cn.iocoder.yudao.module.ciai.dal.mysql.pmshiprecords.PmShipRecordsMapper;
import cn.iocoder.yudao.module.ciai.dal.mysql.pmtaskplan.PmTaskPlanMapper;
import cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.ciai.gkjinterface.common.AsyncTaskExecutor;
import cn.iocoder.yudao.module.ciai.gkjinterface.service.VehicleSyncService;
import cn.iocoder.yudao.module.ciai.service.cardriver.CarDriverService;
import cn.iocoder.yudao.module.ciai.service.emproductionunit.EmProductionUnitService;
import cn.iocoder.yudao.module.ciai.service.gps.BaiduMapUtils;
import cn.iocoder.yudao.module.ciai.service.gps.GpsQueryService;
import cn.iocoder.yudao.module.infra.api.websocket.WebSocketSenderApi;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.PM_SHIP_RECORDS_NOT_EXISTS;

/**
 * 生产管理_发货记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PmShipRecordsServiceImpl implements PmShipRecordsService {

    @Resource
    private PmShipRecordsMapper pmShipRecordsMapper;

    @Resource
    private CarVehicleMapper carVehicleMapper;

    @Resource
    private PmTaskPlanMapper pmTaskPlanMapper;

    @Resource
    private EmProductionUnitService emProductionUnitService;

    @Resource
    private VehiclePerformanceCacheService performanceCacheService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private RedissonClient redisson;

    @Resource
    private WebSocketSenderApi webSocketSenderApi;

    @Resource
    private BaiduMapUtils baiduMapUtils;

    @Resource
    private VehicleSyncService vehicleSyncService;

    @Resource
    private AsyncTaskExecutor asyncTaskExecutor;

    @Resource
    private GpsQueryService gpsQueryService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private CarDriverService carDriverService;

    // 配置参数：最小允许发车的剩余方量
    private static final double MIN_DISPATCH_VOLUME = 10.0;
    // 押车时间上限
    private static final int MAX_PARKING_TIME_MINUTES_DEFAULT = 20;


    /**
     * 获取 Redis 锁
     */
    private RLock getTaskLock(Long taskId) {
        return redisson.getFairLock("lock:dispatch:task:" + taskId);
    }

    /**
     * 释放任务锁
     */
    private void releaseTaskLock(Long taskId) {
        String lockKey = "lock:dispatch:task:" + taskId;
        RLock lock = redisson.getFairLock(lockKey);
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
            log.info("【自动调度】任务 {} 的锁已释放", taskId);
        } else if (lock.isLocked()) {
            log.warn("【自动调度】任务 {} 的锁未被当前线程持有，可能已被其他节点释放", taskId);
        } else {
            log.info("【自动调度】任务 {} 的锁已经释放", taskId);
        }
    }

    /**
     * 自动调度主方法，批量处理所有活跃任务
     *
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public List<DispatchPlanRespVO> autoDispatch(DispatchRequestVO dispatchRequestVO) {
        // 1.获取所有活跃任务
        List<DispatchPlanRespVO> dispatchPlans = new ArrayList<>();
        // 查询审核通过、待开盘且自动调度的任务
        List<PmTaskPlanDO> activeTasks = pmTaskPlanMapper.selectActiveTasks();
        if (activeTasks.isEmpty()) {
            log.info("【自动调度】未找到符合条件的任务");
            return dispatchPlans;
        }

        for (PmTaskPlanDO task : activeTasks) {
            // Declare selectedProductionUnitId here to ensure it's in scope for logging if vehicle selection fails
            Long selectedProductionUnitId = null;
            try {
                // 1.获取锁
                RLock lock = getTaskLock(task.getId());
                if (!lock.tryLock()) {
                    log.warn("【并发控制】任务 {} 正在被其他线程处理", task.getTaskNumber());
                    continue;
                }
                try {
                    // 2.检查任务是否已完成
                    Double remainingVolume = task.getRemainingVolume();
                    if (remainingVolume != null && remainingVolume <= 0) {
                        log.warn("【自动调度】任务 {} 已完成，跳过", task.getTaskNumber());
                        continue;
                    }
                    // 3.判断是否允许发车
                    if (remainingVolume == null || remainingVolume < Optional.ofNullable(dispatchRequestVO.getMinDepartureVolume()).orElse(MIN_DISPATCH_VOLUME)) {
                        log.warn("【自动调度】任务 {} 剩余方量不足 ({})，无法发车 (最小发车方量: {})", task.getTaskNumber(), remainingVolume, Optional.ofNullable(dispatchRequestVO.getMinDepartureVolume()).orElse(MIN_DISPATCH_VOLUME));
                        continue;
                    }

                    // 3.5 获取可用生产机组
                    List<EmProductionUnitDO> availableUnits = emProductionUnitService.getAvailableProductionUnits();
                    if (availableUnits.isEmpty()) {
                        log.warn("【自动调度】任务 {} 无可用生产机组，跳过", task.getTaskNumber());
                        continue;
                    }
                    // TODO: 如果需要，实现更复杂的选择逻辑。目前，选择第一个可用的
                    EmProductionUnitDO selectedUnit = availableUnits.get(0);
                    selectedProductionUnitId = selectedUnit.getId(); // Assign for potential later use/logging

                    // 4.获取空闲、启用且有绑定司机的车辆
                    List<CarVehicleDO> availableVehicles = carVehicleMapper.selectAvailableVehicles();
                    if (availableVehicles.isEmpty()) {
                        log.warn("【自动调度】任务 {} (机组 {}) 无可用车辆", task.getTaskNumber(), selectedProductionUnitId);
                        continue;
                    }

                    // 5. 根据优先级评分公式选择最优车辆
                    // TODO 缺少车辆载重
                    Optional<CarVehicleDO> bestVehicleOpt = availableVehicles.stream()
                            .map(v -> {
                                double score = calculatePriorityScore(task, v, dispatchRequestVO);
                                return new AbstractMap.SimpleEntry<>(v, score);
                            })
                            .filter(e -> e.getValue() > 0)
                            .max(Map.Entry.comparingByValue())
                            .map(Map.Entry::getKey);

                    if (bestVehicleOpt.isPresent()) {
                        CarVehicleDO vehicle = bestVehicleOpt.get();
                        Long driverId = getDriverForSelectedVehicle(vehicle);
                        if (driverId == null) {
                            log.error("【调度失败】任务 {} (机组 {}) 未能为最优车辆 {} (ID: {}) 分配到司机，跳过此任务。",
                                    task.getTaskNumber(), selectedProductionUnitId, vehicle.getSelfNumber(), vehicle.getId());
                            continue;
                        }
                        // 6.创建发货记录
                        LocalDateTime now = LocalDateTime.now();
                        PmShipRecordsDO record = new PmShipRecordsDO();
                        record.setDeliveryNumber(getInvoice()); // 获取发货单号
                        record.setOrderTime(LocalDateTime.now()); // 发车时间
                        record.setTaskPlanId(task.getId()); // 任务计划 ID
                        record.setIssuer(SecurityFrameworkUtils.getLoginUserNickname()); // 发货人
                        record.setTicketPrintingTime(LocalDateTime.now()); // 打票时间
                        record.setStatus("未回单"); // 状态：未回单
                        record.setVehicleId(vehicle.getId()); // 车辆 ID
                        record.setDriverId(driverId); // 司机 ID
                        record.setTransportVolume(Math.min(Optional.ofNullable(vehicle.getRatedLoad()).orElse(0.0), remainingVolume));
                        record.setDepartureTime(now);
                        record.setProductionStatus(3); // 准备
                        record.setProductionUnitId(selectedProductionUnitId); // 设置机组ID
                        record.setEnabled(1);
                        // 插入发货记录
                        pmShipRecordsMapper.insert(record);

                        // 更新生产机组状态为运行中
                        // emProductionUnitService.markUnitAsRunning(selectedProductionUnitId, task.getId());
                        // log.info("【自动调度】任务 {} (机组 {}) 机组状态已更新为运行中", task.getTaskNumber(), selectedProductionUnitId);

                        // 更新车辆状态为忙碌
                        updateCar(vehicle.getId());
                        // 更新司机状态为忙碌
                        carVehicleMapper.updateToBusy(driverId);
                        // 7.更新任务计划表 - 累计方量
                        pmTaskPlanMapper.updateCumulativeVolume(task.getId(), record.getTransportVolume());
                        // 更新任务状态为执行中
                        int updatedRows = pmTaskPlanMapper.updateTaskStatus(task.getId(), "执行中");
                        if (updatedRows > 0) {
                            log.info("【自动调度】任务 {} (ID: {}) 状态已更新为执行中", task.getTaskNumber(), task.getId());
                        } else {
                            log.warn("【自动调度】任务 {} (ID: {}) 状态更新为执行中失败，可能任务不存在或已被修改", task.getTaskNumber(), task.getId());
                        }
                        // 清除该车辆的缓存，确保下次调度使用最新数据
                        performanceCacheService.deletePerformanceStats(vehicle.getSelfNumber());

                        // 调用车辆指令同步方法
                        // 假设自动调度的发货记录总是需要同步，或者 instructionStatus 默认为 0 或 null
                        // Integer autoInstructionStatus = 0; // 或者 null, 根据业务定义
                        // executeVehicleCommandSync(record.getId(), autoInstructionStatus);

                        // 8.构建返回结果
                        DispatchPlanRespVO planVO = new DispatchPlanRespVO();
                        planVO.setTaskNumber(task.getTaskNumber());
                        planVO.setVehicleNumber(vehicle.getVehicleNumber());
                        // TODO 获取司机姓名 planVO.setDriverName();
                        planVO.setEstimatedDeparture(now);
                        planVO.setPriorityScore(calculatePriorityScore(task, vehicle, dispatchRequestVO));
                        planVO.setStatus("已调度");
                        planVO.setProductionUnitId(selectedProductionUnitId); // Also add to response
                        dispatchPlans.add(planVO);
                        log.info("【自动调度】任务 {} (机组 {}) 成功分配给车辆 {}", task.getTaskNumber(), selectedProductionUnitId, vehicle.getVehicleNumber());
                    } else {
                        log.info("【自动调度】任务 {} (机组 {}) 无合适车辆", task.getTaskNumber(), selectedProductionUnitId);
                    }
                } finally {
                    releaseTaskLock(task.getId());
                }
            } catch (Exception e) {
                log.error("【自动调度】处理任务 {} (机组 {}) 时发生异常", task.getTaskNumber(), selectedProductionUnitId, e);
            }
        }
        return dispatchPlans;
    }

    /**
     * 获取车辆路线信息
     *
     * @param id 发货记录ID
     * @return
     */
    @Override
    public RouteInfoRespVO getRouteInfo(Long id) {
        // 1.查询发货记录
        PmShipRecordsDO shipRecord = pmShipRecordsMapper.selectById(id);
        if (shipRecord == null) {
            throw exception(ErrorCodeConstants.SHIP_RECORDS_NOT_EXISTS);
        }

        // 2.获取任务计划
        PmTaskPlanDO taskPlan = pmTaskPlanMapper.selectProName(shipRecord.getTaskPlanId());

        if (taskPlan == null) {
            throw exception(ErrorCodeConstants.TASK_PLAN_NOT_EXISTS);
        }

        // 3.获取车辆路线
        CarVehicleDO vehicle = carVehicleMapper.selectById(shipRecord.getVehicleId());
        if (vehicle == null) {
            throw exception(ErrorCodeConstants.CAR_VEHICLE_NOT_EXISTS);
        }

        // 4.获取司机信息
        CarDriverDO driver = carDriverService.getCarDriver(shipRecord.getDriverId());
        if (driver == null) {
            throw exception(ErrorCodeConstants.CAR_VEHICLE_NOT_EXISTS);
        }

        // 5.创建返回对象
        RouteInfoRespVO routeInfo = new RouteInfoRespVO();

        // 6.获取站点地址经纬度
        String stationAddress = taskPlan.getStationAddress();
        Map<String, BigDecimal> stationCoordinates = baiduMapUtils.getGeocode(stationAddress);
        if (stationCoordinates != null && !stationCoordinates.isEmpty()) {
            LocationPointVO origin = new LocationPointVO();
            origin.setLat(stationCoordinates.get("lat").doubleValue());
            origin.setLng(stationCoordinates.get("lng").doubleValue());
            origin.setName(stationAddress);
            routeInfo.setOrigin(origin);
        }

        // 7.获取工地经纬度
        String projectAddress = taskPlan.getProjectAddress();
        Map<String, BigDecimal> projectCoordinates = baiduMapUtils.getGeocode(projectAddress);
        if (projectCoordinates != null) {
            LocationPointVO destination = new LocationPointVO();
            destination.setLat(projectCoordinates.get("lat").doubleValue());
            destination.setLng(projectCoordinates.get("lng").doubleValue());
            destination.setName(projectAddress);
            routeInfo.setDestination(destination);
        }

        // 8.获取车辆当前的位置
//        GpsLocationVO currentGps = gpsQueryService.getLatestGpsInfoForVehicle(vehicle.getSelfNumber());
//        if (currentGps != null) {
//            LocationPointVO currentLocation = new LocationPointVO();
//            currentLocation.setLat(currentGps.getLat().doubleValue());
//            currentLocation.setLng(currentGps.getLng().doubleValue());
//            routeInfo.setCurrentLocation(currentLocation);
//            currentLocation.setName("当前位置");
//            routeInfo.setCurrentLocation(currentLocation);
//        }
        // 9.计算路线信息
        if (routeInfo.getOrigin() != null && routeInfo.getDestination() != null) {
            String origin = routeInfo.getOrigin().getLat() + "," + routeInfo.getOrigin().getLng();
            String destination = routeInfo.getDestination().getLat() + "," + routeInfo.getDestination().getLng();
            RouteResultVO route = baiduMapUtils.getDrivingDistanceAndTime(origin, destination);
            if (route != null) {
                routeInfo.setDistance(route.getDistance());
                routeInfo.setDuration(route.getDuration());
                // 10. 解析路线途经点
                // 这里需要根据百度地图API返回的路线点格式进行解析
                // 假设百度地图API返回的路线点已经在RouteResultVO中处理好了
                routeInfo.setPathPoints(route.getPathPoints());
            }
        }
        // 11.设置车辆信息
        VehicleInfoVO vehicleInfo = new VehicleInfoVO();
        vehicleInfo.setVehicleNumber(vehicle.getVehicleNumber());
        vehicleInfo.setSelfNumber(vehicle.getSelfNumber());
        vehicleInfo.setDriverName(driver.getDriverName());
        // vehicleInfo.setSpeed(currentGps != null ? currentGps.getSpeed() : 0.0);
        routeInfo.setVehicle(vehicleInfo);

        return routeInfo;
    }

    /**
     * 获取车辆运输路线车辆信息
     *
     * @param taskPlanId 任务计划ID
     * @return 车辆信息
     */
    @Override
    public RouteVehiclesVO getRouteVehicles(Long taskPlanId) {
        try {
            // 1. 根据任务计划ID查询发货记录
            List<PmShipRecordsDO> shipRecords = pmShipRecordsMapper.selectByTaskPlanId(taskPlanId);
            if (shipRecords.isEmpty()) {
                return RouteVehiclesVO.builder()
                        .outboundVehicles(new ArrayList<>())
                        .inboundVehicles(new ArrayList<>())
                        .build();
            }

            // 2. 获取车辆ID列表
            List<Long> vehicleIds = shipRecords.stream()
                    .map(PmShipRecordsDO::getVehicleId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (vehicleIds.isEmpty()) {
                return RouteVehiclesVO.builder()
                        .outboundVehicles(new ArrayList<>())
                        .inboundVehicles(new ArrayList<>())
                        .build();
            }

            // 3. 查询车辆信息
            List<CarVehicleDO> vehicles = carVehicleMapper.selectBatchIds(vehicleIds);

            // 4. 查询任务计划信息
            PmTaskPlanDO taskPlan = pmTaskPlanMapper.selectById(taskPlanId);

            // 5. 查询最新GPS位置信息
            Map<Integer, GpsDO> vehicleLocationMap = getLatestVehicleLocations(vehicleIds);

            // 6. 查询司机信息
            Map<Long, String> driverNameMap = getDriverNames(shipRecords);

            // 7. 构建车辆信息并分类
            List<RouteVehicleVO> outboundVehicles = new ArrayList<>();
            List<RouteVehicleVO> inboundVehicles = new ArrayList<>();

            for (CarVehicleDO vehicle : vehicles) {
                RouteVehicleVO vehicleVO = buildRouteVehicleVO(
                        vehicle,
                        shipRecords,
                        vehicleLocationMap.get(vehicle.getId()),
                        driverNameMap,
                        taskPlan
                );

                // 根据车辆状态判断去程/返程
                if (isOutbound(vehicle.getId(), shipRecords)) {
                    outboundVehicles.add(vehicleVO);
                } else {
                    inboundVehicles.add(vehicleVO);
                }
            }

            return RouteVehiclesVO.builder()
                    .outboundVehicles(outboundVehicles)
                    .inboundVehicles(inboundVehicles)
                    .build();

        } catch (Exception e) {
            log.error("获取运输路线车辆信息失败, taskPlanId: {}", taskPlanId, e);
            return RouteVehiclesVO.builder()
                    .outboundVehicles(new ArrayList<>())
                    .inboundVehicles(new ArrayList<>())
                    .build();
        }
    }

    /**
     * 计算车辆与任务之间的优先级评分
     * 评分越高表示越优先
     *
     * @param task    任务
     * @param vehicle 车辆
     * @return
     */
    private double calculatePriorityScore(PmTaskPlanDO task, CarVehicleDO vehicle, DispatchRequestVO dispatchRequestVO) {
        // 获取车辆和任务相关的统计数据
        String truckNumber = vehicle.getSelfNumber();
        // 获取车辆性能数据
        List<VehiclePerformanceStatsDO> statsList = performanceCacheService.getPerformanceStats(truckNumber);
        if (statsList == null || statsList.isEmpty()) {
            log.warn("【调度限制】车辆 {} 无历史调度数据", truckNumber);
            return 0.0;
        }

        // 查找与当前任务工地地址匹配的车辆性能统计数据
        Optional<VehiclePerformanceStatsDO> matchedStatsOpt = statsList.stream()
                .filter(s -> Objects.equals(s.getProjectAddress(), task.getProjectAddress()))
                .findFirst();

        // 如果没有匹配的工地数据，则使用最近一次数据
        VehiclePerformanceStatsDO stats = matchedStatsOpt.orElseGet(() -> statsList.get(0));

        // 获取车辆最新 GPS 数据
        GpsLocationVO currentGps = getLatestGpsInfo(vehicle);
        if (currentGps == null || currentGps.getLat() == null || currentGps.getLng() == null) {
            log.warn("【GPS】车辆 {} 无有效GPS数据", truckNumber);
            return 0.0; // 如果没有GPS数据，跳过该车
        }

        // 获取目标地址的经纬度
        String projectAddress = task.getProjectAddress();
        Map<String, BigDecimal> targetCoordinates = baiduMapUtils.getGeocode(projectAddress);
        if (targetCoordinates == null || targetCoordinates.isEmpty() || !targetCoordinates.containsKey("lat") || !targetCoordinates.containsKey("lng")) {
            log.warn("【地图服务】无法解析目标地址 {} 的坐标或坐标不完整", projectAddress);
            return 0.0;
        }

        // 构造 origin 和 destination - 百度驾车API需要 "lat,lng" 格式
        String origin = currentGps.getLat() + "," + currentGps.getLng(); //  获取车辆当前位置的经纬度
        String destination = targetCoordinates.get("lat") + "," + targetCoordinates.get("lng"); //  获取目标地址的经纬度

        // 计算路线信息（ETA 和距离）
        RouteResultVO route = baiduMapUtils.getDrivingDistanceAndTime(origin, destination);
        if (route == null) {
            log.warn("【地图服务】无法计算从 {} 到 {} 的路线信息", origin, destination);
            return 0.0;
        }
        // 计算 ETA（预计到达时间）
        long etaSeconds = route.getDuration();
        // long etaMinutes = etaSeconds / 60; // // 计算预计到达时间（分钟），用于后续日志记录或调试信息

        // 使用配置的最大押车时间（如果提供）或默认值
        int maxParkingTimeMinutes = Optional.ofNullable(dispatchRequestVO.getMaxOvertime()).orElse(MAX_PARKING_TIME_MINUTES_DEFAULT);
        long maxParkingTimeSeconds = (long) maxParkingTimeMinutes * 60;

        // 押车时间 = ETA (seconds) + 卸料时间 (需要转换为秒)
        // 假设 stats.getAvgUnloadingTime() 返回的是分钟，所以乘以60转为秒
        double avgUnloadingTimeMinutes = Optional.ofNullable(stats.getAvgUnloadingTime()).orElse(0.0);
        double unloadingTimeSeconds = avgUnloadingTimeMinutes * 60;
        double realParkingTimeSeconds = etaSeconds + unloadingTimeSeconds;

        if (realParkingTimeSeconds > maxParkingTimeSeconds) {
            log.warn("【调度限制】车辆 {} 实际押车时间超限（{}秒 > {}秒），跳过",
                    vehicle.getSelfNumber(), realParkingTimeSeconds, maxParkingTimeSeconds);
            return 0.0;
        }

        // =========== 各项得分计算 ===========
        // 1. 距离得分（位置匹配度）
        double locationScore = calculateLocationScore(vehicle, task, currentGps, targetCoordinates);

        // 2. 负载匹配度
        double loadScore;
        Double ratedLoad = vehicle.getRatedLoad();
        if (ratedLoad == null || ratedLoad <= 1e-5) { // Check for null or very small (effectively zero) ratedLoad
            loadScore = 0.0; // If vehicle has no rated load, it cannot match any quantity
            log.warn("【调度评分】车辆 {} 额定载重为null或接近零，负载匹配度计为0", vehicle.getSelfNumber());
        } else {
            loadScore = Optional.ofNullable(task.getPlannedQuantity()).orElse(0.0) / ratedLoad;
        }
        if (loadScore > 1.0) loadScore = 1.0; // Cap at 1.0
        if (loadScore < 0) loadScore = 0.0; // Ensure score is not negative

        // 3. 运输时间得分（等待时间）- 时间越短得分越高
        double transportTimeScore = 1.0 / (etaSeconds + 1e-5); // 使用 etaSeconds。如果 etaSeconds 为 0，则加上 1e-5 以避免除以零。

        // 4. 历史表现得分（基于卸料时间）- 时间越短得分越高
        // 确保卸载时间秒数不为零或负数，以避免出现极高分数或除以零的情况。
        double effectiveUnloadingTimeSeconds = (unloadingTimeSeconds <= 1e-5) ? 1e-5 : unloadingTimeSeconds;
        double historicalPerfScore = 1.0 / (effectiveUnloadingTimeSeconds + 1e-5); // 为提高稳健性，添加 1e-5

        // 权重配置
        // 获取调度请求中的权重配置，若未提供则使用默认权重配置
        DispatchRequestVO.WeightConfig weights = Optional.ofNullable(dispatchRequestVO.getWeightConfig()).orElse(new DispatchRequestVO.WeightConfig());

        // 从权重配置中获取各项评分的权重值，若未指定则使用默认值：
        double distanceWeight = Optional.ofNullable(weights.getDistanceWeight()).orElse(0.2);           // 距离得分权重，默认 0.2
        double waitingTimeWeight = Optional.ofNullable(weights.getWaitingTimeWeight()).orElse(0.2);     // 等待时间（运输时间）得分权重，默认 0.2
        double loadMatchingWeight = Optional.ofNullable(weights.getLoadMatchingWeight()).orElse(0.3);   // 载重匹配得分权重，默认 0.3
        double historicalPerformanceWeight = Optional.ofNullable(weights.getHistoricalPerformanceWeight()).orElse(0.15); // 历史表现得分权重，默认 0.15
        double idleWeightValue = Optional.ofNullable(weights.getIdleWeight()).orElse(0.05);             // 空闲状态得分权重，默认 0.05
        // 返回总得分
        double finalScore = locationScore * distanceWeight +
                transportTimeScore * waitingTimeWeight +
                loadScore * loadMatchingWeight +
                historicalPerfScore * historicalPerformanceWeight +
                idleWeightValue;

        // 3. 输出评分日志
        log.debug("【调度评分】车辆 {}: 地理位置得分={}, 运输时间得分={}, 负载匹配得分={}, 历史表现得分={}, 空闲状态得分={}, 总得分={}",
                truckNumber, locationScore, transportTimeScore, loadScore, historicalPerfScore, idleWeightValue, finalScore);

        // 4. 阈值过滤
        if (dispatchRequestVO.getPriorityThreshold() != null && finalScore < dispatchRequestVO.getPriorityThreshold()) {
            log.debug("【调度评分】车辆 {} 得分 {} 低于阈值 {}，过滤", vehicle.getSelfNumber(), finalScore, dispatchRequestVO.getPriorityThreshold());
            return 0.0;
        }
        return finalScore;
    }

    /**
     * 根据地理位置计算距离得分（简化为字符串匹配）
     *
     * @param vehicle               车辆信息
     * @param task                  任务
     * @param currentVehicleGps     当前车辆位置
     * @param targetTaskCoordinates 目标任务地址
     * @return
     */
    private double calculateLocationScore(CarVehicleDO vehicle, PmTaskPlanDO task, GpsLocationVO currentVehicleGps, Map<String, BigDecimal> targetTaskCoordinates) {
        String vehicleCode = vehicle.getSelfNumber();

        if (currentVehicleGps == null || currentVehicleGps.getLat() == null || currentVehicleGps.getLng() == null) {
            log.warn("【GPS】车辆 {} 无有效位置数据, 无法计算位置得分", vehicleCode);
            return 0.1;
        }

        if (targetTaskCoordinates == null || targetTaskCoordinates.isEmpty() || !targetTaskCoordinates.containsKey("lat") || !targetTaskCoordinates.containsKey("lng")) {
            log.warn("【地图服务】任务 {} 目标地址无法解析或坐标不完整, 无法计算位置得分", task.getTaskNumber());
            return 0.1;
        }

        BigDecimal vehicleLat = currentVehicleGps.getLat();
        BigDecimal vehicleLng = currentVehicleGps.getLng();
        BigDecimal targetLat = targetTaskCoordinates.get("lat");
        BigDecimal targetLng = targetTaskCoordinates.get("lng");

        double distance = baiduMapUtils.calculateDistance(vehicleLat, vehicleLng, targetLat, targetLng);

        // 简单的距离评分逻辑：距离越近，分数越高。
        // 假设50公里以外评分很低，1公里以内评分很高。
        if (distance < 0) distance = Double.MAX_VALUE; // Should not happen with Haversine, but as a safeguard.

        if (distance <= 1000) { // 1km以内
            return 1.0;
        } else if (distance <= 5000) { // 5km以内
            return 0.8;
        } else if (distance <= 10000) { // 10km以内
            return 0.6;
        } else if (distance <= 25000) { // 25km以内
            return 0.4;
        } else if (distance <= 50000) { // 50km以内
            return 0.2;
        }
        log.debug("【调度评分】车辆 {} 到任务 {} 的距离 {} 米，位置评分计为0.1", vehicleCode, task.getTaskNumber(), distance);
        return 0.1; // 超过50km，或者其他情况
    }


    /**
     * 获取指定车辆的绑定司机ID。
     * 调用此方法前，应确保传入的 vehicle 对象是空闲、启用且已绑定司机的。
     *
     * @param vehicle 保证是空闲、启用且已绑定司机的车辆对象
     * @return 绑定的司机ID，如果意外未找到则记录错误并可能返回null或抛出异常。
     */
    private Long getDriverForSelectedVehicle(CarVehicleDO vehicle) { // 方法名更改以反映其新职责
        if (vehicle == null || vehicle.getId() == null) {
            log.error("【获取司机】传入的车辆对象或车辆ID为空");
            // 根据业务需求决定是返回null还是抛出异常
            // throw new IllegalArgumentException("Vehicle or Vehicle ID cannot be null");
            return null;
        }

        Long driverId = carVehicleMapper.getBoundDriverIfVehicleIdle(vehicle.getId());

        if (driverId != null) {
            log.info("【获取司机】为车辆 {} (ID: {}) 成功获取到绑定司机 ID={}", vehicle.getSelfNumber(), vehicle.getId(), driverId);
            return driverId;
        } else {
            // 理论上不应该发生，因为 vehicle 是从预过滤列表 (selectAvailableVehiclesWithDrivers) 中选出的。
            // 这可能表示数据不一致、并发问题，或者 getBoundDriverIfVehicleIdle 的条件与预过滤SQL不完全匹配。
            log.error("【严重逻辑错误或数据不一致】为车辆 {} (ID: {}) 获取司机ID失败，尽管该车辆应已绑定司机。",
                    vehicle.getSelfNumber(), vehicle.getId());
            // 兜底：可以尝试调用 findFallbackDriver()，但这违背了"策略一"的严格性。
            // 或者，更符合策略一的做法是认为此次分配失败。
            // return findFallbackDriver(); // 如果仍需兜底

            // 更严格的做法：
            // throw exception(ErrorCodeConstants.EM_VEHICLE_DRIVER_NOT_EXISTS, "无法为已选车辆找到预期绑定的司机");
            log.warn("【获取司机】由于为已选车辆 {} 未能找到预期司机，将尝试最后的兜底方案。", vehicle.getSelfNumber());
            return findFallbackDriver(); // 保留了原有的兜底逻辑，但应关注为何会执行到这里
        }
    }

    /**
     * 最后兜底：查找任意一个可用的司机。
     * 仅在主要司机分配逻辑失败时作为最后手段。
     */
    private Long findFallbackDriver() {
        // SQL查询司机表，寻找一个 enabled = 1 且 is_idle = 0 (空闲) 的司机。
        // 注意：ciai_car_driver 表中 is_idle 的含义需要确认。
        // 如果 ciai_car_driver 表没有 is_idle 字段，或者含义不同，需要调整SQL。
        // 假设 ciai_car_driver.is_idle = 0 表示司机空闲。
        String sql = "SELECT id FROM ciai_car_driver WHERE enabled = 1 LIMIT 1"; // 简化：只找启用的，不一定强求空闲状态，除非司机表有明确的空闲标记
        // 如果司机表有 is_idle (0空闲, 1忙碌):
        // String sql = "SELECT id FROM ciai_car_driver WHERE enabled = 1 AND is_idle = 0 ORDER BY RAND() LIMIT 1";
        try {
            Long driverId = jdbcTemplate.queryForObject(sql, Long.class);
            if (driverId != null) {
                log.warn("【兜底司机】找到一个备用司机 ID={}", driverId);
                return driverId;
            } else {
                log.error("【兜底司机】随机查找司机失败，数据库中无符合条件的司机。");
                throw exception(ErrorCodeConstants.EM_VEHICLE_DRIVER_NOT_EXISTS);
            }
        } catch (EmptyResultDataAccessException e) {
            log.error("【兜底司机】随机查找司机失败，数据库中无符合条件的司机。", e);
            throw exception(ErrorCodeConstants.EM_VEHICLE_DRIVER_NOT_EXISTS);
        } catch (Exception e) {
            log.error("【兜底司机】查找备用司机时发生未知异常。", e);
            throw exception(ErrorCodeConstants.EM_VEHICLE_DRIVER_NOT_EXISTS); // 或者一个更通用的错误码
        }
    }

    /**
     * 检查首车提醒任务
     */
//    @Scheduled(fixedRate = 60_000) // 每分钟执行一次
    public void checkFirstVehicleReminder() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyMinutesLater = now.plusMinutes(30);

        List<PmTaskPlanDO> tasks = pmTaskPlanMapper.getTasksNeedFirstVehicleReminder(
                Timestamp.valueOf(now), Timestamp.valueOf(thirtyMinutesLater));

        for (PmTaskPlanDO task : tasks) {
            log.info("【首车提醒】任务 {} 即将开盘", task.getTaskNumber());
            // 获取可用车辆列表
            List<CarVehicleDO> availableVehicles = carVehicleMapper.selectAvailableVehicles();

            if (availableVehicles.isEmpty()) {
                log.warn("【自动调度】无可用车辆");
                continue;
            }

            // 计算每辆车的预计到达时间
            for (CarVehicleDO vehicle : availableVehicles) {
                List<VehiclePerformanceStatsDO> statsList = performanceCacheService.getPerformanceStats(vehicle.getSelfNumber());
                if (statsList == null || statsList.isEmpty()) {
                    continue;
                }

                Optional<VehiclePerformanceStatsDO> matchedStatsOpt = statsList.stream()
                        .filter(s -> Objects.equals(s.getProjectAddress(), task.getProjectAddress()))
                        .findFirst();

                VehiclePerformanceStatsDO stats = matchedStatsOpt.orElse(statsList.get(0));
                double avgTransportTimeMinutes = Optional.ofNullable(stats.getAvgTransportTime()).orElse(0.0);

                // 预计发车时间 = 开盘时间 - avgTransportTime
                LocalDateTime estimatedDeparture = task.getPlanOpenTime().minusMinutes((long) avgTransportTimeMinutes);
                LocalDateTime earliestDeparture = now.plusMinutes(5); // 至少提前5分钟发车

                if (!estimatedDeparture.isBefore(earliestDeparture)) {
                    log.debug("【首车提醒】车辆 {} 可在 {} 发车以准时送达工地",
                            vehicle.getSelfNumber(), estimatedDeparture);

                    sendFirstVehicleAlert(task, vehicle, estimatedDeparture);
                }
            }
        }
    }

    /**
     * 发送首车提醒给调度员（WebSocket + 日志记录）
     */
    private void sendFirstVehicleAlert(PmTaskPlanDO task, CarVehicleDO vehicle, LocalDateTime estimatedDeparture) {
        FirstVehicleAlertVO alert = new FirstVehicleAlertVO();
        alert.setTaskNumber(task.getTaskNumber());
        alert.setProjectAddress(task.getProjectAddress());
        alert.setPlanOpenTime(task.getPlanOpenTime());
        alert.setEstimatedDeparture(estimatedDeparture);
        alert.setVehicleId(vehicle.getId());
        alert.setVehicleNumber(vehicle.getVehicleNumber());

        List<VehiclePerformanceStatsDO> statsList = performanceCacheService.getPerformanceStats(vehicle.getSelfNumber());
        if (statsList != null && !statsList.isEmpty()) {
            Optional<VehiclePerformanceStatsDO> matchedStatsOpt = statsList.stream()
                    .filter(s -> Objects.equals(s.getProjectAddress(), task.getProjectAddress()))
                    .findFirst();
            VehiclePerformanceStatsDO currentStats = matchedStatsOpt.orElse(statsList.get(0));
            alert.setAvgTransportTime(Optional.ofNullable(currentStats.getAvgTransportTime()).orElse(0.0));
        } else {
            alert.setAvgTransportTime(0.0); // Default if no stats
        }

        // For alerts, we might use default weights or a simplified scoring if DispatchRequestVO is not available
        alert.setPriorityScore(calculatePriorityScore(task, vehicle, new DispatchRequestVO()));
        webSocketSenderApi.sendObject(1, "FIRST_VEHICLE_REMINDER", alert);
    }

    //    @Scheduled(fixedRate = 60_000)
    public void releaseDriversAfterCompletion() {
        // 1. 查询已完成的任务对应的司机
        List<Long> completedDriverIds = carVehicleMapper.selectCompletedDrivers();

        if (completedDriverIds == null || completedDriverIds.isEmpty()) {
            log.info("【司机状态】当前无可释放的司机");
            return;
        }

        // 2. 更新这些司机的状态为"空闲"
        for (Long driverId : completedDriverIds) {
            carVehicleMapper.updateToIdle(driverId);
            log.info("【司机状态】司机 ID={} 完成运输，设为空闲", driverId);
        }
    }


    public String getInvoice() {
        // 获取当天的日期，格式为 yyMMdd
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

        // 查询 create_time 为今天的数据
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        QueryWrapper<PmShipRecordsDO> queryWrapper = Wrappers.query();
        queryWrapper.likeRight("create_time", today);

        List<PmShipRecordsDO> records = pmShipRecordsMapper.selectList(queryWrapper);

        int maxNumber = 0;
        for (PmShipRecordsDO record : records) {
            String deliveryNumber = record.getDeliveryNumber();
            if (deliveryNumber != null && deliveryNumber.startsWith("YSD" + datePart) && deliveryNumber.length() >= 14) {
                // 取最后五位数字并尝试解析为整数
                String numberPart = deliveryNumber.substring(9); // "YSD" + 6位日期 = 9
                try {
                    int number = Integer.parseInt(numberPart);
                    if (number > maxNumber) {
                        maxNumber = number;
                    }
                } catch (NumberFormatException ignored) {
                }
            }
        }

        // 新的编号为 maxNumber + 1，左补零到五位数
        String newNumber = String.format("YSD%s%05d", datePart, maxNumber + 1);

        return newNumber;
    }

    private void executeVehicleCommandSync(Long shipRecordId, Integer instructionStatus) {
        // 假设 instructionStatus 为 null 或 0 时需要同步
        if (instructionStatus == 1) {
            if (vehicleSyncService != null && asyncTaskExecutor != null) {
                log.info("【指令同步】发货记录ID {}，准备执行车辆指令同步。", shipRecordId);
                asyncTaskExecutor.execute(
                        "车辆指令同步", // 任务名可以根据调用来源稍作区分
                        () -> vehicleSyncService.syncVehicleCommand(shipRecordId)
                );
            } else {
                log.warn("【指令同步】vehicleSyncService 或 asyncTaskExecutor 未注入，无法执行车辆指令同步。");
            }
        } else {
            log.info("【指令同步】发货记录ID {}，指令状态为 {}，无需执行车辆指令同步。", shipRecordId, instructionStatus);
        }
    }

    @Override
    @DSTransactional
    public Long createPmShipRecords(PmShipRecordsSaveReqVO createReqVO) {
        String currentYear = String.valueOf(LocalDate.now().getYear());
        String lockKey = "lock:task_no:generate:" + currentYear;
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 10, TimeUnit.SECONDS); // 10秒过期

        Long insertedId = null;
        if (Boolean.TRUE.equals(locked)) {
            try {
                // 加锁成功，安全生成唯一发货单号
                PmShipRecordsDO pmShipRecords = BeanUtils.toBean(createReqVO, PmShipRecordsDO.class);
                pmShipRecords.setDeliveryNumber(getInvoice()); // 会按日重置序号
                pmShipRecords.setOrderTime(LocalDateTime.now());
                pmShipRecords.setTicketPrintingTime(LocalDateTime.now());
                pmShipRecords.setDepartureTime(LocalDateTime.now());
                pmShipRecords.setIssuer(SecurityFrameworkUtils.getLoginUserNickname());

                int rows = pmShipRecordsMapper.insert(pmShipRecords);
                if (rows <= 0 || pmShipRecords.getId() == null) {
                    throw new RuntimeException("发货记录插入失败");
                }
                insertedId = pmShipRecords.getId();
            } finally {
                // 解锁
                stringRedisTemplate.delete(lockKey);
            }
        } else {
            // 获取锁失败，抛出异常或重试
            throw new RuntimeException("系统繁忙，请稍后再试");
        }
        // 新增时更新车辆状态
        updateCar(createReqVO.getVehicleId());
        // 如果计趟，更新任务计划和发货记录的累计车次和累计方量
        if (createReqVO.getIsNotCountedTrip() == null || createReqVO.getIsNotCountedTrip() == 0) {
            updatePmRecords(insertedId, createReqVO.getTaskPlanId());
        }

        Long intoId = createReqVO.getTransferDeliveryId();
        if (intoId != null) {
            // 1. 获取当前记录
            PmShipRecordsDO record = pmShipRecordsMapper.selectById(intoId);
            if (record != null) {
                // 2. 计算剩余量
                BigDecimal currentVolume = Optional.ofNullable(record.getRemainingMaterialVolume())
                        .map(BigDecimal::valueOf)
                        .orElse(BigDecimal.ZERO);
                BigDecimal transferVolume = Optional.ofNullable(createReqVO.getTransferVolume())
                        .map(BigDecimal::valueOf)
                        .orElse(BigDecimal.ZERO);
                BigDecimal newVolume = currentVolume.subtract(transferVolume);

                // 3. 更新字段
                PmShipRecordsDO update = new PmShipRecordsDO();

                update.setId(intoId);
                update.setRemainingMaterialVolume(newVolume.doubleValue());
                // 如果 newVolume 等于 0，则设置处理状态为"已处理"
                if (newVolume.compareTo(BigDecimal.ZERO) == 0) {
                    update.setHandlingStatus("已处理");
                }
                pmShipRecordsMapper.updateById(update);
            }
        }
        // if (createReqVO.getInstructionStatus() == null || createReqVO.getInstructionStatus() == 0) {
        //     asyncTaskExecutor.execute(
        //             "车辆指令同步",
        //             () -> vehicleSyncService.syncVehicleCommand(pmShipRecords.getId())
        //     );
        // }

        // 调用提取的车辆指令同步方法
        executeVehicleCommandSync(insertedId, createReqVO.getInstructionStatus());
        // 返回
        return insertedId;
    }

    /**
     * 清除车辆状态
     */
    public void updateCar(Long carId) {
        UpdateWrapper<CarVehicleDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", carId) // 设置更新条件，carId匹配
                .set("queue_order", 0)  // 设置queue_order为0
                .set("is_idle", 1)      // 设置is_idle为1
                .set("is_queued", 0);   // 设置is_queued为0


        carVehicleMapper.update(null, updateWrapper);
    }

    /**
     * 更新发货记录数据
     *
     * @param id
     * @param planId
     */
    public void updatePmRecords(Long id, Long planId) {
        // 查询满足条件的发货记录
        List<PmShipRecordsDO> records = pmShipRecordsMapper.selectList(
                new QueryWrapper<PmShipRecordsDO>()
                        .eq("task_plan_id", planId)
                        .le("id", id)
                        .eq("cancellation", 0)
                        .eq("is_not_counted_trip", 0)
        );

        // 计算累计车次与累计方量
        long totalTrips = records.size();
        double totalVolume = records.stream()
                .mapToDouble(r -> Optional.ofNullable(r.getTransportVolume()).orElse(0.0))
                .sum();

        // 更新当前发货单
        PmShipRecordsDO updatedShipRecord = new PmShipRecordsDO();
        updatedShipRecord.setAccumulatedTrips(totalTrips);
        updatedShipRecord.setAccumulatedVolume(totalVolume);
        pmShipRecordsMapper.update(updatedShipRecord,
                new QueryWrapper<PmShipRecordsDO>().eq("id", id)
        );

        // 更新对应任务单
        PmTaskPlanDO updatedTaskPlan = new PmTaskPlanDO();
        updatedTaskPlan.setTotalTruckNumber(totalTrips);
        updatedTaskPlan.setTotalSquareMeasure(totalVolume);
        pmTaskPlanMapper.update(updatedTaskPlan,
                new QueryWrapper<PmTaskPlanDO>().eq("id", planId)
        );
    }

    @Override
    @DSTransactional
    public void updatePmShipRecords(PmShipRecordsSaveReqVO updateReqVO) {
        // 校验存在
        validatePmShipRecordsExists(updateReqVO.getId());
        // 更新
        PmShipRecordsDO updateObj = BeanUtils.toBean(updateReqVO, PmShipRecordsDO.class);
        pmShipRecordsMapper.updateById(updateObj);
        if (updateReqVO.getInstructionStatus() == null || updateReqVO.getInstructionStatus() == 0) {
            asyncTaskExecutor.execute(
                    "车辆指令同步",
                    () -> vehicleSyncService.syncVehicleCommand(updateReqVO.getId())
            );
        }
    }

    @Override
    @Transactional
    public void deletePmShipRecords(Long id) {
        // 校验存在
        validatePmShipRecordsExists(id);
        PmShipRecordsDO toDelete = pmShipRecordsMapper.selectById(id);
        Long taskPlanId = toDelete.getTaskPlanId();
        Double transportVolume = Optional.ofNullable(toDelete.getTransportVolume()).orElse(0.0);

        // 3. 查询该记录之后的所有未删除记录（ID > 当前，未取消）
        List<PmShipRecordsDO> affectedList = pmShipRecordsMapper.selectList(
                new QueryWrapper<PmShipRecordsDO>()
                        .eq("task_plan_id", taskPlanId)
                        .gt("id", id)
                        .orderByAsc("id")
        );

        if (!affectedList.isEmpty()) {
            for (PmShipRecordsDO record : affectedList) {
                double currentCumulativeVolume = Optional.ofNullable(record.getAccumulatedVolume()).orElse(0.0);
                long currentCumulativeCount = Optional.ofNullable(record.getAccumulatedTrips()).orElse(0L);

                record.setAccumulatedVolume(currentCumulativeVolume - transportVolume);
                record.setAccumulatedTrips(currentCumulativeCount - 1);
                pmShipRecordsMapper.updateById(record);
            }
        }

        PmTaskPlanDO update = new PmTaskPlanDO();
        update.setId(taskPlanId);
        update.setTotalSquareMeasure(update.getTotalSquareMeasure() - transportVolume);
        update.setTotalTruckNumber(update.getTotalTruckNumber() - 1);
        pmTaskPlanMapper.updateById(update);
        // 删除
        pmShipRecordsMapper.deleteById(id);
    }

    private void validatePmShipRecordsExists(Long id) {
        if (pmShipRecordsMapper.selectById(id) == null) {
            throw exception(PM_SHIP_RECORDS_NOT_EXISTS);
        }
    }

    @Override
    public PmShipRecordsDO getPmShipRecords(Long id) {
        return pmShipRecordsMapper.selectByIds(id);
    }

    @Override
    public PageResult<PmShipRecordsDO> getPmShipRecordsPage(PmShipRecordsPageReqVO pageReqVO) {
        IPage<PmShipRecordsDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        pmShipRecordsMapper.selectPages(page, pageReqVO);
        // 分页封装
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<CarVehicleDO> getInfo(Long status) {

        QueryWrapper<CarVehicleDO> queryWrapper = new QueryWrapper<CarVehicleDO>()
                .select("id", "self_number", "rated_load")
                .eq("is_enabled", 1)
                .eq("is_idle", 0);

        if (status != null && status == 0) {
            queryWrapper.eq("is_queued", 0);
        } else if (status != null && status == 1) {
            queryWrapper.eq("is_queued", 1)
                    .orderByAsc("queue_order");
        }

        return carVehicleMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public void updateLineUp(Long id) {
        // 查询当前最大 queue_order
        QueryWrapper<CarVehicleDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(queue_order)");
        List<Object> result = carVehicleMapper.selectObjs(queryWrapper);

        Long maxOrder = (result != null && !result.isEmpty() && result.get(0) != null)
                ? ((Number) result.get(0)).longValue()
                : 0;

        // 构造更新实体
        CarVehicleDO update = new CarVehicleDO();
        update.setId(id);
        update.setQueueOrder(maxOrder + 1);
        update.setIsQueued(1);

        // 执行更新
        carVehicleMapper.updateById(update);
    }

    @Override
    @Transactional
    public void getFirst(Long id) {
        // 1. 所有已排队车辆 queueOrder + 1
        UpdateWrapper<CarVehicleDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .eq("is_queued", 1)
                .setSql("queue_order = queue_order + 1");
        carVehicleMapper.update(null, updateWrapper);

        // 2. 设置当前车辆为排队顺序 0，标记为已排队
        CarVehicleDO vehicle = new CarVehicleDO();
        vehicle.setId(id);
        vehicle.setIsQueued(1);
        vehicle.setQueueOrder(Long.valueOf(0));
        carVehicleMapper.updateById(vehicle);
    }

    @Override
    public void getEnd(Long id) {
        // 1. 获取当前最大排队顺序 (queueOrder)
        // 查询当前最大 queue_order
        QueryWrapper<CarVehicleDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(queue_order)");
        List<Object> result = carVehicleMapper.selectObjs(queryWrapper);

        Long maxOrder = (result != null && !result.isEmpty() && result.get(0) != null)
                ? ((Number) result.get(0)).longValue()
                : 0;

        // 2. 设置目标车辆排队顺序为最大值 + 1
        CarVehicleDO vehicle = new CarVehicleDO();
        vehicle.setId(id);
        vehicle.setIsQueued(1);  // 标记为已排队
        vehicle.setQueueOrder(maxOrder + 1);  // 排队顺序设置为最大值 + 1

        // 3. 更新车辆
        carVehicleMapper.updateById(vehicle);
    }

    @Override
    public void getCanel(Long id) {
        // 1. 取消目标车辆排队状态，设置 isQueued = 0 并清除队列顺序
        CarVehicleDO vehicle = new CarVehicleDO();
        vehicle.setId(id);
        vehicle.setIsQueued(0);  // 取消排队
        vehicle.setQueueOrder(null);  // 重置排队顺序
        carVehicleMapper.updateById(vehicle);
    }

    @Override
    public PmExpressVO selectExService() {
        // 获取当天的开始时间（00:00:00）
        LocalDateTime startTime = LocalDate.now().atStartOfDay();

        // 获取当前时间作为结束时间
        LocalDateTime endTime = LocalDateTime.now();

        // 转换为字符串（根据你数据库里时间格式选择合适的格式）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startStr = startTime.format(formatter);
        String endStr = endTime.format(formatter);
        return pmShipRecordsMapper.selectExMap(startStr, endStr);
    }

    @Override
    public List<CarVehicleDO> getInfos() {
        QueryWrapper<CarVehicleDO> queryWrapper = new QueryWrapper<CarVehicleDO>()
                .select("id", "self_number", "rated_load")
                .eq("is_enabled", 1)
                .eq("is_idle", 0)
                .orderByAsc("id");

        return carVehicleMapper.selectList(queryWrapper);
    }

    @Override
    public List<CarDriverDO> getDriverService(PmDriverRepVO pmDriverRepVO) {
        return pmShipRecordsMapper.getDriverMapping(pmDriverRepVO);
    }

    @Override
    public List<PmShipRecordsDO> getPmShipRecordsPages(Long id) {
        return pmShipRecordsMapper.getPmShipRecordsMapping(id);
    }

    @Override
    public List<PmUnitVO> getUnitService() {
        return pmShipRecordsMapper.pmUnitMapping();
    }

    @Override
    public List<PmRetuVO> getRetuService(Long id) {
        return pmShipRecordsMapper.retuMapping(id);
    }

    /**
     * 获取某辆车的最新 GPS 信息（经纬度 + 速度）
     */
    private GpsLocationVO getLatestGpsInfo(CarVehicleDO vehicle) {
        // 将调用委托给 GpsQueryService
        if (vehicle == null || vehicle.getSelfNumber() == null || vehicle.getSelfNumber().trim().isEmpty()) {
            log.warn("【GPS】无法获取车辆最新GPS信息：车辆信息或车牌号为空。");
            return null;
        }
        return gpsQueryService.getLatestGpsInfoForVehicle(vehicle.getSelfNumber());
    }

    /**
     * 获取车辆最新GPS位置信息
     */
    private Map<Integer, GpsDO> getLatestVehicleLocations(List<Long> vehicleIds) {
        List<GpsDO> gpsData = gpsQueryService.selectLatestLocationsByVehicleIds(vehicleIds);
        return gpsData.stream()
                .collect(Collectors.toMap(GpsDO::getVehicleId, gps -> gps, (existing, replacement) -> replacement));
    }

    /**
     * 获取司机姓名映射
     */
    private Map<Long, String> getDriverNames(List<PmShipRecordsDO> shipRecords) {
        List<Long> driverIds = shipRecords.stream()
                .map(PmShipRecordsDO::getDriverId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (driverIds.isEmpty()) {
            return new HashMap<>();
        }

        /**
         * 获取司机名称
         */
        List<CarDriverDO> drivers = carDriverService.selectBatchIds(driverIds);
        return drivers.stream()
                .collect(Collectors.toMap(CarDriverDO::getId, CarDriverDO::getDriverName));
    }

    /**
     * 判断车辆是否为去程
     */
    private boolean isOutbound(Long vehicleId, List<PmShipRecordsDO> shipRecords) {
        return shipRecords.stream()
                .filter(record -> vehicleId.equals(record.getVehicleId()))
                .anyMatch(record -> record.getDepartureTime() != null && record.getReturnTime() == null);
    }

    /**
     * 构建车辆VO对象
     */
    private RouteVehicleVO buildRouteVehicleVO(
            CarVehicleDO vehicle,
            List<PmShipRecordsDO> shipRecords,
            GpsDO gpsData,
            Map<Long, String> driverNameMap,
            PmTaskPlanDO taskPlan
    ) {
        // 查找该车辆的发货记录
        PmShipRecordsDO shipRecord = shipRecords.stream()
                .filter(record -> vehicle.getId().equals(record.getVehicleId()))
                .findFirst()
                .orElse(null);

        RouteVehicleVO.RouteVehicleVOBuilder builder = RouteVehicleVO.builder()
                .id(vehicle.getId())
                .selfNumber(vehicle.getSelfNumber())
                .vehicleNumber(vehicle.getVehicleNumber())
                .vehicleType(vehicle.getVehicleType())
                .pmShipRecordsId(shipRecord != null ? shipRecord.getId() : null) // 添加发货记录ID
                .ratedLoad(vehicle.getRatedLoad());

        // 设置GPS位置信息
        if (gpsData != null) {
            builder.location(gpsData.getAddress())
                    .speed(gpsData.getSpeed())
                    .lng(BigDecimal.valueOf(gpsData.getLng()))
                    .lat(BigDecimal.valueOf(gpsData.getLat()))
                    .gpsTime(gpsData.getGpsTime());
        }

        // 设置发货记录相关信息
        if (shipRecord != null) {
            builder.deliveryNumber(shipRecord.getDeliveryNumber())
                    .transportVolume(shipRecord.getTransportVolume())
                    .departureTime(shipRecord.getDepartureTime())
                    .returnTime(shipRecord.getReturnTime())
                    .driverName(driverNameMap.get(shipRecord.getDriverId()));

            // 设置状态
            if (shipRecord.getDepartureTime() != null && shipRecord.getReturnTime() == null) {
                builder.status("去程");
            } else if (shipRecord.getReturnTime() != null) {
                builder.status("返程");
            } else {
                builder.status("待发");
            }
        }

        // 设置工程地址
        if (taskPlan != null) {
            builder.projectAddress(taskPlan.getProjectAddress());
        }

        return builder.build();
    }

    // 更新视频审核
    @Override
    public void updateVideo(Long id, Integer video) {
        PmShipRecordsDO videoDO = new PmShipRecordsDO();
        videoDO.setId(id);
        videoDO.setVideoReview(video); // 设置新的审核状态
        pmShipRecordsMapper.updateById(videoDO);
    }

    // 更新加水审核
    @Override
    public void updateWater(Long id, Integer water) {
        PmShipRecordsDO waterDO = new PmShipRecordsDO();
        waterDO.setId(id);
        waterDO.setWaterReview(water); // 设置新的审核状态
        pmShipRecordsMapper.updateById(waterDO);
    }
}