package cn.iocoder.yudao.module.ciai.dal.dataobject.emequipmenttype;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 设备管理_设备类型 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_em_equipment_type")
@KeySequence("ciai_em_equipment_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmEquipmentTypeDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 设备类型名称
     */
    private String typeName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 启用状态 (1:启用 0:停用)
     */
    private Boolean enabled;

}