package cn.iocoder.yudao.module.ciai.controller.admin.wzwarehouse.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物资管理料仓管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WzWarehousePageReqVO extends PageParam {

    @Schema(description = "料仓名称", example = "芋艿")
    private String warehouseName;

    @Schema(description = "别名")
    private String alias;

    @Schema(description = "原材种类")
    private String materialType;

    @Schema(description = "原材种类ID")
    private String rawMaterialTypeId;

}