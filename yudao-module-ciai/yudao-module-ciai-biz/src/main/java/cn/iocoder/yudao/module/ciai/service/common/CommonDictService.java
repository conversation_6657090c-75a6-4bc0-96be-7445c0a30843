package cn.iocoder.yudao.module.ciai.service.common;

import cn.iocoder.yudao.module.ciai.dal.dataobject.common.OptionVO;

import java.util.List;

/**
 * 通用字典 Service 接口
 */
public interface CommonDictService {
    
    /**
     * 获取通用字典数据
     * 
     * @param valueColumn 值列名
     * @param labelColumn 标签列名
     * @param tableName 表名
     * @param whereCondition WHERE条件
     * @param orderBy 排序方式
     * @return 字典选项列表
     */
    List<OptionVO> getDictOptions(String valueColumn, String labelColumn, 
                                 String tableName, String whereCondition, String orderBy);

    List<OptionVO> selectListByDeptId(Long deptId);

    List<OptionVO> selectListByParentId(Long parentId);
} 