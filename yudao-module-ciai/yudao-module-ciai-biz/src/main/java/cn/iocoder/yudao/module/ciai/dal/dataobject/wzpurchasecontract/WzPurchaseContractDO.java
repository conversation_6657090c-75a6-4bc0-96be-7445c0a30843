package cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchasecontract;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 物资管理_采购合同 DO
 *
 * <AUTHOR>
 */
@TableName("ciai_wz_purchase_contract")
@KeySequence("ciai_wz_purchase_contract_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WzPurchaseContractDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 合同编号
     */
    private String contractNumber;
    /**
     * 内部合同编号
     */
    private String internalContractNumber;
    /**
     * 合同名称
     */
    private String contractName;
    /**
     * 合同类型
     */
    private String contractType;
    /**
     * 合同状态
     */
    private String contractStatus;
    /**
     * 供货人
     */
    private String supplier;
    /**
     * 签订单位ID
     */
    private Long signingUnitId;
    /**
     * 结算依据
     */
    private String settlementBasis;
    /**
     * 结算单位ID
     */
    private Long settlementUnitId;
    /**
     * 是否自运
     */
    private Boolean isSelfTransport;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 备注
     */
    private String remark;
    /**
     * 关联销售合同ID
     */
    private Long relatedSalesContractId;
    /**
     * 合同附件
     */
    private String contractAttachment;
    /**
     * 审核人
     */
    private String auditor;
    /**
     * 审核状态
     */
    private String auditStatus;
    /**
     * 审核日期
     */
    private LocalDateTime auditDate;
    /**
     * 所属站别ID
     */
    private Long stationId;
    /**
     * 分享站别ID
     */
    private String sharedStationId;
    /**
     * 作废
     */
    private Boolean cancellation;
    /**
     * 运输单位ID
     */
    private Long transportUnitId;
    /**
     * 运输联系人
     */
    private String transportContactPerson;
    /**
     * 运输联系电话
     */
    private String transportContactPhone;
    /**
     * 启用状态
     */
    private Boolean enabled;

}