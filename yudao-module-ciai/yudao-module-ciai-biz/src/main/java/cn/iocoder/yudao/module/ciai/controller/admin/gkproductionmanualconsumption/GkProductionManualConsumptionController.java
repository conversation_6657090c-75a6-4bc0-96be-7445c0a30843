package cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualconsumption;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.gkproductionmanualconsumption.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.gkproductionmanualconsumption.GkProductionManualConsumptionDO;
import cn.iocoder.yudao.module.ciai.service.gkproductionmanualconsumption.GkProductionManualConsumptionService;

@Tag(name = "管理后台 - 生产手动消耗")
@RestController
@RequestMapping("/ciai/gk-production-manual-consumption")
@Validated
public class GkProductionManualConsumptionController {

    @Resource
    private GkProductionManualConsumptionService gkProductionManualConsumptionService;

    @PostMapping("/create")
    @Operation(summary = "创建生产手动消耗")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:create')")
    public CommonResult<Long> createGkProductionManualConsumption(@Valid @RequestBody GkProductionManualConsumptionSaveReqVO createReqVO) {
        return success(gkProductionManualConsumptionService.createGkProductionManualConsumption(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新生产手动消耗")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:update')")
    public CommonResult<Boolean> updateGkProductionManualConsumption(@Valid @RequestBody GkProductionManualConsumptionSaveReqVO updateReqVO) {
        gkProductionManualConsumptionService.updateGkProductionManualConsumption(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除生产手动消耗")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:delete')")
    public CommonResult<Boolean> deleteGkProductionManualConsumption(@RequestParam("id") Long id) {
        gkProductionManualConsumptionService.deleteGkProductionManualConsumption(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得生产手动消耗")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:query')")
    public CommonResult<GkProductionManualConsumptionRespVO> getGkProductionManualConsumption(@RequestParam("id") Long id) {
        GkProductionManualConsumptionDO gkProductionManualConsumption = gkProductionManualConsumptionService.getGkProductionManualConsumption(id);
        return success(BeanUtils.toBean(gkProductionManualConsumption, GkProductionManualConsumptionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得生产手动消耗分页")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:query')")
    public CommonResult<PageResult<GkProductionManualConsumptionRespVO>> getGkProductionManualConsumptionPage(@Valid GkProductionManualConsumptionPageReqVO pageReqVO) {
        PageResult<GkProductionManualConsumptionDO> pageResult = gkProductionManualConsumptionService.getGkProductionManualConsumptionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GkProductionManualConsumptionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出生产手动消耗 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:gk-production-manual-consumption:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGkProductionManualConsumptionExcel(@Valid GkProductionManualConsumptionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GkProductionManualConsumptionDO> list = gkProductionManualConsumptionService.getGkProductionManualConsumptionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "生产手动消耗.xls", "数据", GkProductionManualConsumptionRespVO.class,
                        BeanUtils.toBean(list, GkProductionManualConsumptionRespVO.class));
    }

}