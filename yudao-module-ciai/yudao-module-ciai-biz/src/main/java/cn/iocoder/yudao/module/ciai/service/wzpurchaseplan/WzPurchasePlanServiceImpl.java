package cn.iocoder.yudao.module.ciai.service.wzpurchaseplan;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanJoinDO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.ciai.controller.admin.wzpurchaseplan.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.wzpurchaseplan.WzPurchasePlanDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.ciai.dal.mysql.wzpurchaseplan.WzPurchasePlanMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.ciai.enums.ErrorCodeConstants.*;

/**
 * 物资管理_采购计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WzPurchasePlanServiceImpl implements WzPurchasePlanService {

    @Resource
    private WzPurchasePlanMapper wzPurchasePlanMapper;

    @Override
    public Long createWzPurchasePlan(WzPurchasePlanSaveReqVO createReqVO) {
        createReqVO.setStatus("待审批");
        createReqVO.setApplicant(SecurityFrameworkUtils.getLoginUserNickname());
        // 插入
        WzPurchasePlanDO wzPurchasePlan = BeanUtils.toBean(createReqVO, WzPurchasePlanDO.class);
        wzPurchasePlanMapper.insert(wzPurchasePlan);
        // 返回
        return wzPurchasePlan.getId();
    }

    @Override
    public void updateWzPurchasePlan(WzPurchasePlanSaveReqVO updateReqVO) {
        // 校验存在
        validateWzPurchasePlanExists(updateReqVO.getId());
        // 更新
        WzPurchasePlanDO updateObj = BeanUtils.toBean(updateReqVO, WzPurchasePlanDO.class);
        wzPurchasePlanMapper.updateById(updateObj);
    }

    @Override
    public void updateWzPurchaseApprovalPlan(WzPurchasePlanUpdateApprovalReqVO updateReqVO) {
        // 校验存在
        validateWzPurchasePlanExists(updateReqVO.getId());
        //1 审批  2 撤销
        if(updateReqVO.getOperationType() == 1){
            if(wzPurchasePlanMapper.selectById(updateReqVO.getId()).getStatus() == "已批准")
                throw exception(WZ_PURCHASE_PLAN_Approved);
            updateReqVO.setApprover(SecurityFrameworkUtils.getLoginUserNickname());
            updateReqVO.setStatus("已批准");
        }else if (updateReqVO.getOperationType() == 2){
            updateReqVO.setApprover("");
            updateReqVO.setStatus("待审批");
        }
        // 更新
        WzPurchasePlanDO updateObj = BeanUtils.toBean(updateReqVO, WzPurchasePlanDO.class);
        wzPurchasePlanMapper.updateById(updateObj);
    }

    @Override
    public void deleteWzPurchasePlan(Long id) {
        // 校验存在
        validateWzPurchasePlanExists(id);
        WzPurchasePlanDO wzPurchasePlanDO = wzPurchasePlanMapper.selectById(id);
        if(wzPurchasePlanDO.getApprover() == "已批准"){
            throw exception(WZ_PURCHASE_PLAN_Approved);
        }else {
            // 删除
            wzPurchasePlanMapper.deleteById(id);
        }
    }

    private void validateWzPurchasePlanExists(Long id) {
        if (wzPurchasePlanMapper.selectById(id) == null) {
            throw exception(WZ_PURCHASE_PLAN_NOT_EXISTS);
        }
    }

    @Override
    public WzPurchasePlanDO getWzPurchasePlan(Long id) {
        return wzPurchasePlanMapper.selectById(id);
    }

    @Override
    public WzPurchasePlanDO getWzPurchasePlanJoin(Long id) {
        return wzPurchasePlanMapper.selectJoinById(id);
    }

    @Override
    public PageResult<WzPurchasePlanDO> getWzPurchasePlanPage(WzPurchasePlanPageReqVO pageReqVO) {
        return wzPurchasePlanMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<WzPurchasePlanJoinDO> getWzPurchasePlanJoinPage(WzPurchasePlanPageReqVO pageReqVO) {
        return wzPurchasePlanMapper.selectJoinPage(pageReqVO);
    }
}