package cn.iocoder.yudao.module.ciai.dal.mysql.equipmentdeprereport;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.equipmentdeprereport.equipmentdeprereportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.iocoder.yudao.module.ciai.controller.admin.equipmentdeprereport.vo.*;

/**
 * 设备折旧报表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface equipmentdeprereportMapper extends BaseMapperX<equipmentdeprereportDO> {

    default PageResult<equipmentdeprereportDO> selectPage(equipmentdeprereportPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<equipmentdeprereportDO>()
                .likeIfPresent(equipmentdeprereportDO::getEquipmentName, reqVO.getEquipmentName())
                .betweenIfPresent(equipmentdeprereportDO::getPurchaseTime, reqVO.getPurchaseTime())
                .orderByDesc(equipmentdeprereportDO::getId));
    }

    /**
     * 获取设备折旧报表数据
     *
     * @param reqVO 查询条件
     * @return 折旧报表数据列表
     */
    List<equipmentdeprereportRespVO> getEquipmentDepreReport(@Param("reqVO") equipmentdeprereportPageReqVO reqVO);
}