package cn.iocoder.yudao.module.ciai.dal.mysql.mmsalecontractproprice;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproject.MmSaleContractProjectDO;
import cn.iocoder.yudao.module.ciai.dal.dataobject.mmsalecontractproprice.MmSaleContractProPriceDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.ciai.controller.admin.mmsalecontractproprice.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


/**
 * 工程定价信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MmSaleContractProPriceMapper extends BaseMapperX<MmSaleContractProPriceDO> {

    default PageResult<MmSaleContractProPriceDO> selectPage(MmSaleContractProPricePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MmSaleContractProPriceDO>()
                .betweenIfPresent(MmSaleContractProPriceDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(MmSaleContractProPriceDO::getProductName, reqVO.getProductName())
                .eqIfPresent(MmSaleContractProPriceDO::getProductCategory, reqVO.getProductCategory())
                .eqIfPresent(MmSaleContractProPriceDO::getProductSpecification, reqVO.getProductSpecification())
                .eqIfPresent(MmSaleContractProPriceDO::getProductUnitPrice, reqVO.getProductUnitPrice())
                .eqIfPresent(MmSaleContractProPriceDO::getProjectId, reqVO.getProjectId())
                .orderByDesc(MmSaleContractProPriceDO::getId));
    }


    IPage<MmSaleContractProPriceDO> getProPrice(Page<?> page, @Param("id") Long id);

    // 在MmSaleContractProPriceMapper接口中添加以下方法

    /**
     * 根据工程ID和产品规格查询价格记录
     */
    MmSaleContractProPriceDO selectOneByProjectIdAndSpecification(@Param("projectId") Long projectId,
                                                                  @Param("productCategory") String productCategory,
                                                                  @Param("productName") String productName,
                                                                  @Param("specification") String specification);

    /**
     * 更新相关发货记录的价格
     */
    void updateShipRecordsPrices(@Param("projectId") Long projectId);
}