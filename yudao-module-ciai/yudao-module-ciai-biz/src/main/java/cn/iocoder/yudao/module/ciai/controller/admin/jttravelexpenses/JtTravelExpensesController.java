package cn.iocoder.yudao.module.ciai.controller.admin.jttravelexpenses;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.ciai.controller.admin.jttravelexpenses.vo.*;
import cn.iocoder.yudao.module.ciai.dal.dataobject.jttravelexpenses.JtTravelExpensesDO;
import cn.iocoder.yudao.module.ciai.service.jttravelexpenses.JtTravelExpensesService;

@Tag(name = "管理后台 - 交通费")
@RestController
@RequestMapping("/ciai/jt-travel-expenses")
@Validated
public class JtTravelExpensesController {

    @Resource
    private JtTravelExpensesService jtTravelExpensesService;

    @PostMapping("/create")
    @Operation(summary = "创建交通费")
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:create')")
    public CommonResult<Long> createJtTravelExpenses(@Valid @RequestBody JtTravelExpensesSaveReqVO createReqVO) {
        return success(jtTravelExpensesService.createJtTravelExpenses(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交通费")
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:update')")
    public CommonResult<Boolean> updateJtTravelExpenses(@Valid @RequestBody JtTravelExpensesSaveReqVO updateReqVO) {
        jtTravelExpensesService.updateJtTravelExpenses(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交通费")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:delete')")
    public CommonResult<Boolean> deleteJtTravelExpenses(@RequestParam("id") Long id) {
        jtTravelExpensesService.deleteJtTravelExpenses(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交通费")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:query')")
    public CommonResult<JtTravelExpensesRespVO> getJtTravelExpenses(@RequestParam("id") Integer id) {
        JtTravelExpensesDO jtTravelExpenses = jtTravelExpensesService.getJtTravelExpenses(id);
        return success(BeanUtils.toBean(jtTravelExpenses, JtTravelExpensesRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得交通费分页")
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:query')")
    public CommonResult<PageResult<JtTravelExpensesRespVO>> getJtTravelExpensesPage(@Valid JtTravelExpensesPageReqVO pageReqVO) {
        PageResult<JtTravelExpensesDO> pageResult = jtTravelExpensesService.getJtTravelExpensesPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, JtTravelExpensesRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出交通费 Excel")
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportJtTravelExpensesExcel(@Valid JtTravelExpensesPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<JtTravelExpensesDO> list = jtTravelExpensesService.getJtTravelExpensesPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "交通费.xls", "数据", JtTravelExpensesRespVO.class,
                        BeanUtils.toBean(list, JtTravelExpensesRespVO.class));
    }

    /**
     * 生成会计凭证
     * @param id
     * @return
     */
    @PostMapping("/generate-voucher")
    @Operation(summary = "生成会计凭证")
    @Parameter(name = "id", description = "交通费报销ID", required = true)
    @PreAuthorize("@ss.hasPermission('ciai:jt-travel-expenses:generate-voucher')")
    public CommonResult<Boolean> generateAccountingVoucher(@RequestParam("id") Integer id) {
        jtTravelExpensesService.generateAccountingVoucher(id);
        return success(true);
    }

}