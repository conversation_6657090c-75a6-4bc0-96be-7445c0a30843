package cn.iocoder.yudao.server.license.core;

import cn.iocoder.yudao.server.license.properties.LicenseProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Component
public class LicenseVerifier {

    @Resource
    private LicenseProperties properties;

    private LicenseInfo licenseInfo;
    private String uuidContent;

    @PostConstruct
    public void verifyLicense() {
        try {
            this.licenseInfo = doVerify();
            this.uuidContent = loadUuidContent();
        } catch (Exception e) {
            throw new RuntimeException("License 校验失败: " + e.getMessage(), e);
        }
    }

    public void refresh() {
        try {
            this.licenseInfo = doVerify();
            this.uuidContent = loadUuidContent();
        } catch (Exception e) {
            this.licenseInfo = null;
            this.uuidContent = null;
            throw new RuntimeException("License 刷新失败: " + e.getMessage(), e);
        }
    }

    private LicenseInfo doVerify() throws Exception {
        File file = new File(properties.getFile().getPath());
        if (!file.exists()) {
            throw new RuntimeException("License 文件不存在: " + properties.getFile().getPath());
        }

        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> license = mapper.readValue(file, Map.class);

        // 取出并移除签名字段
        String signature = (String) license.remove("signature");
        String data = mapper.writeValueAsString(license);

        boolean valid = RsaUtils.verify(
                data,
                signature,
                loadPublicKeyBytes(properties.getPublicKeyPath())
        );
        if (!valid) throw new RuntimeException("License 签名验证失败");

        LicenseInfo info = new LicenseInfo();
        info.setClientCode((String) license.get("clientCode"));
        info.setStartDate(LocalDate.parse((String) license.get("startDate")));
        info.setEndDate(LocalDate.parse((String) license.get("endDate")));
        info.setFeatures((List<String>) license.get("features"));
        info.setExpired(LocalDate.now().isAfter(info.getEndDate()));

        return info;
    }

    /**
     * 读取 uuid 文件内容
     */
    private String loadUuidContent() throws Exception {
        String uuidFilePath = properties.getUuidFilePath();
        if (uuidFilePath == null || uuidFilePath.isBlank()) {
            throw new RuntimeException("UUID 文件路径未配置");
        }

        InputStream is;
        if (uuidFilePath.startsWith("classpath:")) {
            String path = uuidFilePath.replace("classpath:", "");
            is = new ClassPathResource(path).getInputStream();
        } else {
            is = java.nio.file.Files.newInputStream(java.nio.file.Path.of(uuidFilePath));
        }

        try (is) {
            byte[] bytes = is.readAllBytes();
            String uuid = new String(bytes, StandardCharsets.UTF_8).trim();
            if (uuid.isEmpty()) {
                throw new RuntimeException("UUID 文件为空");
            }
            return uuid;
        }
    }

    public LicenseInfo getLicenseInfo() {
        return licenseInfo;
    }

    public String getUuidContent() {
        return uuidContent;
    }

    private byte[] loadPublicKeyBytes(String publicKeyPath) throws Exception {
        if (publicKeyPath.startsWith("classpath:")) {
            String path = publicKeyPath.replace("classpath:", "");
            try (InputStream is = new ClassPathResource(path).getInputStream()) {
                return is.readAllBytes();
            }
        } else {
            return java.nio.file.Files.readAllBytes(java.nio.file.Path.of(publicKeyPath));
        }
    }
}
