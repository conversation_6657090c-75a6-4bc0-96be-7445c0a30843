package cn.iocoder.yudao.module.infra.service.print;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import cn.iocoder.yudao.module.infra.controller.admin.print.vo.*;
import cn.iocoder.yudao.module.infra.dal.dataobject.print.PrintDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.infra.dal.mysql.print.PrintMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.*;

/**
 * 打印模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PrintServiceImpl implements PrintService {

    @Resource
    private PrintMapper printMapper;

    @Override
    public Long createPrint(PrintSaveReqVO createReqVO) {
        PrintDO printDO = printMapper.selectByName(createReqVO.getName());
        if (printDO != null) {
            throw exception(PRINT_NAME_EXISTS);
        }
        // 插入
        PrintDO print = BeanUtils.toBean(createReqVO, PrintDO.class);
        printMapper.insert(print);
        // 返回
        return print.getId();
    }

    @Override
    public void updatePrint(PrintSaveReqVO updateReqVO) {
        // 校验存在
        validatePrintExists(updateReqVO.getId());
        PrintDO printDO = printMapper.selectByName(updateReqVO.getName());
        if (printDO != null && printDO.getId() != updateReqVO.getId()) {
            throw exception(PRINT_NAME_EXISTS);
        }
        // 更新
        PrintDO updateObj = BeanUtils.toBean(updateReqVO, PrintDO.class);
        printMapper.updateById(updateObj);
    }

    @Override
    public void deletePrint(Long id) {
        // 校验存在
        validatePrintExists(id);
        // 删除
        printMapper.deleteById(id);
    }

    private void validatePrintExists(Long id) {
        if (printMapper.selectById(id) == null) {
            throw exception(PRINT_NOT_EXISTS);
        }
    }

    @Override
    public PrintDO getPrint(Long id) {
        return printMapper.selectById(id);
    }

    @Override
    public PrintDO getPrintByName(String name) {
        return printMapper.selectByName(name);
    }

    @Override
    public PageResult<PrintDO> getPrintPage(PrintPageReqVO pageReqVO) {
        return printMapper.selectPage(pageReqVO);
    }

}