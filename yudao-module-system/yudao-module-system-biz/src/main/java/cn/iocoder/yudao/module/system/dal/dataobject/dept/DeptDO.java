package cn.iocoder.yudao.module.system.dal.dataobject.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("system_dept")
@KeySequence("system_dept_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class DeptDO extends TenantBaseDO {

    public static final Long PARENT_ID_ROOT = 0L;

    /**
     * 部门ID
     */
    @TableId
    private Long id;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 父部门ID
     *
     * 关联 {@link #id}
     */
    private Long parentId;
    /**
     * 显示顺序
     */
    private Integer sort;
    /**
     * 负责人
     *
     * 关联 {@link AdminUserDO#getId()}
     */
    private Long leaderUserId;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 部门状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 类型（0 = 公司，1 = 部门）
     */
    private Integer type;
    /**
     * 简称
     */
    private String abbreviation;
    /**
     * 地址
     */
    private String address;
    /**
     * 社会信用代码
     */
    private String socialCreditCode;
    /**
     * 产值入账方式
     */
    private String revenueAccountingMethod;
    /**
     * 成本费用入账方式
     */
    private String costAccountingMethod;
    /**
     * 增值税计税方式
     */
    private String vatTaxationMethod;
    /**
     * 法律主体
     */
    private String legalEntity;
    /**
     * 收入确认原则
     */
    private String revenueRecognitionPrinciple;
    /**
     * 材料成本结转
     */
    private String materialCostTransferMethod;
    /**
     * 满勤天数计算方法
     */
    private String fullAttendanceDaysCalculationMethod;
    /**
     * 管理主体
     */
    private String managementEntity;
    /**
     * 对接税收系统（0 = 未对接，1 = 已对接）
     */
    private Boolean taxSystemIntegration;
    /**
     * 对接银行系统（0 = 未对接，1 = 已对接）
     */
    private Boolean bankSystemIntegration;

}
