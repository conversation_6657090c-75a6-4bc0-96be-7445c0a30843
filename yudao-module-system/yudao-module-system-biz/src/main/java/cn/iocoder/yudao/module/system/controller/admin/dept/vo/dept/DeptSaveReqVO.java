package cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Schema(description = "管理后台 - 部门创建/修改 Request VO")
@Data
public class DeptSaveReqVO {

    @Schema(description = "部门编号", example = "1024")
    private Long id;

    @Schema(description = "部门名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 30, message = "部门名称长度不能超过 30 个字符")
    private String name;

    @Schema(description = "父部门 ID", example = "1024")
    private Long parentId;

    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

    @Schema(description = "负责人的用户编号", example = "2048")
    private Long leaderUserId;

    @Schema(description = "联系电话", example = "15601691000")
    @Size(max = 11, message = "联系电话长度不能超过11个字符")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过 50 个字符")
    private String email;

    @Schema(description = "状态,见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    @InEnum(value = CommonStatusEnum.class, message = "修改状态必须是 {value}")
    private Integer status;

    @Schema(description = "类型（0 = 公司，1 = 部门）", example = "0")
    private Integer type;

    @Schema(description = "简称", example = "芋道科技")
    @Size(max = 30, message = "简称长度不能超过 30 个字符")
    private String abbreviation;

    @Schema(description = "地址", example = "浙江省杭州市西湖区")
    @Size(max = 255, message = "地址长度不能超过 255 个字符")
    private String address;

    @Schema(description = "社会信用代码", example = "91330100XXXXXXXXXX")
    @Size(max = 18, message = "社会信用代码长度不能超过 18 个字符")
    private String socialCreditCode;

    @Schema(description = "产值入账方式", example = "按项目")
    @Size(max = 50, message = "产值入账方式长度不能超过 50 个字符")
    private String revenueAccountingMethod;

    @Schema(description = "成本费用入账方式", example = "按部门")
    @Size(max = 50, message = "成本费用入账方式长度不能超过 50 个字符")
    private String costAccountingMethod;

    @Schema(description = "增值税计税方式", example = "一般计税")
    @Size(max = 50, message = "增值税计税方式长度不能超过 50 个字符")
    private String vatTaxationMethod;

    @Schema(description = "法律主体", example = "杭州芋道科技有限公司")
    @Size(max = 100, message = "法律主体长度不能超过 100 个字符")
    private String legalEntity;

    @Schema(description = "收入确认原则", example = "完工百分比法")
    @Size(max = 100, message = "收入确认原则长度不能超过 100 个字符")
    private String revenueRecognitionPrinciple;

    @Schema(description = "材料成本结转", example = "加权平均法")
    @Size(max = 50, message = "材料成本结转长度不能超过 50 个字符")
    private String materialCostTransferMethod;

    @Schema(description = "满勤天数计算方法", example = "自然月")
    @Size(max = 50, message = "满勤天数计算方法长度不能超过 50 个字符")
    private String fullAttendanceDaysCalculationMethod;

    @Schema(description = "管理主体", example = "杭州芋道科技有限公司")
    @Size(max = 100, message = "管理主体长度不能超过 100 个字符")
    private String managementEntity;

    @Schema(description = "对接税收系统（0 = 未对接，1 = 已对接）", example = "0")
    private Boolean taxSystemIntegration;

    @Schema(description = "对接银行系统（0 = 未对接，1 = 已对接）", example = "0")
    private Boolean bankSystemIntegration;

}
